/**
 * 虚拟化身场景相关的视觉脚本节点
 * 
 * 提供虚拟化身场景加载、切换、管理等功能的节点实现
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeSlot, SlotType } from '../NodeSlot';
import { AvatarSceneLoader, SceneLoadConfig, SceneLoadResult } from '../../avatar/AvatarSceneLoader';
import { AvatarData } from '../../avatar/AvatarCustomizationSystem';
import { Vector3 } from '../../math/Vector3';

/**
 * 一键进入虚拟场景节点
 */
export class QuickEnterSceneNode extends VisualScriptNode {
  constructor() {
    super('QuickEnterScene', '一键进入虚拟场景', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));
    this.addInputSlot(new NodeSlot('sceneId', '场景ID', SlotType.String, 'default'));
    this.addInputSlot(new NodeSlot('spawnX', '生成位置X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('spawnY', '生成位置Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('spawnZ', '生成位置Z', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('initialAnimation', '初始动画', SlotType.String, ''));
    this.addInputSlot(new NodeSlot('enablePhysics', '启用物理', SlotType.Boolean, true));
    this.addInputSlot(new NodeSlot('enableAI', '启用AI', SlotType.Boolean, false));
    this.addInputSlot(new NodeSlot('enableVoice', '启用语音', SlotType.Boolean, false));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '加载成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('sceneEntity', '场景实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('avatarEntity', '虚拟化身实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('loadTime', '加载时间', SlotType.Number));
    this.addOutputSlot(new NodeSlot('loadResult', '加载结果', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const avatarData = this.getInputValue('avatarData') as AvatarData;
    const sceneId = this.getInputValue('sceneId') as string;
    const spawnX = this.getInputValue('spawnX') as number;
    const spawnY = this.getInputValue('spawnY') as number;
    const spawnZ = this.getInputValue('spawnZ') as number;
    const initialAnimation = this.getInputValue('initialAnimation') as string;
    const enablePhysics = this.getInputValue('enablePhysics') as boolean;
    const enableAI = this.getInputValue('enableAI') as boolean;
    const enableVoice = this.getInputValue('enableVoice') as boolean;

    if (!avatarData) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身数据不能为空');
    }

    if (!sceneId) {
      this.triggerOutput('onError');
      throw new Error('场景ID不能为空');
    }

    try {
      // 获取场景加载系统
      const sceneLoader = this.context?.world?.getSystem('AvatarSceneLoader') as AvatarSceneLoader;
      if (!sceneLoader) {
        throw new Error('虚拟化身场景加载系统未找到');
      }

      // 构建加载配置
      const loadConfig: SceneLoadConfig = {
        sceneId,
        avatarData,
        spawnPosition: new Vector3(spawnX, spawnY, spawnZ),
        initialAnimation: initialAnimation || undefined,
        autoStart: true,
        loadOptions: {
          enablePhysics,
          enableAI,
          enableVoice,
          preloadAssets: true
        }
      };

      // 执行加载
      const result = await sceneLoader.loadAvatarToScene(loadConfig);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('sceneEntity', result.sceneEntity);
      this.setOutputValue('avatarEntity', result.avatarEntity);
      this.setOutputValue('loadTime', result.loadTime);
      this.setOutputValue('loadResult', result);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return result;
    } catch (error) {
      console.error('一键进入虚拟场景失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取可用场景列表节点
 */
export class GetAvailableScenesNode extends VisualScriptNode {
  constructor() {
    super('GetAvailableScenes', '获取可用场景列表', '虚拟化身');

    // 输出插槽
    this.addOutputSlot(new NodeSlot('scenes', '场景列表', SlotType.Array));
    this.addOutputSlot(new NodeSlot('count', '场景数量', SlotType.Number));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    try {
      // 获取场景加载系统
      const sceneLoader = this.context?.world?.getSystem('AvatarSceneLoader') as AvatarSceneLoader;
      if (!sceneLoader) {
        throw new Error('虚拟化身场景加载系统未找到');
      }

      // 获取可用场景
      const scenes = sceneLoader.getAvailableScenes();

      // 设置输出值
      this.setOutputValue('scenes', scenes);
      this.setOutputValue('count', scenes.length);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return scenes;
    } catch (error) {
      console.error('获取可用场景列表失败:', error);
      throw error;
    }
  }
}

/**
 * 切换场景节点
 */
export class SwitchSceneNode extends VisualScriptNode {
  constructor() {
    super('SwitchScene', '切换场景', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('fromSceneId', '当前场景ID', SlotType.String));
    this.addInputSlot(new NodeSlot('toSceneId', '目标场景ID', SlotType.String));
    this.addInputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '切换成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('newSceneEntity', '新场景实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('newAvatarEntity', '新虚拟化身实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('switchResult', '切换结果', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const fromSceneId = this.getInputValue('fromSceneId') as string;
    const toSceneId = this.getInputValue('toSceneId') as string;
    const avatarData = this.getInputValue('avatarData') as AvatarData;

    if (!fromSceneId || !toSceneId) {
      this.triggerOutput('onError');
      throw new Error('场景ID不能为空');
    }

    if (!avatarData) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身数据不能为空');
    }

    try {
      // 获取场景加载系统
      const sceneLoader = this.context?.world?.getSystem('AvatarSceneLoader') as AvatarSceneLoader;
      if (!sceneLoader) {
        throw new Error('虚拟化身场景加载系统未找到');
      }

      // 执行场景切换
      const result = await sceneLoader.switchScene(fromSceneId, toSceneId, avatarData);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('newSceneEntity', result.sceneEntity);
      this.setOutputValue('newAvatarEntity', result.avatarEntity);
      this.setOutputValue('switchResult', result);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return result;
    } catch (error) {
      console.error('切换场景失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 卸载场景节点
 */
export class UnloadSceneNode extends VisualScriptNode {
  constructor() {
    super('UnloadScene', '卸载场景', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('sceneId', '场景ID', SlotType.String));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '卸载成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const sceneId = this.getInputValue('sceneId') as string;

    if (!sceneId) {
      this.triggerOutput('onError');
      throw new Error('场景ID不能为空');
    }

    try {
      // 获取场景加载系统
      const sceneLoader = this.context?.world?.getSystem('AvatarSceneLoader') as AvatarSceneLoader;
      if (!sceneLoader) {
        throw new Error('虚拟化身场景加载系统未找到');
      }

      // 执行卸载
      const success = await sceneLoader.unloadScene(sceneId);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('卸载场景失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 移动虚拟化身节点
 */
export class MoveAvatarNode extends VisualScriptNode {
  constructor() {
    super('MoveAvatar', '移动虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('sceneId', '场景ID', SlotType.String));
    this.addInputSlot(new NodeSlot('positionX', '位置X', SlotType.Number));
    this.addInputSlot(new NodeSlot('positionY', '位置Y', SlotType.Number));
    this.addInputSlot(new NodeSlot('positionZ', '位置Z', SlotType.Number));
    this.addInputSlot(new NodeSlot('rotationX', '旋转X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationY', '旋转Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationZ', '旋转Z', SlotType.Number, 0));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '移动成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const sceneId = this.getInputValue('sceneId') as string;
    const positionX = this.getInputValue('positionX') as number;
    const positionY = this.getInputValue('positionY') as number;
    const positionZ = this.getInputValue('positionZ') as number;
    const rotationX = this.getInputValue('rotationX') as number;
    const rotationY = this.getInputValue('rotationY') as number;
    const rotationZ = this.getInputValue('rotationZ') as number;

    if (!sceneId) {
      this.triggerOutput('onError');
      throw new Error('场景ID不能为空');
    }

    try {
      // 获取场景加载系统
      const sceneLoader = this.context?.world?.getSystem('AvatarSceneLoader') as AvatarSceneLoader;
      if (!sceneLoader) {
        throw new Error('虚拟化身场景加载系统未找到');
      }

      // 执行移动
      const position = new Vector3(positionX, positionY, positionZ);
      const rotation = new Vector3(rotationX, rotationY, rotationZ);
      const success = sceneLoader.moveAvatarToPosition(sceneId, position, rotation);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('移动虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 注册虚拟化身场景节点
 */
export function registerAvatarSceneNodes(registry: any): void {
  registry.registerNode('QuickEnterSceneNode', QuickEnterSceneNode);
  registry.registerNode('GetAvailableScenesNode', GetAvailableScenesNode);
  registry.registerNode('SwitchSceneNode', SwitchSceneNode);
  registry.registerNode('UnloadSceneNode', UnloadSceneNode);
  registry.registerNode('MoveAvatarNode', MoveAvatarNode);
}
