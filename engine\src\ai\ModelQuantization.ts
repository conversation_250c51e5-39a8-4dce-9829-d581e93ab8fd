/**
 * 模型量化工具
 * 用于减小模型大小并提高推理速度
 */

/**
 * 量化位数
 */
export type QuantizationBits = 4 | 8 | 16 | 32;

/**
 * 量化方法
 */
export enum QuantizationMethod {
  /** 线性量化 */
  LINEAR = 'linear',
  /** 对数量化 */
  LOGARITHMIC = 'logarithmic',
  /** K-means量化 */
  KMEANS = 'kmeans',
  /** 混合精度量化 */
  MIXED_PRECISION = 'mixed_precision'
}

/**
 * 量化策略
 */
export enum QuantizationStrategy {
  /** 静态量化 */
  STATIC = 'static',
  /** 动态量化 */
  DYNAMIC = 'dynamic',
  /** 渐进式量化 */
  PROGRESSIVE = 'progressive',
  /** 自适应量化 */
  ADAPTIVE = 'adaptive'
}

/**
 * 量化配置
 */
export interface QuantizationConfig {
  /** 是否启用量化 */
  enabled: boolean;
  /** 量化位数 */
  bits: QuantizationBits;
  /** 量化方法 */
  method?: QuantizationMethod;
  /** 量化策略 */
  strategy?: QuantizationStrategy;
  /** 是否使用对称量化 */
  symmetric?: boolean;
  /** 是否使用通道量化 */
  perChannel?: boolean;
  /** 是否使用动态量化 */
  dynamic?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
  /** 精度阈值 */
  precisionThreshold?: number;
  /** 是否启用混合精度 */
  mixedPrecision?: boolean;
  /** 校准数据集大小 */
  calibrationDataSize?: number;
  /** 是否启用知识蒸馏 */
  knowledgeDistillation?: boolean;
}

/**
 * 量化统计
 */
export interface QuantizationStats {
  /** 原始模型大小（字节） */
  originalSize: number;
  /** 量化后模型大小（字节） */
  quantizedSize: number;
  /** 压缩比 */
  compressionRatio: number;
  /** 量化时间（毫秒） */
  quantizationTime: number;
  /** 精度损失 */
  precisionLoss?: number;
  /** 推理速度提升倍数 */
  speedupRatio?: number;
  /** 内存使用减少比例 */
  memoryReduction?: number;
  /** 量化方法 */
  method: QuantizationMethod;
  /** 量化策略 */
  strategy: QuantizationStrategy;
  /** 校准数据使用量 */
  calibrationDataUsed?: number;
}

/**
 * 量化验证结果
 */
export interface QuantizationValidationResult {
  /** 是否通过验证 */
  passed: boolean;
  /** 精度损失 */
  precisionLoss: number;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 验证时间（毫秒） */
  validationTime: number;
}

/**
 * 批量量化结果
 */
export interface BatchQuantizationResult {
  /** 成功量化的模型数量 */
  successCount: number;
  /** 失败量化的模型数量 */
  failureCount: number;
  /** 总处理时间（毫秒） */
  totalTime: number;
  /** 平均压缩比 */
  averageCompressionRatio: number;
  /** 平均精度损失 */
  averagePrecisionLoss: number;
  /** 详细结果 */
  results: Map<string, QuantizationStats | Error>;
}

/**
 * 校准数据
 */
export interface CalibrationData {
  /** 输入数据 */
  inputs: ArrayBuffer[];
  /** 期望输出 */
  expectedOutputs?: ArrayBuffer[];
  /** 数据标签 */
  labels?: string[];
}

/**
 * 模型量化工具
 */
export class ModelQuantization {
  /** 配置 */
  private config: QuantizationConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: QuantizationConfig = {
    enabled: false,
    bits: 8,
    method: QuantizationMethod.LINEAR,
    strategy: QuantizationStrategy.STATIC,
    symmetric: true,
    perChannel: true,
    dynamic: false,
    debug: false,
    precisionThreshold: 0.05,
    mixedPrecision: false,
    calibrationDataSize: 100,
    knowledgeDistillation: false
  };
  
  /** 量化统计 */
  private stats: QuantizationStats | null = null;
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<QuantizationConfig> = {}) {
    this.config = {
      ...ModelQuantization.DEFAULT_CONFIG,
      ...config
    };
  }
  
  /**
   * 量化模型
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  public quantize(model: ArrayBuffer): ArrayBuffer {
    // 如果未启用量化，直接返回原始模型
    if (!this.config.enabled) {
      return model;
    }
    
    const startTime = Date.now();
    
    try {
      // 根据量化位数选择不同的量化方法
      let quantizedModel: ArrayBuffer;

      switch (this.config.bits) {
        case 4:
          quantizedModel = this.quantize4Bit(model);
          break;
        case 8:
          quantizedModel = this.quantize8Bit(model);
          break;
        case 16:
          quantizedModel = this.quantize16Bit(model);
          break;
        case 32:
        default:
          // 32位不进行量化，直接返回原始模型
          quantizedModel = model;
          break;
      }
      
      // 计算量化统计
      const endTime = Date.now();
      this.stats = {
        originalSize: model.byteLength,
        quantizedSize: quantizedModel.byteLength,
        compressionRatio: model.byteLength / quantizedModel.byteLength,
        quantizationTime: endTime - startTime,
        method: this.config.method!,
        strategy: this.config.strategy!,
        memoryReduction: (model.byteLength - quantizedModel.byteLength) / model.byteLength
      };
      
      // 如果启用调试，输出统计信息
      if (this.config.debug) {
        console.log('[ModelQuantization] 量化统计:', this.stats);
      }
      
      return quantizedModel;
    } catch (error) {
      console.error('[ModelQuantization] 量化失败:', error);
      
      // 量化失败，返回原始模型
      return model;
    }
  }
  
  /**
   * 8位量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  private quantize8Bit(model: ArrayBuffer): ArrayBuffer {
    // 这里是8位量化的实现
    // 实际实现需要根据具体需求和环境
    
    // 模拟量化过程
    const float32Array = new Float32Array(model);
    const int8Array = new Int8Array(float32Array.length);
    
    // 找出最大值和最小值
    let max = -Infinity;
    let min = Infinity;
    
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      max = Math.max(max, value);
      min = Math.min(min, value);
    }
    
    // 计算缩放因子
    const scale = this.config.symmetric
      ? Math.max(Math.abs(min), Math.abs(max)) / 127
      : (max - min) / 255;
    
    const zeroPoint = this.config.symmetric
      ? 0
      : Math.round((0 - min) / scale);
    
    // 量化
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      int8Array[i] = Math.round(value / scale) + zeroPoint;
    }
    
    // 创建量化后的模型数据
    const quantizedModel = new ArrayBuffer(int8Array.byteLength + 8);
    const quantizedView = new DataView(quantizedModel);
    
    // 存储缩放因子和零点
    quantizedView.setFloat32(0, scale, true);
    quantizedView.setInt32(4, zeroPoint, true);
    
    // 存储量化后的数据
    const quantizedArray = new Int8Array(quantizedModel, 8);
    quantizedArray.set(int8Array);
    
    return quantizedModel;
  }
  
  /**
   * 16位量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  private quantize16Bit(model: ArrayBuffer): ArrayBuffer {
    // 这里是16位量化的实现
    // 实际实现需要根据具体需求和环境
    
    // 模拟量化过程
    const float32Array = new Float32Array(model);
    const int16Array = new Int16Array(float32Array.length);
    
    // 找出最大值和最小值
    let max = -Infinity;
    let min = Infinity;
    
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      max = Math.max(max, value);
      min = Math.min(min, value);
    }
    
    // 计算缩放因子
    const scale = this.config.symmetric
      ? Math.max(Math.abs(min), Math.abs(max)) / 32767
      : (max - min) / 65535;
    
    const zeroPoint = this.config.symmetric
      ? 0
      : Math.round((0 - min) / scale);
    
    // 量化
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      int16Array[i] = Math.round(value / scale) + zeroPoint;
    }
    
    // 创建量化后的模型数据
    const quantizedModel = new ArrayBuffer(int16Array.byteLength + 8);
    const quantizedView = new DataView(quantizedModel);
    
    // 存储缩放因子和零点
    quantizedView.setFloat32(0, scale, true);
    quantizedView.setInt32(4, zeroPoint, true);
    
    // 存储量化后的数据
    const quantizedArray = new Int16Array(quantizedModel, 8);
    quantizedArray.set(int16Array);
    
    return quantizedModel;
  }
  
  /**
   * 反量化模型
   * @param quantizedModel 量化后的模型数据
   * @returns 反量化后的模型数据
   */
  public dequantize(quantizedModel: ArrayBuffer): ArrayBuffer {
    // 如果未启用量化，直接返回原始模型
    if (!this.config.enabled) {
      return quantizedModel;
    }
    
    try {
      // 读取缩放因子和零点
      const quantizedView = new DataView(quantizedModel);
      const scale = quantizedView.getFloat32(0, true);
      const zeroPoint = quantizedView.getInt32(4, true);
      
      // 根据量化位数选择不同的反量化方法
      switch (this.config.bits) {
        case 4:
          return this.dequantize4Bit(quantizedModel, scale, zeroPoint);
        case 8:
          return this.dequantize8Bit(quantizedModel, scale, zeroPoint);
        case 16:
          return this.dequantize16Bit(quantizedModel, scale, zeroPoint);
        case 32:
        default:
          // 32位不进行反量化，直接返回原始模型
          return quantizedModel;
      }
    } catch (error) {
      console.error('[ModelQuantization] 反量化失败:', error);
      
      // 反量化失败，返回原始模型
      return quantizedModel;
    }
  }
  
  /**
   * 8位反量化
   * @param quantizedModel 量化后的模型数据
   * @param scale 缩放因子
   * @param zeroPoint 零点
   * @returns 反量化后的模型数据
   */
  private dequantize8Bit(quantizedModel: ArrayBuffer, scale: number, zeroPoint: number): ArrayBuffer {
    // 读取量化后的数据
    const quantizedArray = new Int8Array(quantizedModel, 8);
    
    // 创建反量化后的模型数据
    const dequantizedModel = new ArrayBuffer(quantizedArray.length * 4);
    const float32Array = new Float32Array(dequantizedModel);
    
    // 反量化
    for (let i = 0; i < quantizedArray.length; i++) {
      float32Array[i] = (quantizedArray[i] - zeroPoint) * scale;
    }
    
    return dequantizedModel;
  }
  
  /**
   * 16位反量化
   * @param quantizedModel 量化后的模型数据
   * @param scale 缩放因子
   * @param zeroPoint 零点
   * @returns 反量化后的模型数据
   */
  private dequantize16Bit(quantizedModel: ArrayBuffer, scale: number, zeroPoint: number): ArrayBuffer {
    // 读取量化后的数据
    const quantizedArray = new Int16Array(quantizedModel, 8);
    
    // 创建反量化后的模型数据
    const dequantizedModel = new ArrayBuffer(quantizedArray.length * 4);
    const float32Array = new Float32Array(dequantizedModel);
    
    // 反量化
    for (let i = 0; i < quantizedArray.length; i++) {
      float32Array[i] = (quantizedArray[i] - zeroPoint) * scale;
    }
    
    return dequantizedModel;
  }
  
  /**
   * 获取量化统计
   * @returns 量化统计
   */
  public getStats(): QuantizationStats | null {
    return this.stats;
  }
  
  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): QuantizationConfig {
    return { ...this.config };
  }
  
  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: Partial<QuantizationConfig>): void {
    this.config = {
      ...this.config,
      ...config
    };
  }

  /**
   * 动态量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  public dynamicQuantize(model: ArrayBuffer): ArrayBuffer {
    if (!this.config.enabled || !this.config.dynamic) {
      return model;
    }

    const startTime = Date.now();

    try {
      // 分析模型数据分布
      const distribution = this.analyzeDataDistribution(model);

      // 根据分布选择最优量化参数
      const optimalConfig = this.selectOptimalQuantizationParams(distribution);

      // 应用动态量化
      const quantizedModel = this.applyDynamicQuantization(model, optimalConfig);

      // 更新统计信息
      const endTime = Date.now();
      this.stats = {
        originalSize: model.byteLength,
        quantizedSize: quantizedModel.byteLength,
        compressionRatio: model.byteLength / quantizedModel.byteLength,
        quantizationTime: endTime - startTime,
        method: this.config.method!,
        strategy: QuantizationStrategy.DYNAMIC,
        memoryReduction: (model.byteLength - quantizedModel.byteLength) / model.byteLength
      };

      return quantizedModel;
    } catch (error) {
      console.error('[ModelQuantization] 动态量化失败:', error);
      return model;
    }
  }

  /**
   * 通道量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  public perChannelQuantize(model: ArrayBuffer): ArrayBuffer {
    if (!this.config.enabled || !this.config.perChannel) {
      return model;
    }

    const startTime = Date.now();

    try {
      const float32Array = new Float32Array(model);
      const channels = this.detectChannels(float32Array);

      // 为每个通道单独计算量化参数
      const channelParams = channels.map(channel =>
        this.calculateChannelQuantizationParams(channel)
      );

      // 应用通道量化
      const quantizedModel = this.applyPerChannelQuantization(float32Array, channelParams);

      // 更新统计信息
      const endTime = Date.now();
      this.stats = {
        originalSize: model.byteLength,
        quantizedSize: quantizedModel.byteLength,
        compressionRatio: model.byteLength / quantizedModel.byteLength,
        quantizationTime: endTime - startTime,
        method: this.config.method!,
        strategy: this.config.strategy!,
        memoryReduction: (model.byteLength - quantizedModel.byteLength) / model.byteLength
      };

      return quantizedModel;
    } catch (error) {
      console.error('[ModelQuantization] 通道量化失败:', error);
      return model;
    }
  }

  /**
   * 混合精度量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  public mixedPrecisionQuantize(model: ArrayBuffer): ArrayBuffer {
    if (!this.config.enabled || !this.config.mixedPrecision) {
      return model;
    }

    const startTime = Date.now();

    try {
      const float32Array = new Float32Array(model);

      // 分析权重重要性
      const importance = this.analyzeWeightImportance(float32Array);

      // 根据重要性分配不同精度
      const precisionMap = this.assignMixedPrecision(importance);

      // 应用混合精度量化
      const quantizedModel = this.applyMixedPrecisionQuantization(float32Array, precisionMap);

      // 更新统计信息
      const endTime = Date.now();
      this.stats = {
        originalSize: model.byteLength,
        quantizedSize: quantizedModel.byteLength,
        compressionRatio: model.byteLength / quantizedModel.byteLength,
        quantizationTime: endTime - startTime,
        method: QuantizationMethod.MIXED_PRECISION,
        strategy: this.config.strategy!,
        memoryReduction: (model.byteLength - quantizedModel.byteLength) / model.byteLength
      };

      return quantizedModel;
    } catch (error) {
      console.error('[ModelQuantization] 混合精度量化失败:', error);
      return model;
    }
  }

  /**
   * 批量量化
   * @param models 模型数据数组
   * @returns 批量量化结果
   */
  public async batchQuantize(models: Map<string, ArrayBuffer>): Promise<BatchQuantizationResult> {
    const startTime = Date.now();
    const results = new Map<string, QuantizationStats | Error>();
    let successCount = 0;
    let failureCount = 0;
    let totalCompressionRatio = 0;
    let totalPrecisionLoss = 0;

    for (const [modelId, modelData] of models) {
      try {
        this.quantize(modelData);
        const stats = this.getStats();

        if (stats) {
          results.set(modelId, stats);
          successCount++;
          totalCompressionRatio += stats.compressionRatio;
          totalPrecisionLoss += stats.precisionLoss || 0;
        }
      } catch (error) {
        results.set(modelId, error as Error);
        failureCount++;
      }
    }

    const endTime = Date.now();

    return {
      successCount,
      failureCount,
      totalTime: endTime - startTime,
      averageCompressionRatio: successCount > 0 ? totalCompressionRatio / successCount : 0,
      averagePrecisionLoss: successCount > 0 ? totalPrecisionLoss / successCount : 0,
      results
    };
  }

  /**
   * 验证量化结果
   * @param originalModel 原始模型
   * @param quantizedModel 量化后模型
   * @param calibrationData 校准数据
   * @returns 验证结果
   */
  public async validateQuantization(
    originalModel: ArrayBuffer,
    quantizedModel: ArrayBuffer,
    calibrationData?: CalibrationData
  ): Promise<QuantizationValidationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 基本验证
      if (quantizedModel.byteLength >= originalModel.byteLength) {
        warnings.push('量化后模型大小未减小');
      }

      // 精度验证
      let precisionLoss = 0;
      if (calibrationData) {
        precisionLoss = await this.calculatePrecisionLoss(
          originalModel,
          quantizedModel,
          calibrationData
        );
      }

      // 检查精度阈值
      if (precisionLoss > this.config.precisionThreshold!) {
        errors.push(`精度损失超过阈值: ${precisionLoss} > ${this.config.precisionThreshold}`);
      }

      const endTime = Date.now();

      return {
        passed: errors.length === 0,
        precisionLoss,
        errors,
        warnings,
        validationTime: endTime - startTime
      };
    } catch (error) {
      errors.push(`验证过程中发生错误: ${error}`);

      return {
        passed: false,
        precisionLoss: 1.0,
        errors,
        warnings,
        validationTime: Date.now() - startTime
      };
    }
  }

  /**
   * 计算精度损失
   */
  private async calculatePrecisionLoss(
    originalModel: ArrayBuffer,
    quantizedModel: ArrayBuffer,
    _calibrationData: CalibrationData
  ): Promise<number> {
    // 这里应该实现实际的精度损失计算
    // 暂时返回模拟值
    const originalSize = originalModel.byteLength;
    const quantizedSize = quantizedModel.byteLength;
    const compressionRatio = originalSize / quantizedSize;

    // 简单的精度损失估算
    return Math.max(0, (compressionRatio - 1) * 0.01);
  }

  // 辅助方法实现
  private analyzeDataDistribution(model: ArrayBuffer): any {
    const float32Array = new Float32Array(model);
    const values = Array.from(float32Array);

    // 计算基本统计信息
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const std = Math.sqrt(variance);

    // 计算分位数
    const sorted = values.sort((a, b) => a - b);
    const q25 = sorted[Math.floor(sorted.length * 0.25)];
    const q50 = sorted[Math.floor(sorted.length * 0.5)];
    const q75 = sorted[Math.floor(sorted.length * 0.75)];

    return {
      mean,
      std,
      variance,
      min: Math.min(...values),
      max: Math.max(...values),
      q25,
      q50,
      q75,
      outlierRatio: this.calculateOutlierRatio(values, q25, q75)
    };
  }

  private calculateOutlierRatio(values: number[], q25: number, q75: number): number {
    const iqr = q75 - q25;
    const lowerBound = q25 - 1.5 * iqr;
    const upperBound = q75 + 1.5 * iqr;

    const outliers = values.filter(val => val < lowerBound || val > upperBound);
    return outliers.length / values.length;
  }

  private selectOptimalQuantizationParams(distribution: any): any {
    // 根据数据分布选择最优量化参数
    const { std, outlierRatio } = distribution;

    return {
      symmetric: outlierRatio < 0.1, // 异常值少时使用对称量化
      bits: std > 1.0 ? 16 : 8, // 方差大时使用更高精度
      method: outlierRatio > 0.2 ? QuantizationMethod.LOGARITHMIC : QuantizationMethod.LINEAR
    };
  }

  private applyDynamicQuantization(model: ArrayBuffer, config: any): ArrayBuffer {
    // 应用动态量化配置
    const originalConfig = { ...this.config };

    // 临时更新配置
    this.config.symmetric = config.symmetric;
    this.config.bits = config.bits;
    this.config.method = config.method;

    // 执行量化
    const result = this.quantize(model);

    // 恢复原始配置
    this.config = originalConfig;

    return result;
  }

  private detectChannels(data: Float32Array): Float32Array[] {
    // 简单的通道检测逻辑
    // 假设数据按通道组织，每个通道包含相同数量的元素
    const channelSize = Math.sqrt(data.length);
    const channels: Float32Array[] = [];

    for (let i = 0; i < data.length; i += channelSize) {
      channels.push(data.slice(i, i + channelSize));
    }

    return channels;
  }

  private calculateChannelQuantizationParams(channel: Float32Array): any {
    const values = Array.from(channel);
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      scale: this.config.symmetric
        ? Math.max(Math.abs(min), Math.abs(max)) / 127
        : (max - min) / 255,
      zeroPoint: this.config.symmetric ? 0 : Math.round((0 - min) / ((max - min) / 255))
    };
  }

  private applyPerChannelQuantization(data: Float32Array, channelParams: any[]): ArrayBuffer {
    // 应用通道量化
    const quantizedData = new Int8Array(data.length);
    const channelSize = data.length / channelParams.length;

    for (let i = 0; i < channelParams.length; i++) {
      const { scale, zeroPoint } = channelParams[i];
      const startIdx = i * channelSize;
      const endIdx = startIdx + channelSize;

      for (let j = startIdx; j < endIdx; j++) {
        quantizedData[j] = Math.round(data[j] / scale) + zeroPoint;
      }
    }

    return quantizedData.buffer;
  }

  private analyzeWeightImportance(data: Float32Array): number[] {
    // 分析权重重要性
    const importance = new Array(data.length);

    for (let i = 0; i < data.length; i++) {
      // 简单的重要性计算：基于权重的绝对值
      importance[i] = Math.abs(data[i]);
    }

    return importance;
  }

  private assignMixedPrecision(importance: number[]): Map<number, QuantizationBits> {
    // 根据重要性分配精度
    const precisionMap = new Map<number, QuantizationBits>();

    // 计算重要性阈值
    const sortedImportance = [...importance].sort((a, b) => b - a);
    const highThreshold = sortedImportance[Math.floor(sortedImportance.length * 0.1)];
    const mediumThreshold = sortedImportance[Math.floor(sortedImportance.length * 0.3)];

    for (let i = 0; i < importance.length; i++) {
      if (importance[i] >= highThreshold) {
        precisionMap.set(i, 16); // 高重要性使用16位
      } else if (importance[i] >= mediumThreshold) {
        precisionMap.set(i, 8);  // 中等重要性使用8位
      } else {
        precisionMap.set(i, 4);  // 低重要性使用4位
      }
    }

    return precisionMap;
  }

  private applyMixedPrecisionQuantization(
    data: Float32Array,
    precisionMap: Map<number, QuantizationBits>
  ): ArrayBuffer {
    // 应用混合精度量化
    const result = new ArrayBuffer(data.length * 2); // 预估大小
    const view = new DataView(result);
    let offset = 0;

    for (let i = 0; i < data.length; i++) {
      const precision = precisionMap.get(i) || 8;
      const value = data[i];

      switch (precision) {
        case 4:
          // 4位量化（存储为8位）
          const quantized4 = Math.round(value * 7) + 8;
          view.setInt8(offset, Math.max(-8, Math.min(7, quantized4)));
          offset += 1;
          break;
        case 8:
          // 8位量化
          const quantized8 = Math.round(value * 127);
          view.setInt8(offset, Math.max(-128, Math.min(127, quantized8)));
          offset += 1;
          break;
        case 16:
          // 16位量化
          const quantized16 = Math.round(value * 32767);
          view.setInt16(offset, Math.max(-32768, Math.min(32767, quantized16)), true);
          offset += 2;
          break;
        default:
          // 32位（原始精度）
          view.setFloat32(offset, value, true);
          offset += 4;
          break;
      }
    }

    return result.slice(0, offset);
  }

  /**
   * 4位量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  private quantize4Bit(model: ArrayBuffer): ArrayBuffer {
    const float32Array = new Float32Array(model);

    // 找出最大值和最小值
    let max = -Infinity;
    let min = Infinity;

    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      max = Math.max(max, value);
      min = Math.min(min, value);
    }

    // 计算缩放因子（4位：-8到7）
    const scale = this.config.symmetric
      ? Math.max(Math.abs(min), Math.abs(max)) / 7
      : (max - min) / 15;

    const zeroPoint = this.config.symmetric ? 0 : Math.round((0 - min) / scale);

    // 量化（每两个4位值打包成一个字节）
    const packedLength = Math.ceil(float32Array.length / 2);
    const packedArray = new Uint8Array(packedLength);

    for (let i = 0; i < float32Array.length; i += 2) {
      const value1 = Math.round(float32Array[i] / scale) + zeroPoint;
      const value2 = i + 1 < float32Array.length
        ? Math.round(float32Array[i + 1] / scale) + zeroPoint
        : 0;

      // 将两个4位值打包成一个字节
      const clampedValue1 = Math.max(-8, Math.min(7, value1)) + 8; // 转换为0-15
      const clampedValue2 = Math.max(-8, Math.min(7, value2)) + 8;

      packedArray[Math.floor(i / 2)] = (clampedValue1 & 0xF) | ((clampedValue2 & 0xF) << 4);
    }

    // 创建量化后的模型数据
    const quantizedModel = new ArrayBuffer(packedArray.byteLength + 12);
    const quantizedView = new DataView(quantizedModel);

    // 存储元数据
    quantizedView.setFloat32(0, scale, true);
    quantizedView.setInt32(4, zeroPoint, true);
    quantizedView.setInt32(8, float32Array.length, true); // 原始长度

    // 存储量化后的数据
    const quantizedArray = new Uint8Array(quantizedModel, 12);
    quantizedArray.set(packedArray);

    return quantizedModel;
  }

  /**
   * 4位反量化
   * @param quantizedModel 量化后的模型数据
   * @param scale 缩放因子
   * @param zeroPoint 零点
   * @returns 反量化后的模型数据
   */
  private dequantize4Bit(quantizedModel: ArrayBuffer, scale: number, zeroPoint: number): ArrayBuffer {
    const quantizedView = new DataView(quantizedModel);
    const originalLength = quantizedView.getInt32(8, true);

    // 读取打包的量化数据
    const packedArray = new Uint8Array(quantizedModel, 12);

    // 创建反量化后的模型数据
    const dequantizedModel = new ArrayBuffer(originalLength * 4);
    const float32Array = new Float32Array(dequantizedModel);

    // 反量化
    for (let i = 0; i < originalLength; i += 2) {
      const packedByte = packedArray[Math.floor(i / 2)];

      // 解包两个4位值
      const value1 = (packedByte & 0xF) - 8; // 转换回-8到7
      const value2 = ((packedByte >> 4) & 0xF) - 8;

      float32Array[i] = (value1 - zeroPoint) * scale;
      if (i + 1 < originalLength) {
        float32Array[i + 1] = (value2 - zeroPoint) * scale;
      }
    }

    return dequantizedModel;
  }

  /**
   * 获取支持的量化位数
   */
  public static getSupportedBits(): QuantizationBits[] {
    return [4, 8, 16, 32];
  }

  /**
   * 获取推荐的量化配置
   * @param modelSize 模型大小（字节）
   * @param targetCompressionRatio 目标压缩比
   * @returns 推荐配置
   */
  public static getRecommendedConfig(
    modelSize: number,
    targetCompressionRatio: number = 4
  ): Partial<QuantizationConfig> {
    if (modelSize < 1024 * 1024) { // 小于1MB
      return {
        bits: 16,
        method: QuantizationMethod.LINEAR,
        strategy: QuantizationStrategy.STATIC
      };
    } else if (modelSize < 100 * 1024 * 1024) { // 小于100MB
      return {
        bits: 8,
        method: QuantizationMethod.LINEAR,
        strategy: QuantizationStrategy.DYNAMIC,
        perChannel: true
      };
    } else { // 大于100MB
      return {
        bits: targetCompressionRatio > 8 ? 4 : 8,
        method: QuantizationMethod.MIXED_PRECISION,
        strategy: QuantizationStrategy.ADAPTIVE,
        perChannel: true,
        mixedPrecision: true
      };
    }
  }
}
