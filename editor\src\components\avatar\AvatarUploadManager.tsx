/**
 * 虚拟化身上传管理器组件
 * 
 * 提供虚拟化身文件上传、数字人创建、管理等功能的统一界面
 */

import React, { useState, useCallback, useRef } from 'react';
import { 
  Card, 
  Upload, 
  Button, 
  Input, 
  InputNumber, 
  Switch, 
  Select, 
  Progress, 
  List, 
  Avatar, 
  Space, 
  Divider, 
  message, 
  Modal,
  Descriptions,
  Tag
} from 'antd';
import { 
  UploadOutlined, 
  CloudUploadOutlined, 
  RobotOutlined,
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 上传文件信息接口
 */
interface UploadFileInfo {
  fileName: string;
  fileSize: number;
  fileType: string;
  fileContent: string;
  uploadTime: Date;
}

/**
 * 数字人信息接口
 */
interface DigitalHumanInfo {
  id: string;
  name: string;
  avatarData: any;
  sceneId: string;
  position: { x: number; y: number; z: number };
  config: {
    enableAI: boolean;
    enableVoice: boolean;
    enableInteraction: boolean;
    knowledgeBaseId?: string;
    personality?: string;
  };
  createdAt: Date;
  status: 'active' | 'inactive' | 'error';
}

/**
 * 组件状态接口
 */
interface UploadManagerState {
  // 上传相关
  uploadFiles: UploadFile[];
  uploadProgress: number;
  isUploading: boolean;
  uploadStage: string;
  
  // 配置相关
  targetSceneId: string;
  spawnPosition: { x: number; y: number; z: number };
  digitalHumanName: string;
  enableAI: boolean;
  enableVoice: boolean;
  enableInteraction: boolean;
  knowledgeBaseId: string;
  personality: string;
  autoActivate: boolean;
  
  // 数字人管理
  digitalHumans: DigitalHumanInfo[];
  selectedDigitalHuman: DigitalHumanInfo | null;
  showDetailModal: boolean;
  
  // 统计信息
  statistics: {
    totalUploads: number;
    successfulUploads: number;
    failedUploads: number;
    activeDigitalHumans: number;
    averageProcessingTime: number;
    supportedFormats: string[];
  };
}

/**
 * 组件属性接口
 */
interface AvatarUploadManagerProps {
  onUploadComplete?: (result: any) => void;
  onDigitalHumanCreated?: (digitalHuman: DigitalHumanInfo) => void;
  onDigitalHumanRemoved?: (digitalHumanId: string) => void;
}

/**
 * 虚拟化身上传管理器组件
 */
export const AvatarUploadManager: React.FC<AvatarUploadManagerProps> = ({
  onUploadComplete,
  onDigitalHumanCreated,
  onDigitalHumanRemoved
}) => {
  const [state, setState] = useState<UploadManagerState>({
    uploadFiles: [],
    uploadProgress: 0,
    isUploading: false,
    uploadStage: '',
    targetSceneId: 'default',
    spawnPosition: { x: 0, y: 0, z: 0 },
    digitalHumanName: '',
    enableAI: false,
    enableVoice: false,
    enableInteraction: true,
    knowledgeBaseId: '',
    personality: 'friendly',
    autoActivate: true,
    digitalHumans: [],
    selectedDigitalHuman: null,
    showDetailModal: false,
    statistics: {
      totalUploads: 0,
      successfulUploads: 0,
      failedUploads: 0,
      activeDigitalHumans: 0,
      averageProcessingTime: 0,
      supportedFormats: ['json', 'bin', 'gltf', 'fbx']
    }
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 更新状态
   */
  const updateState = useCallback((updates: Partial<UploadManagerState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 处理文件上传
   */
  const handleFileUpload: UploadProps['customRequest'] = useCallback(async (options) => {
    const { file, onSuccess, onError, onProgress } = options;
    
    try {
      updateState({ isUploading: true, uploadProgress: 0, uploadStage: '准备上传...' });

      // 读取文件内容
      const fileContent = await readFileContent(file as File);
      
      // 构建上传文件信息
      const uploadFileInfo: UploadFileInfo = {
        fileName: (file as File).name,
        fileSize: (file as File).size,
        fileType: (file as File).type,
        fileContent,
        uploadTime: new Date()
      };

      // 模拟上传过程
      await simulateUploadProcess(uploadFileInfo, {
        onProgress: (progress, stage) => {
          updateState({ uploadProgress: progress, uploadStage: stage });
          onProgress?.({ percent: progress });
        }
      });

      // 创建数字人
      const digitalHuman = await createDigitalHuman(uploadFileInfo);
      
      // 更新数字人列表
      updateState({
        digitalHumans: [...state.digitalHumans, digitalHuman],
        statistics: {
          ...state.statistics,
          totalUploads: state.statistics.totalUploads + 1,
          successfulUploads: state.statistics.successfulUploads + 1,
          activeDigitalHumans: state.statistics.activeDigitalHumans + 1
        }
      });

      onSuccess?.(digitalHuman);
      onUploadComplete?.(digitalHuman);
      onDigitalHumanCreated?.(digitalHuman);
      
      message.success('虚拟化身上传成功，数字人已创建');

    } catch (error) {
      console.error('上传失败:', error);
      updateState({
        statistics: {
          ...state.statistics,
          totalUploads: state.statistics.totalUploads + 1,
          failedUploads: state.statistics.failedUploads + 1
        }
      });
      onError?.(error);
      message.error(`上传失败: ${error.message}`);
    } finally {
      updateState({ isUploading: false, uploadProgress: 0, uploadStage: '' });
    }
  }, [state, onUploadComplete, onDigitalHumanCreated, updateState]);

  /**
   * 读取文件内容
   */
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  };

  /**
   * 模拟上传过程
   */
  const simulateUploadProcess = async (
    fileInfo: UploadFileInfo,
    callbacks: {
      onProgress: (progress: number, stage: string) => void;
    }
  ): Promise<void> => {
    const stages = [
      { progress: 20, stage: '验证文件格式...', delay: 300 },
      { progress: 40, stage: '解析虚拟化身数据...', delay: 500 },
      { progress: 60, stage: '验证数据完整性...', delay: 400 },
      { progress: 80, stage: '创建数字人实体...', delay: 600 },
      { progress: 100, stage: '集成到场景...', delay: 300 }
    ];

    for (const stage of stages) {
      callbacks.onProgress(stage.progress, stage.stage);
      await new Promise(resolve => setTimeout(resolve, stage.delay));
    }
  };

  /**
   * 创建数字人
   */
  const createDigitalHuman = async (fileInfo: UploadFileInfo): Promise<DigitalHumanInfo> => {
    // 解析虚拟化身数据
    let avatarData;
    try {
      avatarData = JSON.parse(fileInfo.fileContent);
    } catch (error) {
      // 如果不是JSON格式，创建基础数据
      avatarData = {
        id: `uploaded_${Date.now()}`,
        name: fileInfo.fileName.replace(/\.[^/.]+$/, ''),
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    const digitalHuman: DigitalHumanInfo = {
      id: `dh_${Date.now()}`,
      name: state.digitalHumanName || avatarData.name || fileInfo.fileName.replace(/\.[^/.]+$/, ''),
      avatarData,
      sceneId: state.targetSceneId,
      position: state.spawnPosition,
      config: {
        enableAI: state.enableAI,
        enableVoice: state.enableVoice,
        enableInteraction: state.enableInteraction,
        knowledgeBaseId: state.knowledgeBaseId || undefined,
        personality: state.personality
      },
      createdAt: new Date(),
      status: 'active'
    };

    return digitalHuman;
  };

  /**
   * 删除数字人
   */
  const handleRemoveDigitalHuman = useCallback(async (digitalHumanId: string) => {
    try {
      // 从列表中移除
      const updatedDigitalHumans = state.digitalHumans.filter(dh => dh.id !== digitalHumanId);
      
      updateState({
        digitalHumans: updatedDigitalHumans,
        statistics: {
          ...state.statistics,
          activeDigitalHumans: Math.max(0, state.statistics.activeDigitalHumans - 1)
        }
      });

      onDigitalHumanRemoved?.(digitalHumanId);
      message.success('数字人已删除');

    } catch (error) {
      console.error('删除数字人失败:', error);
      message.error('删除数字人失败');
    }
  }, [state, onDigitalHumanRemoved, updateState]);

  /**
   * 显示数字人详情
   */
  const handleShowDigitalHumanDetail = useCallback((digitalHuman: DigitalHumanInfo) => {
    updateState({
      selectedDigitalHuman: digitalHuman,
      showDetailModal: true
    });
  }, [updateState]);

  /**
   * 渲染上传区域
   */
  const renderUploadArea = () => (
    <Card title="虚拟化身文件上传" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Upload.Dragger
          name="avatarFile"
          multiple={false}
          accept=".json,.bin,.gltf,.fbx"
          customRequest={handleFileUpload}
          showUploadList={false}
          disabled={state.isUploading}
        >
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 JSON、Binary、GLTF、FBX 格式的虚拟化身文件
          </p>
        </Upload.Dragger>

        {state.isUploading && (
          <div>
            <Progress 
              percent={state.uploadProgress} 
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            <p style={{ marginTop: 8, color: '#666' }}>{state.uploadStage}</p>
          </div>
        )}
      </Space>
    </Card>
  );

  /**
   * 渲染配置面板
   */
  const renderConfigPanel = () => (
    <Card title="数字人配置" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <label>目标场景：</label>
          <Select
            value={state.targetSceneId}
            onChange={(value) => updateState({ targetSceneId: value })}
            style={{ width: 200, marginLeft: 8 }}
          >
            <Option value="default">默认场景</Option>
            <Option value="medical_hall">医疗展厅</Option>
            <Option value="classroom">虚拟教室</Option>
            <Option value="outdoor_park">户外公园</Option>
          </Select>
        </div>

        <div>
          <label>数字人名称：</label>
          <Input
            value={state.digitalHumanName}
            onChange={(e) => updateState({ digitalHumanName: e.target.value })}
            placeholder="留空则使用文件名"
            style={{ width: 200, marginLeft: 8 }}
          />
        </div>

        <div>
          <label>生成位置：</label>
          <Space>
            <InputNumber
              value={state.spawnPosition.x}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, x: value || 0 }
              })}
              placeholder="X"
              style={{ width: 60 }}
            />
            <InputNumber
              value={state.spawnPosition.y}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, y: value || 0 }
              })}
              placeholder="Y"
              style={{ width: 60 }}
            />
            <InputNumber
              value={state.spawnPosition.z}
              onChange={(value) => updateState({
                spawnPosition: { ...state.spawnPosition, z: value || 0 }
              })}
              placeholder="Z"
              style={{ width: 60 }}
            />
          </Space>
        </div>

        <div>
          <Switch
            checked={state.enableAI}
            onChange={(checked) => updateState({ enableAI: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用AI功能</span>
        </div>

        <div>
          <Switch
            checked={state.enableVoice}
            onChange={(checked) => updateState({ enableVoice: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用语音功能</span>
        </div>

        <div>
          <Switch
            checked={state.enableInteraction}
            onChange={(checked) => updateState({ enableInteraction: checked })}
          />
          <span style={{ marginLeft: 8 }}>启用交互功能</span>
        </div>

        {state.enableAI && (
          <>
            <div>
              <label>知识库ID：</label>
              <Input
                value={state.knowledgeBaseId}
                onChange={(e) => updateState({ knowledgeBaseId: e.target.value })}
                placeholder="可选"
                style={{ width: 200, marginLeft: 8 }}
              />
            </div>

            <div>
              <label>个性设置：</label>
              <Select
                value={state.personality}
                onChange={(value) => updateState({ personality: value })}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Option value="friendly">友好</Option>
                <Option value="professional">专业</Option>
                <Option value="humorous">幽默</Option>
                <Option value="serious">严肃</Option>
              </Select>
            </div>
          </>
        )}
      </Space>
    </Card>
  );

  /**
   * 渲染数字人列表
   */
  const renderDigitalHumansList = () => (
    <Card title="数字人管理" size="small">
      <List
        itemLayout="horizontal"
        dataSource={state.digitalHumans}
        renderItem={(digitalHuman) => (
          <List.Item
            actions={[
              <Button
                key="detail"
                type="link"
                icon={<EyeOutlined />}
                onClick={() => handleShowDigitalHumanDetail(digitalHuman)}
              >
                详情
              </Button>,
              <Button
                key="delete"
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleRemoveDigitalHuman(digitalHuman.id)}
              >
                删除
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<RobotOutlined />} />}
              title={
                <Space>
                  {digitalHuman.name}
                  <Tag color={digitalHuman.status === 'active' ? 'green' : 'red'}>
                    {digitalHuman.status === 'active' ? '活跃' : '非活跃'}
                  </Tag>
                </Space>
              }
              description={
                <Space direction="vertical" size="small">
                  <span>场景: {digitalHuman.sceneId}</span>
                  <span>位置: ({digitalHuman.position.x}, {digitalHuman.position.y}, {digitalHuman.position.z})</span>
                  <span>创建时间: {digitalHuman.createdAt.toLocaleString()}</span>
                </Space>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: '暂无数字人' }}
      />
    </Card>
  );

  /**
   * 渲染统计信息
   */
  const renderStatistics = () => (
    <Card title="统计信息" size="small">
      <Descriptions column={2} size="small">
        <Descriptions.Item label="总上传次数">
          {state.statistics.totalUploads}
        </Descriptions.Item>
        <Descriptions.Item label="成功上传">
          {state.statistics.successfulUploads}
        </Descriptions.Item>
        <Descriptions.Item label="失败上传">
          {state.statistics.failedUploads}
        </Descriptions.Item>
        <Descriptions.Item label="活跃数字人">
          {state.statistics.activeDigitalHumans}
        </Descriptions.Item>
        <Descriptions.Item label="平均处理时间">
          {state.statistics.averageProcessingTime.toFixed(0)}ms
        </Descriptions.Item>
        <Descriptions.Item label="支持格式">
          {state.statistics.supportedFormats.join(', ')}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );

  /**
   * 渲染数字人详情模态框
   */
  const renderDetailModal = () => (
    <Modal
      title="数字人详情"
      open={state.showDetailModal}
      onCancel={() => updateState({ showDetailModal: false, selectedDigitalHuman: null })}
      footer={[
        <Button key="close" onClick={() => updateState({ showDetailModal: false, selectedDigitalHuman: null })}>
          关闭
        </Button>
      ]}
      width={600}
    >
      {state.selectedDigitalHuman && (
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="数字人ID">
            {state.selectedDigitalHuman.id}
          </Descriptions.Item>
          <Descriptions.Item label="名称">
            {state.selectedDigitalHuman.name}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={state.selectedDigitalHuman.status === 'active' ? 'green' : 'red'}>
              {state.selectedDigitalHuman.status === 'active' ? '活跃' : '非活跃'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="所在场景">
            {state.selectedDigitalHuman.sceneId}
          </Descriptions.Item>
          <Descriptions.Item label="位置">
            X: {state.selectedDigitalHuman.position.x},
            Y: {state.selectedDigitalHuman.position.y},
            Z: {state.selectedDigitalHuman.position.z}
          </Descriptions.Item>
          <Descriptions.Item label="AI功能">
            {state.selectedDigitalHuman.config.enableAI ? '已启用' : '未启用'}
          </Descriptions.Item>
          <Descriptions.Item label="语音功能">
            {state.selectedDigitalHuman.config.enableVoice ? '已启用' : '未启用'}
          </Descriptions.Item>
          <Descriptions.Item label="交互功能">
            {state.selectedDigitalHuman.config.enableInteraction ? '已启用' : '未启用'}
          </Descriptions.Item>
          {state.selectedDigitalHuman.config.knowledgeBaseId && (
            <Descriptions.Item label="知识库ID">
              {state.selectedDigitalHuman.config.knowledgeBaseId}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="个性设置">
            {state.selectedDigitalHuman.config.personality}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {state.selectedDigitalHuman.createdAt.toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="虚拟化身数据">
            <div style={{ maxHeight: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
              <pre style={{ margin: 0, fontSize: 12 }}>
                {JSON.stringify(state.selectedDigitalHuman.avatarData, null, 2)}
              </pre>
            </div>
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );

  return (
    <div style={{ padding: 16 }}>
      <Card title="虚拟化身上传管理器" extra={
        <Space>
          <Button icon={<FileTextOutlined />} onClick={() => fileInputRef.current?.click()}>
            选择文件
          </Button>
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      }>
        <input
          ref={fileInputRef}
          type="file"
          accept=".json,.bin,.gltf,.fbx"
          style={{ display: 'none' }}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              handleFileUpload({
                file,
                onSuccess: () => {},
                onError: () => {},
                onProgress: () => {}
              });
            }
          }}
        />

        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {renderUploadArea()}

          {renderConfigPanel()}

          {renderDigitalHumansList()}

          {renderStatistics()}
        </Space>

        {renderDetailModal()}
      </Card>
    </div>
  );
};
