/**
 * 变换组件
 * 管理实体的位置、旋转和缩放
 */

import * as THREE from 'three';
import { Component } from './Component';

export class TransformComponent extends Component {
  public static readonly type: string = 'Transform';
  
  /** Three.js 对象 */
  public object3D: THREE.Object3D;
  
  /** 位置 */
  public position: THREE.Vector3;
  
  /** 旋转 */
  public rotation: THREE.Euler;
  
  /** 四元数旋转 */
  public quaternion: THREE.Quaternion;
  
  /** 缩放 */
  public scale: THREE.Vector3;
  
  /** 变换矩阵 */
  public matrix: THREE.Matrix4;
  
  /** 世界变换矩阵 */
  public matrixWorld: THREE.Matrix4;

  constructor() {
    super(TransformComponent.type);
    
    // 创建 Three.js 对象
    this.object3D = new THREE.Object3D();
    
    // 获取引用
    this.position = this.object3D.position;
    this.rotation = this.object3D.rotation;
    this.quaternion = this.object3D.quaternion;
    this.scale = this.object3D.scale;
    this.matrix = this.object3D.matrix;
    this.matrixWorld = this.object3D.matrixWorld;
  }

  /**
   * 设置位置
   */
  public setPosition(x: number, y: number, z: number): void {
    this.position.set(x, y, z);
  }

  /**
   * 设置旋转（欧拉角）
   */
  public setRotation(x: number, y: number, z: number): void {
    this.rotation.set(x, y, z);
  }

  /**
   * 设置旋转（四元数）
   */
  public setQuaternion(x: number, y: number, z: number, w: number): void {
    this.quaternion.set(x, y, z, w);
  }

  /**
   * 设置缩放
   */
  public setScale(x: number, y: number, z: number): void {
    this.scale.set(x, y, z);
  }

  /**
   * 获取位置
   */
  public getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  /**
   * 获取旋转
   */
  public getRotation(): THREE.Euler {
    return this.rotation.clone();
  }

  /**
   * 获取四元数
   */
  public getQuaternion(): THREE.Quaternion {
    return this.quaternion.clone();
  }

  /**
   * 获取缩放
   */
  public getScale(): THREE.Vector3 {
    return this.scale.clone();
  }

  /**
   * 获取世界位置
   */
  public getWorldPosition(): THREE.Vector3 {
    const worldPosition = new THREE.Vector3();
    this.object3D.getWorldPosition(worldPosition);
    return worldPosition;
  }

  /**
   * 获取世界旋转
   */
  public getWorldQuaternion(): THREE.Quaternion {
    const worldQuaternion = new THREE.Quaternion();
    this.object3D.getWorldQuaternion(worldQuaternion);
    return worldQuaternion;
  }

  /**
   * 获取世界缩放
   */
  public getWorldScale(): THREE.Vector3 {
    const worldScale = new THREE.Vector3();
    this.object3D.getWorldScale(worldScale);
    return worldScale;
  }

  /**
   * 向前移动
   */
  public translateZ(distance: number): void {
    this.object3D.translateZ(distance);
  }

  /**
   * 向右移动
   */
  public translateX(distance: number): void {
    this.object3D.translateX(distance);
  }

  /**
   * 向上移动
   */
  public translateY(distance: number): void {
    this.object3D.translateY(distance);
  }

  /**
   * 绕X轴旋转
   */
  public rotateX(angle: number): void {
    this.object3D.rotateX(angle);
  }

  /**
   * 绕Y轴旋转
   */
  public rotateY(angle: number): void {
    this.object3D.rotateY(angle);
  }

  /**
   * 绕Z轴旋转
   */
  public rotateZ(angle: number): void {
    this.object3D.rotateZ(angle);
  }

  /**
   * 看向目标
   */
  public lookAt(target: THREE.Vector3): void {
    this.object3D.lookAt(target);
  }

  /**
   * 添加子对象
   */
  public add(object: THREE.Object3D): void {
    this.object3D.add(object);
  }

  /**
   * 移除子对象
   */
  public remove(object: THREE.Object3D): void {
    this.object3D.remove(object);
  }

  /**
   * 更新变换矩阵
   */
  public updateMatrix(): void {
    this.object3D.updateMatrix();
  }

  /**
   * 更新世界变换矩阵
   */
  public updateMatrixWorld(force?: boolean): void {
    this.object3D.updateMatrixWorld(force);
  }

  /**
   * 位置插值
   */
  public lerpPosition(target: THREE.Vector3, alpha: number): void {
    this.position.lerp(target, alpha);
  }

  /**
   * 旋转插值（四元数）
   */
  public slerpQuaternion(target: THREE.Quaternion, alpha: number): void {
    this.quaternion.slerp(target, alpha);
  }

  /**
   * 缩放插值
   */
  public lerpScale(target: THREE.Vector3, alpha: number): void {
    this.scale.lerp(target, alpha);
  }

  /**
   * 世界坐标转本地坐标
   */
  public worldToLocal(worldPosition: THREE.Vector3): THREE.Vector3 {
    const localPosition = worldPosition.clone();
    return this.object3D.worldToLocal(localPosition);
  }

  /**
   * 本地坐标转世界坐标
   */
  public localToWorld(localPosition: THREE.Vector3): THREE.Vector3 {
    const worldPosition = localPosition.clone();
    return this.object3D.localToWorld(worldPosition);
  }

  /**
   * 获取前方向量
   */
  public getForward(): THREE.Vector3 {
    const forward = new THREE.Vector3(0, 0, -1);
    forward.applyQuaternion(this.quaternion);
    return forward.normalize();
  }

  /**
   * 获取右方向量
   */
  public getRight(): THREE.Vector3 {
    const right = new THREE.Vector3(1, 0, 0);
    right.applyQuaternion(this.quaternion);
    return right.normalize();
  }

  /**
   * 获取上方向量
   */
  public getUp(): THREE.Vector3 {
    const up = new THREE.Vector3(0, 1, 0);
    up.applyQuaternion(this.quaternion);
    return up.normalize();
  }

  /**
   * 重置变换到默认状态
   */
  public resetTransform(): void {
    this.position.set(0, 0, 0);
    this.rotation.set(0, 0, 0);
    this.scale.set(1, 1, 1);
    this.updateMatrix();
  }

  /**
   * 复制另一个变换组件的变换
   */
  public copyTransform(other: TransformComponent): void {
    this.position.copy(other.position);
    this.rotation.copy(other.rotation);
    this.scale.copy(other.scale);
    this.updateMatrix();
  }

  /**
   * 获取边界框
   */
  public getBoundingBox(): THREE.Box3 {
    const box = new THREE.Box3();
    box.setFromObject(this.object3D);
    return box;
  }

  /**
   * 获取边界球
   */
  public getBoundingSphere(): THREE.Sphere {
    const sphere = new THREE.Sphere();
    sphere.setFromPoints([this.position]);
    return sphere;
  }

  /**
   * 设置变换矩阵
   */
  public setMatrix(matrix: THREE.Matrix4): void {
    this.object3D.matrix.copy(matrix);
    this.object3D.matrix.decompose(this.position, this.quaternion, this.scale);
    this.object3D.matrixAutoUpdate = false;
  }

  /**
   * 启用/禁用自动矩阵更新
   */
  public setMatrixAutoUpdate(autoUpdate: boolean): void {
    this.object3D.matrixAutoUpdate = autoUpdate;
  }

  /**
   * 计算到目标位置的距离
   */
  public distanceTo(target: THREE.Vector3): number {
    return this.position.distanceTo(target);
  }

  /**
   * 计算到目标位置的平方距离（性能更好）
   */
  public distanceToSquared(target: THREE.Vector3): number {
    return this.position.distanceToSquared(target);
  }

  /**
   * 创建组件实例
   */
  protected createInstance(): TransformComponent {
    return new TransformComponent();
  }

  /**
   * 序列化自定义数据
   */
  protected serializeData(): any {
    return {
      position: {
        x: this.position.x,
        y: this.position.y,
        z: this.position.z
      },
      rotation: {
        x: this.rotation.x,
        y: this.rotation.y,
        z: this.rotation.z
      },
      scale: {
        x: this.scale.x,
        y: this.scale.y,
        z: this.scale.z
      },
      matrixAutoUpdate: this.object3D.matrixAutoUpdate
    };
  }

  /**
   * 反序列化自定义数据
   */
  protected deserializeData(data: any): void {
    if (data.position) {
      this.position.set(data.position.x, data.position.y, data.position.z);
    }
    if (data.rotation) {
      this.rotation.set(data.rotation.x, data.rotation.y, data.rotation.z);
    }
    if (data.scale) {
      this.scale.set(data.scale.x, data.scale.y, data.scale.z);
    }
    if (data.matrixAutoUpdate !== undefined) {
      this.object3D.matrixAutoUpdate = data.matrixAutoUpdate;
    }
    this.updateMatrix();
  }

  /**
   * 组件附加到实体时调用
   */
  protected onAttach(): void {
    super.onAttach();
    // 确保变换矩阵是最新的
    this.updateMatrixWorld();
  }

  /**
   * 组件从实体分离时调用
   */
  protected onDetach(): void {
    super.onDetach();
    // 从父对象中移除
    this.object3D.removeFromParent();
  }

  /**
   * 组件重置时调用
   */
  protected onReset(): void {
    super.onReset();
    this.resetTransform();
  }

  /**
   * 更新组件
   */
  protected onUpdate(_deltaTime: number): void {
    // 自动更新矩阵
    if (this.object3D.matrixAutoUpdate) {
      this.object3D.updateMatrixWorld();
    }
  }

  /**
   * 销毁组件
   */
  protected onDispose(): void {
    // 清理 Three.js 对象
    this.object3D.clear();
    this.object3D.removeFromParent();
  }
}
