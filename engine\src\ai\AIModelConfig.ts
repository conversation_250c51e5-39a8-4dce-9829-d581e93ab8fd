/**
 * AI模型配置
 */
import { AIModelType } from './AIModelType';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 配置环境类型
 */
export enum ConfigEnvironment {
  /** 开发环境 */
  DEVELOPMENT = 'development',
  /** 测试环境 */
  TESTING = 'testing',
  /** 生产环境 */
  PRODUCTION = 'production',
  /** 本地环境 */
  LOCAL = 'local'
}

/**
 * 配置优先级
 */
export enum ConfigPriority {
  /** 低优先级 */
  LOW = 1,
  /** 中等优先级 */
  MEDIUM = 2,
  /** 高优先级 */
  HIGH = 3,
  /** 最高优先级 */
  CRITICAL = 4
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 建议信息 */
  suggestions: string[];
}

/**
 * 配置约束
 */
export interface ConfigConstraint {
  /** 字段名 */
  field: string;
  /** 是否必需 */
  required?: boolean;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 允许的值 */
  allowedValues?: any[];
  /** 依赖字段 */
  dependencies?: string[];
  /** 自定义验证函数 */
  validator?: (value: any, config: AIModelConfig) => boolean;
  /** 错误消息 */
  errorMessage?: string;
}

/**
 * AI模型配置接口
 */
export interface AIModelConfig {
  /** 配置ID */
  id?: string;

  /** 配置名称 */
  name?: string;

  /** 配置描述 */
  description?: string;

  /** 配置版本 */
  configVersion?: string;

  /** 模型类型 */
  modelType?: AIModelType;

  /** 模型版本 */
  version?: string;

  /** 模型变体 */
  variant?: string;

  /** 模型温度 (0-1) */
  temperature?: number;

  /** 最大令牌数 */
  maxTokens?: number;

  /** 是否使用流式响应 */
  stream?: boolean;

  /** 批处理大小 */
  batchSize?: number;

  /** 是否使用量化 */
  quantized?: boolean;

  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;

  /** 是否使用GPU加速 */
  useGPU?: boolean;

  /** 使用的GPU设备ID */
  gpuDeviceId?: number;

  /** 模型API密钥 */
  apiKey?: string;

  /** 模型API基础URL */
  baseUrl?: string;

  /** 模型路径 */
  modelPath?: string;

  /** 是否使用本地模型 */
  useLocalModel?: boolean;

  /** 模型缓存大小 */
  cacheSize?: number;

  /** 是否启用调试 */
  debug?: boolean;

  /** 配置环境 */
  environment?: ConfigEnvironment;

  /** 配置优先级 */
  priority?: ConfigPriority;

  /** 创建时间 */
  createdAt?: number;

  /** 更新时间 */
  updatedAt?: number;

  /** 配置标签 */
  tags?: string[];

  /** 性能配置 */
  performance?: {
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 重试次数 */
    retryCount?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
    /** 最大并发数 */
    maxConcurrency?: number;
    /** 内存限制（MB） */
    memoryLimit?: number;
  };

  /** 安全配置 */
  security?: {
    /** 是否启用加密 */
    enableEncryption?: boolean;
    /** 加密算法 */
    encryptionAlgorithm?: string;
    /** 访问控制 */
    accessControl?: string[];
    /** 数据脱敏 */
    dataMasking?: boolean;
  };

  /** 监控配置 */
  monitoring?: {
    /** 是否启用监控 */
    enabled?: boolean;
    /** 监控间隔（毫秒） */
    interval?: number;
    /** 性能阈值 */
    performanceThresholds?: {
      responseTime?: number;
      errorRate?: number;
      throughput?: number;
    };
  };

  /** 自定义配置 */
  [key: string]: any;
}

/**
 * 配置预设
 */
export interface ConfigPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设配置 */
  config: AIModelConfig;
  /** 适用的模型类型 */
  modelTypes: AIModelType[];
  /** 预设标签 */
  tags: string[];
  /** 是否为默认预设 */
  isDefault?: boolean;
}

/**
 * 配置模板
 */
export interface ConfigTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板配置 */
  template: Partial<AIModelConfig>;
  /** 必需字段 */
  requiredFields: string[];
  /** 可选字段 */
  optionalFields: string[];
  /** 模板版本 */
  version: string;
}

/**
 * 配置历史记录
 */
export interface ConfigHistory {
  /** 历史ID */
  id: string;
  /** 配置ID */
  configId: string;
  /** 配置快照 */
  snapshot: AIModelConfig;
  /** 变更描述 */
  changeDescription: string;
  /** 变更时间 */
  timestamp: number;
  /** 变更用户 */
  user?: string;
}

/**
 * 配置管理器选项
 */
export interface ConfigManagerOptions {
  /** 是否启用验证 */
  enableValidation?: boolean;
  /** 是否启用历史记录 */
  enableHistory?: boolean;
  /** 是否启用自动保存 */
  enableAutoSave?: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
  /** 最大历史记录数 */
  maxHistoryCount?: number;
  /** 配置存储路径 */
  storagePath?: string;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * AI模型配置管理器
 */
export class AIModelConfigManager extends EventEmitter {
  /** 配置存储 */
  private configs: Map<string, AIModelConfig> = new Map();

  /** 配置预设 */
  private presets: Map<string, ConfigPreset> = new Map();

  /** 配置模板 */
  private templates: Map<string, ConfigTemplate> = new Map();

  /** 配置历史 */
  private history: Map<string, ConfigHistory[]> = new Map();

  /** 配置约束 */
  private constraints: ConfigConstraint[] = [];

  /** 管理器选项 */
  private options: Required<ConfigManagerOptions>;

  /** 默认选项 */
  private static readonly DEFAULT_OPTIONS: Required<ConfigManagerOptions> = {
    enableValidation: true,
    enableHistory: true,
    enableAutoSave: false,
    autoSaveInterval: 30000, // 30秒
    maxHistoryCount: 50,
    storagePath: './configs',
    debug: false
  };

  constructor(options: ConfigManagerOptions = {}) {
    super();

    this.options = {
      ...AIModelConfigManager.DEFAULT_OPTIONS,
      ...options
    };

    // 初始化约束
    this.initializeConstraints();

    // 初始化预设
    this.initializePresets();

    // 初始化模板
    this.initializeTemplates();

    if (this.options.debug) {
      console.log('AI模型配置管理器初始化完成');
    }
  }

  /**
   * 创建配置
   */
  public createConfig(config: Partial<AIModelConfig>): AIModelConfig {
    const id = config.id || this.generateConfigId();
    const now = Date.now();

    const fullConfig: AIModelConfig = {
      id,
      name: config.name || `配置_${id}`,
      description: config.description || '',
      configVersion: '1.0.0',
      environment: ConfigEnvironment.DEVELOPMENT,
      priority: ConfigPriority.MEDIUM,
      createdAt: now,
      updatedAt: now,
      tags: [],
      ...config
    };

    // 验证配置
    if (this.options.enableValidation) {
      const validation = this.validateConfig(fullConfig);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 保存配置
    this.configs.set(id, fullConfig);

    // 记录历史
    if (this.options.enableHistory) {
      this.addToHistory(id, fullConfig, '创建配置');
    }

    // 发出事件
    this.emit('configCreated', fullConfig);

    if (this.options.debug) {
      console.log(`配置已创建: ${id}`);
    }

    return fullConfig;
  }

  /**
   * 更新配置
   */
  public updateConfig(id: string, updates: Partial<AIModelConfig>): AIModelConfig {
    const existingConfig = this.configs.get(id);
    if (!existingConfig) {
      throw new Error(`配置 ${id} 不存在`);
    }

    const updatedConfig: AIModelConfig = {
      ...existingConfig,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: Date.now()
    };

    // 验证配置
    if (this.options.enableValidation) {
      const validation = this.validateConfig(updatedConfig);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 保存配置
    this.configs.set(id, updatedConfig);

    // 记录历史
    if (this.options.enableHistory) {
      this.addToHistory(id, updatedConfig, '更新配置');
    }

    // 发出事件
    this.emit('configUpdated', updatedConfig);

    if (this.options.debug) {
      console.log(`配置已更新: ${id}`);
    }

    return updatedConfig;
  }

  /**
   * 获取配置
   */
  public getConfig(id: string): AIModelConfig | null {
    return this.configs.get(id) || null;
  }

  /**
   * 删除配置
   */
  public deleteConfig(id: string): boolean {
    const config = this.configs.get(id);
    if (!config) {
      return false;
    }

    this.configs.delete(id);

    // 记录历史
    if (this.options.enableHistory) {
      this.addToHistory(id, config, '删除配置');
    }

    // 发出事件
    this.emit('configDeleted', { id, config });

    if (this.options.debug) {
      console.log(`配置已删除: ${id}`);
    }

    return true;
  }

  /**
   * 获取所有配置
   */
  public getAllConfigs(): AIModelConfig[] {
    return Array.from(this.configs.values());
  }

  /**
   * 根据模型类型获取配置
   */
  public getConfigsByModelType(modelType: AIModelType): AIModelConfig[] {
    return this.getAllConfigs().filter(config => config.modelType === modelType);
  }

  /**
   * 根据环境获取配置
   */
  public getConfigsByEnvironment(environment: ConfigEnvironment): AIModelConfig[] {
    return this.getAllConfigs().filter(config => config.environment === environment);
  }

  /**
   * 验证配置
   */
  public validateConfig(config: AIModelConfig): ConfigValidationResult {
    const result: ConfigValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // 验证约束
    for (const constraint of this.constraints) {
      const value = (config as any)[constraint.field];

      // 检查必需字段
      if (constraint.required && (value === undefined || value === null)) {
        result.errors.push(`字段 ${constraint.field} 是必需的`);
        continue;
      }

      // 如果值不存在且不是必需的，跳过验证
      if (value === undefined || value === null) {
        continue;
      }

      // 检查数值范围
      if (typeof value === 'number') {
        if (constraint.min !== undefined && value < constraint.min) {
          result.errors.push(`字段 ${constraint.field} 的值 ${value} 小于最小值 ${constraint.min}`);
        }
        if (constraint.max !== undefined && value > constraint.max) {
          result.errors.push(`字段 ${constraint.field} 的值 ${value} 大于最大值 ${constraint.max}`);
        }
      }

      // 检查允许的值
      if (constraint.allowedValues && !constraint.allowedValues.includes(value)) {
        result.errors.push(`字段 ${constraint.field} 的值 ${value} 不在允许的值列表中`);
      }

      // 检查依赖关系
      if (constraint.dependencies) {
        for (const dep of constraint.dependencies) {
          if ((config as any)[dep] === undefined) {
            result.warnings.push(`字段 ${constraint.field} 依赖于字段 ${dep}，但 ${dep} 未设置`);
          }
        }
      }

      // 自定义验证
      if (constraint.validator && !constraint.validator(value, config)) {
        result.errors.push(constraint.errorMessage || `字段 ${constraint.field} 验证失败`);
      }
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * 合并配置
   */
  public mergeConfigs(baseConfig: AIModelConfig, ...configs: Partial<AIModelConfig>[]): AIModelConfig {
    let merged = { ...baseConfig };

    for (const config of configs) {
      merged = {
        ...merged,
        ...config,
        // 特殊处理嵌套对象
        performance: {
          ...merged.performance,
          ...config.performance
        },
        security: {
          ...merged.security,
          ...config.security
        },
        monitoring: {
          ...merged.monitoring,
          ...config.monitoring
        },
        tags: [
          ...(merged.tags || []),
          ...(config.tags || [])
        ].filter((tag, index, arr) => arr.indexOf(tag) === index) // 去重
      };
    }

    merged.updatedAt = Date.now();
    return merged;
  }

  /**
   * 从预设创建配置
   */
  public createFromPreset(presetId: string, overrides: Partial<AIModelConfig> = {}): AIModelConfig {
    const preset = this.presets.get(presetId);
    if (!preset) {
      throw new Error(`预设 ${presetId} 不存在`);
    }

    const config = this.mergeConfigs(preset.config, overrides);
    return this.createConfig(config);
  }

  /**
   * 从模板创建配置
   */
  public createFromTemplate(templateId: string, values: Partial<AIModelConfig>): AIModelConfig {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`模板 ${templateId} 不存在`);
    }

    // 检查必需字段
    for (const field of template.requiredFields) {
      if ((values as any)[field] === undefined) {
        throw new Error(`模板 ${templateId} 需要字段 ${field}`);
      }
    }

    const config = this.mergeConfigs(template.template as AIModelConfig, values);
    return this.createConfig(config);
  }

  /**
   * 获取配置历史
   */
  public getConfigHistory(configId: string): ConfigHistory[] {
    return this.history.get(configId) || [];
  }

  /**
   * 导出配置
   */
  public exportConfig(id: string): string {
    const config = this.getConfig(id);
    if (!config) {
      throw new Error(`配置 ${id} 不存在`);
    }

    return JSON.stringify(config, null, 2);
  }

  /**
   * 导入配置
   */
  public importConfig(configData: string): AIModelConfig {
    try {
      const config = JSON.parse(configData) as AIModelConfig;
      return this.createConfig(config);
    } catch (error) {
      throw new Error(`导入配置失败: ${error}`);
    }
  }

  /**
   * 克隆配置
   */
  public cloneConfig(id: string, newName?: string): AIModelConfig {
    const config = this.getConfig(id);
    if (!config) {
      throw new Error(`配置 ${id} 不存在`);
    }

    const cloned = {
      ...config,
      id: undefined, // 让系统生成新ID
      name: newName || `${config.name}_副本`,
      createdAt: undefined,
      updatedAt: undefined
    };

    return this.createConfig(cloned);
  }

  /**
   * 生成配置ID
   */
  private generateConfigId(): string {
    return `config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(configId: string, config: AIModelConfig, description: string): void {
    if (!this.history.has(configId)) {
      this.history.set(configId, []);
    }

    const history = this.history.get(configId)!;
    const historyItem: ConfigHistory = {
      id: `history_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      configId,
      snapshot: JSON.parse(JSON.stringify(config)), // 深拷贝
      changeDescription: description,
      timestamp: Date.now()
    };

    history.push(historyItem);

    // 限制历史记录数量
    if (history.length > this.options.maxHistoryCount) {
      history.shift();
    }
  }

  /**
   * 初始化约束
   */
  private initializeConstraints(): void {
    this.constraints = [
      {
        field: 'temperature',
        min: 0,
        max: 1,
        errorMessage: '温度值必须在0-1之间'
      },
      {
        field: 'maxTokens',
        min: 1,
        max: 100000,
        errorMessage: '最大令牌数必须在1-100000之间'
      },
      {
        field: 'batchSize',
        min: 1,
        max: 1000,
        errorMessage: '批处理大小必须在1-1000之间'
      },
      {
        field: 'quantizationBits',
        allowedValues: [8, 16, 32],
        errorMessage: '量化位数必须是8、16或32'
      },
      {
        field: 'gpuDeviceId',
        min: 0,
        errorMessage: 'GPU设备ID必须大于等于0'
      },
      {
        field: 'cacheSize',
        min: 0,
        errorMessage: '缓存大小必须大于等于0'
      },
      {
        field: 'apiKey',
        validator: (value: string) => typeof value === 'string' && value.length > 0,
        errorMessage: 'API密钥不能为空'
      }
    ];
  }

  /**
   * 初始化预设
   */
  private initializePresets(): void {
    // GPT预设
    this.presets.set('gpt_default', {
      id: 'gpt_default',
      name: 'GPT默认配置',
      description: '适用于GPT模型的默认配置',
      config: {
        modelType: AIModelType.GPT,
        temperature: 0.7,
        maxTokens: 2048,
        stream: false,
        batchSize: 1,
        useGPU: true,
        debug: false
      },
      modelTypes: [AIModelType.GPT],
      tags: ['default', 'gpt', 'text-generation'],
      isDefault: true
    });

    // BERT预设
    this.presets.set('bert_default', {
      id: 'bert_default',
      name: 'BERT默认配置',
      description: '适用于BERT模型的默认配置',
      config: {
        modelType: AIModelType.BERT,
        batchSize: 32,
        useGPU: true,
        quantized: false,
        debug: false
      },
      modelTypes: [AIModelType.BERT, AIModelType.ROBERTA, AIModelType.DISTILBERT],
      tags: ['default', 'bert', 'text-analysis'],
      isDefault: true
    });

    // 高性能预设
    this.presets.set('high_performance', {
      id: 'high_performance',
      name: '高性能配置',
      description: '优化性能的配置',
      config: {
        useGPU: true,
        batchSize: 64,
        quantized: true,
        quantizationBits: 16,
        performance: {
          timeout: 30000,
          retryCount: 3,
          maxConcurrency: 10,
          memoryLimit: 8192
        }
      },
      modelTypes: Object.values(AIModelType),
      tags: ['performance', 'gpu', 'optimized']
    });

    // 开发环境预设
    this.presets.set('development', {
      id: 'development',
      name: '开发环境配置',
      description: '适用于开发环境的配置',
      config: {
        debug: true,
        environment: ConfigEnvironment.DEVELOPMENT,
        priority: ConfigPriority.LOW,
        monitoring: {
          enabled: true,
          interval: 5000
        }
      },
      modelTypes: Object.values(AIModelType),
      tags: ['development', 'debug', 'monitoring']
    });

    // 生产环境预设
    this.presets.set('production', {
      id: 'production',
      name: '生产环境配置',
      description: '适用于生产环境的配置',
      config: {
        debug: false,
        environment: ConfigEnvironment.PRODUCTION,
        priority: ConfigPriority.HIGH,
        security: {
          enableEncryption: true,
          dataMasking: true
        },
        monitoring: {
          enabled: true,
          interval: 60000,
          performanceThresholds: {
            responseTime: 1000,
            errorRate: 0.01,
            throughput: 100
          }
        }
      },
      modelTypes: Object.values(AIModelType),
      tags: ['production', 'security', 'monitoring']
    });
  }

  /**
   * 初始化模板
   */
  private initializeTemplates(): void {
    // 基础模板
    this.templates.set('basic', {
      id: 'basic',
      name: '基础模板',
      description: '包含基本配置的模板',
      template: {
        configVersion: '1.0.0',
        environment: ConfigEnvironment.DEVELOPMENT,
        priority: ConfigPriority.MEDIUM,
        debug: false
      },
      requiredFields: ['modelType', 'name'],
      optionalFields: ['description', 'version', 'variant'],
      version: '1.0.0'
    });

    // API模板
    this.templates.set('api', {
      id: 'api',
      name: 'API模板',
      description: '用于API调用的模板',
      template: {
        useLocalModel: false,
        stream: false,
        performance: {
          timeout: 30000,
          retryCount: 3,
          retryDelay: 1000
        }
      },
      requiredFields: ['modelType', 'apiKey', 'baseUrl'],
      optionalFields: ['temperature', 'maxTokens'],
      version: '1.0.0'
    });

    // 本地模型模板
    this.templates.set('local', {
      id: 'local',
      name: '本地模型模板',
      description: '用于本地模型的模板',
      template: {
        useLocalModel: true,
        useGPU: true,
        quantized: false
      },
      requiredFields: ['modelType', 'modelPath'],
      optionalFields: ['batchSize', 'quantizationBits'],
      version: '1.0.0'
    });
  }

  /**
   * 获取所有预设
   */
  public getAllPresets(): ConfigPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取预设
   */
  public getPreset(id: string): ConfigPreset | null {
    return this.presets.get(id) || null;
  }

  /**
   * 获取所有模板
   */
  public getAllTemplates(): ConfigTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 获取模板
   */
  public getTemplate(id: string): ConfigTemplate | null {
    return this.templates.get(id) || null;
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.configs.clear();
    this.presets.clear();
    this.templates.clear();
    this.history.clear();
    this.constraints = [];
    this.removeAllListeners();
  }
}
