/**
 * AI模型管理器功能测试
 * 验证修复后的AIModelManager功能是否正常工作
 */
import { 
  AIModelManager,
  AIModelManagerConfig,
  ModelStatus,
  ModelInstanceInfo,
  PerformanceMetrics,
  BatchOperationResult
} from '../AIModelManager';
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';
import { AIModelLoadOptions, LoadStrategy, DeviceType } from '../AIModelLoadOptions';

/**
 * AI模型管理器测试类
 */
export class AIModelManagerTest {
  private manager: AIModelManager;

  constructor() {
    // 创建管理器实例，启用所有功能
    const config: AIModelManagerConfig = {
      debug: true,
      cacheSize: 10,
      useLocalModels: false,
      enablePerformanceMonitoring: true,
      performanceMonitoringInterval: 3000,
      enableModelWarmup: true,
      warmupTimeout: 15000,
      enableHotReload: false, // 测试时关闭热重载
      hotReloadInterval: 30000,
      maxConcurrentLoads: 2,
      healthCheckInterval: 10000,
      enableSmartModelSelection: true,
      apiKeys: {
        'gpt': 'test-gpt-key',
        'bert': 'test-bert-key'
      },
      baseUrls: {
        'gpt': 'https://api.openai.com',
        'bert': 'https://api.huggingface.co'
      }
    };

    this.manager = new AIModelManager(config);
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI模型管理器功能测试开始 ===\n');

    try {
      this.testInitialization();
      await this.testBasicModelLoading();
      await this.testBatchOperations();
      await this.testModelManagement();
      await this.testPerformanceMonitoring();
      await this.testHealthCheck();
      this.testSmartModelSelection();
      this.testManagerStatus();
      await this.testModelCleanup();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    } finally {
      this.manager.dispose();
    }
  }

  /**
   * 测试初始化
   */
  private testInitialization(): void {
    console.log('1. 测试初始化');

    this.manager.initialize();

    const status = this.manager.getManagerStatus();
    console.log(`管理器初始化: ${status.isInitialized ? '成功' : '失败'}`);
    console.log(`性能监控: ${status.monitoringEnabled.performance ? '已启用' : '未启用'}`);
    console.log(`健康检查: ${status.monitoringEnabled.healthCheck ? '已启用' : '未启用'}`);
    console.log(`最大并发加载数: ${status.concurrentLoads}/${this.manager['config'].maxConcurrentLoads}`);

    console.log('---\n');
  }

  /**
   * 测试基本模型加载
   */
  private async testBasicModelLoading(): Promise<void> {
    console.log('2. 测试基本模型加载');

    try {
      // 加载BERT模型
      const bertModel = await this.manager.loadModel(
        AIModelType.BERT,
        { variant: 'base', batchSize: 16 },
        { loadStrategy: LoadStrategy.EAGER, deviceType: DeviceType.CPU }
      );

      console.log(`BERT模型加载: ${bertModel ? '成功' : '失败'}`);
      if (bertModel) {
        console.log(`模型ID: ${bertModel.getId()}`);
        console.log(`模型类型: ${bertModel.getType()}`);
      }

      // 加载GPT模型
      const gptModel = await this.manager.loadModel(
        AIModelType.GPT,
        { variant: 'gpt-3.5-turbo', temperature: 0.7 },
        { loadStrategy: LoadStrategy.LAZY, deviceType: DeviceType.AUTO }
      );

      console.log(`GPT模型加载: ${gptModel ? '成功' : '失败'}`);

      // 检查已加载的模型
      const allModels = this.manager.getAllModels();
      console.log(`已加载模型数量: ${allModels.size}`);

    } catch (error) {
      console.log(`模型加载失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试批量操作
   */
  private async testBatchOperations(): Promise<void> {
    console.log('3. 测试批量操作');

    try {
      // 批量加载模型
      const loadRequests = [
        {
          modelType: AIModelType.DISTILBERT,
          config: { variant: 'base' },
          options: { loadStrategy: LoadStrategy.PRELOAD }
        },
        {
          modelType: AIModelType.ALBERT,
          config: { variant: 'base' },
          options: { loadStrategy: LoadStrategy.ON_DEMAND }
        }
      ];

      const loadResult = await this.manager.loadModels(loadRequests);
      console.log('批量加载结果:');
      console.log(`  成功: ${loadResult.successCount}/${loadResult.totalCount}`);
      console.log(`  失败: ${loadResult.failureCount}/${loadResult.totalCount}`);
      console.log(`  成功ID: ${loadResult.successIds.join(', ')}`);

      // 批量预热
      if (loadResult.successIds.length > 0) {
        const warmupResult = await this.manager.warmupModels(loadResult.successIds);
        console.log('批量预热结果:');
        console.log(`  成功: ${warmupResult.successCount}/${warmupResult.totalCount}`);
      }

    } catch (error) {
      console.log(`批量操作失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试模型管理
   */
  private async testModelManagement(): Promise<void> {
    console.log('4. 测试模型管理');

    // 获取所有模型实例信息
    const allInstances = this.manager.getAllModelInstances();
    console.log(`模型实例数量: ${allInstances.size}`);

    for (const [id, instance] of allInstances) {
      console.log(`模型 ${id}:`);
      console.log(`  类型: ${instance.type}`);
      console.log(`  状态: ${instance.status}`);
      console.log(`  使用次数: ${instance.usageCount}`);
      console.log(`  内存使用: ${(instance.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      console.log(`  预热状态: ${instance.isWarmedUp ? '已预热' : '未预热'}`);
      console.log(`  版本: ${instance.version}`);
    }

    // 测试模型重新加载
    if (allInstances.size > 0) {
      const firstModelId = allInstances.keys().next().value;
      console.log(`\n重新加载模型: ${firstModelId}`);
      const reloadSuccess = await this.manager.reloadModel(firstModelId);
      console.log(`重新加载结果: ${reloadSuccess ? '成功' : '失败'}`);
    }

    console.log('---\n');
  }

  /**
   * 测试性能监控
   */
  private async testPerformanceMonitoring(): Promise<void> {
    console.log('5. 测试性能监控');

    // 等待一段时间让性能监控收集数据
    await new Promise(resolve => setTimeout(resolve, 2000));

    const stats = this.manager.getPerformanceStats();
    console.log('性能统计:');
    console.log(`  总模型数: ${stats.totalModels}`);
    console.log(`  已加载模型: ${stats.loadedModels}`);
    console.log(`  就绪模型: ${stats.readyModels}`);
    console.log(`  错误模型: ${stats.errorModels}`);
    console.log(`  总内存使用: ${(stats.totalMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  平均响应时间: ${stats.averageResponseTime.toFixed(2)}ms`);
    console.log(`  总使用次数: ${stats.totalUsageCount}`);

    console.log('  各类型模型数量:');
    for (const [type, count] of stats.modelsByType) {
      console.log(`    ${type}: ${count}`);
    }

    console.log('  各状态模型数量:');
    for (const [status, count] of stats.modelsByStatus) {
      console.log(`    ${status}: ${count}`);
    }

    console.log('---\n');
  }

  /**
   * 测试健康检查
   */
  private async testHealthCheck(): Promise<void> {
    console.log('6. 测试健康检查');

    try {
      // 批量健康检查
      const healthResults = await this.manager.batchHealthCheck();
      
      console.log('健康检查结果:');
      for (const [modelId, health] of healthResults) {
        if (health) {
          console.log(`模型 ${modelId}:`);
          console.log(`  健康状态: ${health.healthy ? '健康' : '不健康'}`);
          console.log(`  状态: ${health.status}`);
          console.log(`  响应时间: ${health.responseTime || 'N/A'}ms`);
          console.log(`  内存使用: ${(health.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
          console.log(`  使用次数: ${health.usageCount}`);
          
          if (health.errors && health.errors.length > 0) {
            console.log(`  错误: ${health.errors.join(', ')}`);
          }
        }
      }
    } catch (error) {
      console.log(`健康检查失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试智能模型选择
   */
  private testSmartModelSelection(): void {
    console.log('7. 测试智能模型选择');

    // 测试不同要求的模型选择
    const requirements = [
      {
        name: '低延迟要求',
        requirements: { maxResponseTime: 1000, preferredTypes: [AIModelType.BERT, AIModelType.DISTILBERT] }
      },
      {
        name: '低内存要求',
        requirements: { maxMemoryUsage: 500 * 1024 * 1024 } // 500MB
      },
      {
        name: '高精度要求',
        requirements: { minAccuracy: 0.9, preferredTypes: [AIModelType.GPT, AIModelType.BERT] }
      }
    ];

    for (const req of requirements) {
      const selectedModel = this.manager.selectOptimalModel('text-classification', req.requirements);
      console.log(`${req.name}: ${selectedModel ? selectedModel.getId() : '无合适模型'}`);
    }

    console.log('---\n');
  }

  /**
   * 测试管理器状态
   */
  private testManagerStatus(): void {
    console.log('8. 测试管理器状态');

    const status = this.manager.getManagerStatus();
    console.log('管理器状态:');
    console.log(`  已初始化: ${status.isInitialized}`);
    console.log(`  总模型数: ${status.totalModels}`);
    console.log(`  加载中模型: ${status.loadingModels}`);
    console.log(`  就绪模型: ${status.readyModels}`);
    console.log(`  错误模型: ${status.errorModels}`);
    console.log(`  队列长度: ${status.queueLength}`);
    console.log(`  当前并发加载: ${status.concurrentLoads}`);
    console.log('  监控状态:');
    console.log(`    性能监控: ${status.monitoringEnabled.performance}`);
    console.log(`    热重载: ${status.monitoringEnabled.hotReload}`);
    console.log(`    健康检查: ${status.monitoringEnabled.healthCheck}`);

    console.log('---\n');
  }

  /**
   * 测试模型清理
   */
  private async testModelCleanup(): Promise<void> {
    console.log('9. 测试模型清理');

    // 清理未使用的模型（设置很短的空闲时间用于测试）
    const cleanedCount = this.manager.cleanupUnusedModels(1000); // 1秒
    console.log(`清理的模型数量: ${cleanedCount}`);

    // 卸载所有模型
    console.log('卸载所有模型...');
    this.manager.unloadAllModels();

    const finalStatus = this.manager.getManagerStatus();
    console.log(`最终模型数量: ${finalStatus.totalModels}`);

    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIModelManagerTest();
  test.runAllTests().catch(console.error);
}
