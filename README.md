# DL引擎 2.0 - 企业级可视化编程平台

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/your-username/dl-engine)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/your-username/dl-engine)
[![Coverage](https://img.shields.io/badge/coverage-85%25-yellowgreen.svg)](https://github.com/your-username/dl-engine)

DL（Digital Learning）引擎2.0是一个功能完整的企业级可视化编程平台，集成了先进的AI技术、实时协作、多媒体处理和专业应用支持。通过413个精心设计的视觉脚本节点，为开发者提供从基础编程到专业应用的完整解决方案。

## 🌟 核心特性

### 🎯 视觉脚本系统
- **413个节点**：覆盖100%功能需求，分布在27个节点文件中
- **可视化编程**：直观的拖拽式编程界面，降低开发门槛
- **实时执行**：支持实时调试和热重载
- **类型安全**：强类型系统确保代码质量

### 🤖 AI智能集成
- **46个AI节点**：涵盖机器学习、自然语言处理、情感计算
- **智能助手**：AI驱动的编程助手和代码生成
- **多模态AI**：支持文本、语音、图像的AI处理
- **个性化推荐**：智能推荐系统提升用户体验

### 🌐 实时协作
- **多用户编辑**：支持100+并发用户实时协作
- **WebRTC通信**：低延迟（<30ms）实时音视频通信
- **版本控制**：完整的版本管理和冲突解决
- **权限管理**：细粒度的用户权限控制

### 🎮 多媒体引擎
- **3D渲染**：基于Three.js的高性能3D渲染引擎
- **物理模拟**：完整的刚体、软体和流体物理系统
- **动画系统**：支持骨骼动画、面部动画和IK系统
- **音频处理**：3D空间音频和实时音频处理

### 🏭 专业应用
- **医疗模拟**：医疗健康展厅和虚拟病人系统
- **工业自动化**：智慧工厂和PLC控制系统
- **教育培训**：数字人教育智能体和学习跟踪
- **区块链集成**：NFT管理和智能合约支持

## 📁 项目架构

```
dl-engine/
├── engine/                 # 底层引擎（TypeScript + Three.js）
│   ├── src/
│   │   ├── core/           # 核心系统
│   │   ├── rendering/      # 渲染系统
│   │   ├── physics/        # 物理系统
│   │   ├── animation/      # 动画系统
│   │   ├── audio/          # 音频系统
│   │   ├── ai/             # AI系统
│   │   ├── network/        # 网络系统
│   │   └── visual-script/  # 视觉脚本系统
│   └── docs/               # 引擎文档
├── editor/                 # 可视化编辑器（React + TypeScript）
│   ├── src/
│   │   ├── components/     # UI组件
│   │   ├── editors/        # 专业编辑器
│   │   ├── panels/         # 面板组件
│   │   ├── ai/             # AI助手
│   │   └── collaboration/  # 协作功能
│   └── docs/               # 编辑器文档
├── server/                 # 微服务后端（Nest.js）
│   ├── api-gateway/        # API网关
│   ├── user-service/       # 用户服务
│   ├── project-service/    # 项目服务
│   ├── ai-service/         # AI服务
│   ├── collaboration-service/ # 协作服务
│   └── [30+ 微服务]/       # 其他专业服务
├── docs/                   # 项目文档
├── examples/               # 示例项目
├── tests/                  # 测试文件
└── deployment/             # 部署配置
```

## 🚀 核心功能详解

### 1. 视觉脚本系统（413个节点）

#### 核心基础节点（53个）
- **核心节点**（14个）：`CoreNodes.ts`
  - 流程控制：OnStartNode、BranchNode、ForLoopNode、WhileLoopNode
  - 数据操作：SetVariableNode、GetVariableNode、ArrayOperationNode
  - 异常处理：TryCatchNode、DelayNode、TypeConvertNode

- **数学节点**（16个）：`MathNodes.ts`
  - 基础运算：AddNode、SubtractNode、MultiplyNode、DivideNode
  - 高级数学：TrigonometricNode、VectorMathNode、RandomNode、InterpolationNode

- **调试节点**（9个）：`DebugNodes.ts`
  - 调试工具：BreakpointNode、LogNode、PerformanceProfilerNode、MemoryMonitorNode

#### AI智能节点（46个）
- **AI模型节点**（12个）：`AIModelNodes.ts`
  - 模型管理：LoadAIModelNode、UnloadModelNode、BatchInferenceNode
  - 内容生成：TextGenerationNode、ImageGenerationNode

- **自然语言处理**（14个）：`AINLPNodes.ts`
  - 文本处理：TextClassificationNode、NamedEntityRecognitionNode、TextSummaryNode
  - 语音处理：SpeechRecognitionNode、SpeechSynthesisNode
  - 对话管理：DialogueManagementNode、IntentRecognitionNode

- **情感计算**（8个）：`AIEmotionNodes.ts`
  - 情感分析：EmotionAnalysisNode、EmotionDrivenAnimationNode
  - 情感管理：EmotionHistoryNode、EmotionTransitionNode

#### 网络通信节点（43个）
- **基础网络**（7个）：`NetworkNodes.ts`
  - 连接管理：ConnectToServerNode、SendNetworkMessageNode、DisconnectFromServerNode

- **WebRTC通信**（13个）：`WebRTCNodes.ts`
  - 实时通信：CreateWebRTCConnectionNode、GetUserMediaNode、SendDataChannelMessageNode

- **HTTP请求**（5个）：`HTTPNodes.ts`
  - API调用：HTTPGetNode、HTTPPostNode、HTTPDeleteNode

#### 用户界面节点（34个）
- **基础UI**（14个）：`UINodes.ts`
  - UI组件：CreateButtonNode、CreateTextNode、CreateInputNode、CreateSliderNode

- **高级UI**（6个）：`AdvancedUINodes.ts`
  - 复杂组件：CreateDataGridNode、CreateTreeViewNode、CreateChartNode

#### 多媒体节点（62个）
- **音频节点**（13个）：`AudioNodes.ts`
  - 音频处理：PlayAudioNode、Audio3DNode、AudioMixerNode、AudioSpatializerNode

- **动画节点**（21个）：`AnimationNodes.ts` + `AdvancedAnimationNodes.ts`
  - 动画控制：PlayAnimationNode、AnimationBlendNode、IKSolverNode、RetargetAnimationNode

- **物理节点**（22个）：`PhysicsNodes.ts` + `SoftBodyNodes.ts`
  - 物理模拟：CreatePhysicsBodyNode、ApplyForceNode、RaycastNode、CreateClothNode

#### 专业应用节点（119个）
- **虚拟化身**（30个）：`AvatarCustomizationNodes.ts`
  - 化身创建：CreateAvatarNode、ReconstructFaceFromPhotoNode、GenerateBodyNode

- **医疗模拟**（4个）：`MedicalSimulationNodes.ts`
  - 医疗应用：MedicalKnowledgeQueryNode、SymptomAnalysisNode、VirtualPatientNode

- **工业自动化**（7个）：`IndustrialAutomationNodes.ts`
  - 工业控制：PLCControlNode、SensorDataReadNode、QualityInspectionNode

### 2. AI智能系统

#### AI助手系统
```typescript
// 文件位置：editor/src/ai/
├── AIChatPanel.tsx              # AI聊天界面
├── ChatMessageComponent.tsx    # 消息组件
├── useAIAssistant.ts           # AI助手Hook
└── AIService.ts                # AI服务接口
```

**核心功能**：
- 自然语言理解和代码生成
- 智能操作建议和自动执行
- 上下文感知的对话管理
- 多模态输入支持（文本、语音、图像）

#### 智能推荐系统
```typescript
// 文件位置：engine/src/ai/
├── AIRecommendationEngine.ts      # 推荐引擎
├── ContentFeatureExtractor.ts    # 特征提取
├── RealtimeRecommendationCache.ts # 实时缓存
└── EnhancedAIModelManager.ts      # 模型管理
```

**核心功能**：
- 个性化内容推荐
- 用户行为分析
- 实时推荐更新
- 多算法融合

### 3. 实时协作系统

#### 协作服务
```typescript
// 文件位置：server/collaboration-service/
├── src/
│   ├── collaboration.controller.ts  # 协作控制器
│   ├── collaboration.service.ts     # 协作服务
│   ├── collaboration.gateway.ts     # WebSocket网关
│   └── entities/                    # 数据实体
```

**核心功能**：
- 实时多用户编辑
- 冲突检测和解决
- 操作历史和版本控制
- 用户状态同步

#### WebRTC通信
```typescript
// 文件位置：server/signaling-service/
├── src/
│   ├── signaling.controller.ts     # 信令控制器
│   ├── webrtc.service.ts          # WebRTC服务
│   └── peer-connection.manager.ts  # 连接管理
```

**核心功能**：
- 低延迟音视频通信（<30ms）
- P2P数据传输
- 屏幕共享
- 网络优化和自适应

### 4. 多媒体引擎

#### 3D渲染引擎
```typescript
// 文件位置：engine/src/rendering/
├── Renderer.ts                 # 渲染器
├── Camera.ts                   # 相机系统
├── Material.ts                 # 材质系统
├── Light.ts                    # 光照系统
└── PostProcessing.ts           # 后处理
```

#### 物理系统
```typescript
// 文件位置：engine/src/physics/
├── PhysicsWorld.ts             # 物理世界
├── RigidBody.ts                # 刚体
├── SoftBody.ts                 # 软体
├── FluidSimulation.ts          # 流体模拟
└── CollisionDetection.ts       # 碰撞检测
```

#### 动画系统
```typescript
// 文件位置：engine/src/animation/
├── AnimationSystem.ts          # 动画系统
├── SkeletalAnimation.ts        # 骨骼动画
├── FacialAnimation.ts          # 面部动画
├── IKSolver.ts                 # IK求解器
└── AnimationBlending.ts        # 动画混合
```

### 5. 专业应用模块

#### 医疗健康展厅
```typescript
// 文件位置：examples/medical-exhibition/
├── MedicalExhibitionScene.ts   # 医疗展厅场景
├── DigitalHuman.ts             # 数字人系统
├── MedicalKnowledgeBase.ts     # 医疗知识库
└── InteractionSystem.ts        # 交互系统
```

#### 智慧工厂系统
```typescript
// 文件位置：examples/smart-factory/
├── FactorySimulation.ts        # 工厂仿真
├── PLCIntegration.ts           # PLC集成
├── ProductionLine.ts           # 生产线
└── QualityControl.ts           # 质量控制
```

#### 虚拟化身系统
```typescript
// 文件位置：engine/src/avatar/
├── AvatarSystem.ts             # 化身系统
├── FaceReconstruction.ts       # 面部重建
├── BodyCustomization.ts        # 身体定制
└── MotionCapture.ts            # 动作捕捉
```

## 🛠️ 技术栈

### 前端技术
- **TypeScript 5.8+**：类型安全的JavaScript超集
- **React 18+**：现代化的用户界面库
- **Redux Toolkit**：状态管理解决方案
- **Ant Design 5+**：企业级UI组件库
- **Three.js**：3D图形渲染库
- **Vite**：快速的构建工具

### 后端技术
- **Nest.js**：企业级Node.js框架
- **TypeORM**：TypeScript ORM框架
- **MySQL 8.0**：关系型数据库
- **Redis**：内存数据库和缓存
- **Docker**：容器化部署
- **Kubernetes**：容器编排

### AI技术
- **TensorFlow.js**：机器学习框架
- **ONNX Runtime**：AI模型推理引擎
- **Transformers.js**：自然语言处理
- **OpenAI API**：大语言模型集成
- **Whisper**：语音识别
- **Stable Diffusion**：图像生成

### 通信技术
- **WebRTC**：实时音视频通信
- **WebSocket**：实时数据传输
- **Socket.IO**：实时事件驱动通信
- **gRPC**：高性能RPC框架
- **MQTT**：物联网消息协议

## 📦 安装和部署

### 系统要求
- **Node.js** >= 18.0.0
- **npm** >= 9.0.0
- **Docker** >= 20.0.0
- **Docker Compose** >= 2.0.0
- **MySQL** >= 8.0
- **Redis** >= 6.0

### 快速开始

1. **克隆项目**
```bash
git clone https://github.com/your-username/dl-engine.git
cd dl-engine
```

2. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

3. **安装依赖**
```bash
# 安装根目录依赖
npm install

# 安装引擎依赖
cd engine && npm install && cd ..

# 安装编辑器依赖
cd editor && npm install && cd ..

# 安装服务器依赖（自动化脚本）
npm run install:server
```

4. **启动开发环境**
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者使用开发脚本
npm run dev:all
```

5. **访问应用**
- 编辑器界面：http://localhost:3001
- API文档：http://localhost:3000/api/docs
- 监控面板：http://localhost:3002

### 生产环境部署

#### Docker部署
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d
```

#### Kubernetes部署
```bash
# 应用Kubernetes配置
kubectl apply -f deployment/k8s/

# 检查部署状态
kubectl get pods -n dl-engine
```

#### 边缘计算部署
```bash
# 部署边缘节点
./scripts/deploy-edge.sh

# 配置负载均衡
./scripts/setup-load-balancer.sh
```

## 📚 使用指南

### 快速入门教程

#### 1. 创建第一个视觉脚本
```typescript
// 在编辑器中创建新的视觉脚本
const script = new VisualScript('MyFirstScript');

// 添加开始节点
const startNode = new OnStartNode();

// 添加日志节点
const logNode = new LogNode({
  message: 'Hello, DL Engine 2.0!'
});

// 连接节点
startNode.connectTo(logNode);

// 执行脚本
script.execute();
```

#### 2. 创建AI驱动的数字人
```typescript
// 创建数字人实体
const digitalHuman = new DigitalHuman({
  name: '小助手',
  appearance: 'professional',
  personality: 'friendly'
});

// 配置AI能力
digitalHuman.addAICapability('speech_recognition');
digitalHuman.addAICapability('natural_language_understanding');
digitalHuman.addAICapability('emotion_expression');

// 设置知识库
digitalHuman.setKnowledgeBase('medical_knowledge');

// 启动交互
digitalHuman.startInteraction();
```

#### 3. 实现实时协作
```typescript
// 创建协作会话
const collaboration = new CollaborationSession({
  projectId: 'project-123',
  maxUsers: 10
});

// 加入协作
collaboration.join(userId, userInfo);

// 监听协作事件
collaboration.on('userJoined', (user) => {
  console.log(`${user.name} 加入了协作`);
});

collaboration.on('operationReceived', (operation) => {
  // 应用远程操作
  applyOperation(operation);
});
```

### 核心概念

#### 视觉脚本节点系统
DL引擎2.0的核心是基于节点的可视化编程系统，包含413个专业节点：

- **输入插槽**：接收数据的接口
- **输出插槽**：输出数据的接口
- **执行流**：控制节点执行顺序
- **数据流**：在节点间传递数据

#### AI集成架构
三层AI架构设计：
- **底层引擎**：AI模型管理、推理执行
- **编辑器层**：AI助手界面、智能建议
- **服务端层**：AI微服务、分布式推理

#### 实时协作机制
- **操作转换**：OT算法确保一致性
- **冲突解决**：智能冲突检测和解决
- **状态同步**：实时用户状态同步
- **版本控制**：完整的历史记录

### API参考

#### 核心API
```typescript
// 引擎初始化
const engine = new DLEngine({
  canvas: document.getElementById('canvas'),
  enablePhysics: true,
  enableAI: true
});

// 场景管理
const scene = engine.createScene('MainScene');
scene.loadFromFile('scene.json');

// 实体操作
const entity = scene.createEntity('Player');
entity.addComponent(new MeshRenderer());
entity.addComponent(new PhysicsBody());

// 视觉脚本
const script = new VisualScript();
script.loadFromFile('player-controller.vs');
entity.addComponent(script);
```

#### AI API
```typescript
// AI模型管理
const aiManager = engine.getAIManager();
await aiManager.loadModel('gpt-3.5-turbo', {
  apiKey: 'your-api-key'
});

// 自然语言处理
const nlp = aiManager.getNLPService();
const result = await nlp.processText('创建一个红色的立方体');

// 语音处理
const speech = aiManager.getSpeechService();
const audioData = await speech.synthesize('你好，欢迎使用DL引擎');
```

#### 协作API
```typescript
// 协作管理
const collaboration = engine.getCollaborationManager();
await collaboration.connect('ws://localhost:3000');

// 发送操作
collaboration.sendOperation({
  type: 'CREATE_ENTITY',
  data: { name: 'NewEntity', position: [0, 0, 0] }
});

// 监听远程操作
collaboration.onRemoteOperation((operation) => {
  engine.applyOperation(operation);
});
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

### 测试结构
```
tests/
├── unit/                   # 单元测试
│   ├── engine/            # 引擎测试
│   ├── editor/            # 编辑器测试
│   └── server/            # 服务器测试
├── integration/           # 集成测试
│   ├── ai-integration/    # AI集成测试
│   ├── collaboration/     # 协作测试
│   └── visual-script/     # 视觉脚本测试
├── e2e/                   # 端到端测试
│   ├── user-workflows/    # 用户工作流测试
│   └── performance/       # 性能测试
└── fixtures/              # 测试数据
```

### 性能测试
```bash
# 运行性能基准测试
npm run benchmark

# 压力测试
npm run stress-test

# 内存泄漏测试
npm run memory-test
```

## 📈 性能优化

### 系统性能指标
- **渲染性能**：60 FPS @ 1080p
- **网络延迟**：< 30ms (WebRTC)
- **并发用户**：100+ 用户/实例
- **内存使用**：< 2GB (典型场景)
- **启动时间**：< 5秒 (编辑器)

### 优化策略

#### 前端优化
- **代码分割**：按需加载模块
- **虚拟化渲染**：大数据集优化
- **缓存策略**：智能缓存管理
- **Bundle优化**：Tree-shaking和压缩

#### 后端优化
- **数据库优化**：索引和查询优化
- **缓存层**：Redis多级缓存
- **负载均衡**：智能请求分发
- **微服务优化**：服务间通信优化

#### AI优化
- **模型量化**：减少模型大小
- **批处理推理**：提高吞吐量
- **边缘推理**：降低延迟
- **缓存预测**：预测结果缓存

## 🔧 配置说明

### 环境变量配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=dl_user
DB_PASSWORD=your_password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# AI服务配置
OPENAI_API_KEY=your_openai_key
AZURE_COGNITIVE_KEY=your_azure_key

# WebRTC配置
STUN_SERVER=stun:stun.l.google.com:19302
TURN_SERVER=turn:your-turn-server.com:3478

# 文件存储配置
STORAGE_TYPE=local # local, s3, azure
STORAGE_PATH=/app/uploads
```

### 微服务配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  api-gateway:
    image: dl-engine/api-gateway:2.0
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your_jwt_secret

  user-service:
    image: dl-engine/user-service:2.0
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis

  ai-service:
    image: dl-engine/ai-service:2.0
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CACHE_SIZE=2GB

  collaboration-service:
    image: dl-engine/collaboration-service:2.0
    environment:
      - REDIS_HOST=redis
      - MAX_CONCURRENT_USERS=100
```

## 🚀 部署架构

### 单机部署
适用于开发和小规模使用：
```
┌─────────────────┐
│   Load Balancer │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   DL Engine     │
│   (All Services)│
└─────────────────┘
```

### 分布式部署
适用于生产环境：
```
┌─────────────────┐
│   Load Balancer │
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ API Gateway│
    └─────┬─────┘
          │
┌─────────▼─────────┐
│   Microservices   │
├───────────────────┤
│ User │ AI │ Collab│
│ Asset│ Game│ ...  │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│   Data Layer      │
├───────────────────┤
│ MySQL │ Redis     │
│ MinIO │ Elastic   │
└───────────────────┘
```

### 边缘计算部署
适用于全球化应用：
```
┌─────────────────┐
│   Global CDN    │
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │Edge Nodes │
    └─────┬─────┘
          │
┌─────────▼─────────┐
│  Regional Centers │
├───────────────────┤
│ US-East│ EU-West  │
│ AP-SE  │ ...      │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│  Central Cloud    │
└───────────────────┘
```

## 🌍 应用案例

### 1. 医疗健康展厅
**项目描述**：为医院创建沉浸式数字展厅，展示医疗设备和健康知识。

**核心功能**：
- 数字人导览员，主动问候和介绍
- 医疗设备3D展示和交互
- 语音问答系统，回答医疗相关问题
- 情感表达，创造舒适的学习氛围

**使用的节点**：
```typescript
// 主要使用的视觉脚本节点
- CreateAvatarNode              // 创建数字人
- SpeechRecognitionNode         // 语音识别
- MedicalKnowledgeQueryNode     // 医疗知识查询
- EmotionAnalysisNode           // 情感分析
- PlayAnimationNode             // 播放动画
- Audio3DNode                   // 3D音效
```

**技术实现**：
- 使用AI自然语言处理进行智能问答
- 集成医疗知识库，提供专业信息
- 实时情感分析，调整交互策略
- 3D场景渲染，展示医疗设备模型

### 2. 智慧工厂仿真
**项目描述**：为制造企业构建数字孪生工厂，实现生产过程可视化和优化。

**核心功能**：
- 生产线实时监控和数据可视化
- PLC设备集成和控制
- 质量检测和异常预警
- 生产计划优化建议

**使用的节点**：
```typescript
// 主要使用的视觉脚本节点
- PLCControlNode                // PLC控制
- SensorDataReadNode            // 传感器数据读取
- QualityInspectionNode         // 质量检测
- CreateDataGridNode            // 数据表格显示
- HTTPPostNode                  // 数据上传
- AlertNotificationNode         // 告警通知
```

### 3. 在线教育平台
**项目描述**：构建沉浸式在线学习平台，支持多人协作和AI辅助教学。

**核心功能**：
- 虚拟教室和3D学习环境
- AI教学助手和个性化推荐
- 实时协作和互动讨论
- 学习进度跟踪和分析

**使用的节点**：
```typescript
// 主要使用的视觉脚本节点
- CreateWebRTCConnectionNode    // 实时通信
- TextGenerationNode            // AI内容生成
- LearningRecordNode            // 学习记录
- ProgressAnalysisNode          // 进度分析
- CreateTreeViewNode            // 课程结构展示
- CollaborationSyncNode         // 协作同步
```

### 4. 虚拟展览馆
**项目描述**：为博物馆和艺术机构创建虚拟展览空间。

**核心功能**：
- 3D文物展示和交互
- 虚拟导览和语音解说
- 多用户同时参观
- AR/VR设备支持

**使用的节点**：
```typescript
// 主要使用的视觉脚本节点
- LoadImageNode                 // 加载展品图像
- Audio3DNode                   // 空间音频
- GestureRecognitionNode        // 手势识别
- CreateModalNode               // 信息弹窗
- SpeechSynthesisNode           // 语音合成
- MotionCaptureInputNode        // 动作捕捉
```

## 📖 文档资源

### 官方文档
- [**快速开始指南**](./docs/quick-start.md) - 5分钟上手DL引擎
- [**视觉脚本节点使用指南**](./docs/视觉脚本节点使用指南.md) - 413个节点详细说明
- [**API参考文档**](./docs/api-reference.md) - 完整的API文档
- [**架构设计文档**](./docs/architecture.md) - 系统架构详解

### 开发指南
- [**引擎开发指南**](./engine/docs/development-guide.md) - 引擎扩展开发
- [**编辑器开发指南**](./editor/docs/development-guide.md) - 编辑器插件开发
- [**微服务开发指南**](./server/docs/microservice-guide.md) - 微服务开发规范
- [**AI集成指南**](./docs/ai-integration-guide.md) - AI功能集成

### 部署运维
- [**部署指南**](./docs/deployment-guide.md) - 生产环境部署
- [**运维手册**](./docs/operations-manual.md) - 系统运维指南
- [**性能调优指南**](./docs/performance-tuning.md) - 性能优化建议
- [**故障排除指南**](./docs/troubleshooting.md) - 常见问题解决

### 教程示例
- [**基础教程**](./examples/tutorials/) - 从入门到进阶
- [**应用案例**](./examples/use-cases/) - 实际项目案例
- [**最佳实践**](./docs/best-practices.md) - 开发最佳实践
- [**代码示例**](./examples/code-samples/) - 代码片段集合

## 🤝 社区和支持

### 社区资源
- **官方网站**：https://dl-engine.com
- **GitHub仓库**：https://github.com/your-username/dl-engine
- **技术论坛**：https://forum.dl-engine.com
- **Discord社区**：https://discord.gg/dl-engine

### 技术支持
- **文档中心**：https://docs.dl-engine.com
- **视频教程**：https://learn.dl-engine.com
- **技术博客**：https://blog.dl-engine.com
- **在线演示**：https://demo.dl-engine.com

### 贡献指南

#### 如何贡献
1. **Fork项目**到你的GitHub账户
2. **创建特性分支**：`git checkout -b feature/amazing-feature`
3. **提交更改**：`git commit -m 'Add some amazing feature'`
4. **推送分支**：`git push origin feature/amazing-feature`
5. **创建Pull Request**

#### 贡献类型
- 🐛 **Bug修复**：修复已知问题
- ✨ **新功能**：添加新的功能特性
- 📚 **文档改进**：完善文档和示例
- 🎨 **UI/UX改进**：提升用户体验
- ⚡ **性能优化**：提高系统性能
- 🧪 **测试增强**：增加测试覆盖率

#### 代码规范
- 使用TypeScript进行开发
- 遵循ESLint和Prettier配置
- 编写单元测试和集成测试
- 提供详细的commit信息
- 更新相关文档

### 版本发布

#### 版本规划
- **主版本**（Major）：重大架构变更
- **次版本**（Minor）：新功能添加
- **修订版本**（Patch）：Bug修复和小改进

#### 发布周期
- **稳定版本**：每季度发布
- **预览版本**：每月发布
- **热修复版本**：按需发布

## 📄 许可证

本项目采用 **MIT许可证** - 详情请参阅 [LICENSE](./LICENSE) 文件。

### 许可证摘要
- ✅ **商业使用**：允许商业项目使用
- ✅ **修改**：允许修改源代码
- ✅ **分发**：允许分发和再分发
- ✅ **私人使用**：允许私人项目使用
- ❗ **责任限制**：作者不承担使用风险
- ❗ **无担保**：软件按"原样"提供

## 🙏 致谢

### 核心团队
- **项目负责人**：[Your Name]
- **架构师**：[Architect Name]
- **前端团队**：[Frontend Team]
- **后端团队**：[Backend Team]
- **AI团队**：[AI Team]

### 技术支持
感谢以下开源项目和技术社区的支持：
- [Three.js](https://threejs.org/) - 3D图形渲染
- [React](https://reactjs.org/) - 用户界面框架
- [Nest.js](https://nestjs.com/) - 后端框架
- [TypeScript](https://www.typescriptlang.org/) - 类型安全
- [Docker](https://www.docker.com/) - 容器化技术

### 特别鸣谢
- 所有贡献者和社区成员
- 测试用户和反馈提供者
- 开源社区的无私奉献

---

## 📞 联系我们

- **邮箱**：<EMAIL>
- **技术支持**：<EMAIL>
- **商务合作**：<EMAIL>
- **媒体咨询**：<EMAIL>

---

<div align="center">

**DL引擎 2.0 - 让创意无限可能** 🚀

[![Star on GitHub](https://img.shields.io/github/stars/your-username/dl-engine?style=social)](https://github.com/your-username/dl-engine)
[![Follow on Twitter](https://img.shields.io/twitter/follow/dl_engine?style=social)](https://twitter.com/dl_engine)

</div>
