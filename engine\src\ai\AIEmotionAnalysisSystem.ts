/**
 * AI情感分析系统
 * 提供高级情感分析功能，支持多种分析方法和情感类型
 */
import { System } from '../core/System';
import { EmotionAnalysisResult } from '../avatar/ai/EmotionBasedAnimationGenerator';
import { EventEmitter } from '../utils/EventEmitter';

// 情感类型枚举
export enum EmotionType {
  HAPPY = 'happy',           // 快乐
  SAD = 'sad',               // 悲伤
  ANGRY = 'angry',           // 愤怒
  SURPRISED = 'surprised',   // 惊讶
  FEARFUL = 'fearful',       // 恐惧
  DISGUSTED = 'disgusted',   // 厌恶
  NEUTRAL = 'neutral',       // 中性
  EXCITED = 'excited',       // 兴奋
  CALM = 'calm',             // 平静
  CONFUSED = 'confused',     // 困惑
  CONTEMPT = 'contempt',     // 蔑视
  LOVE = 'love',             // 爱
  HATE = 'hate',             // 恨
  PRIDE = 'pride',           // 骄傲
  SHAME = 'shame',           // 羞耻
  GUILT = 'guilt',           // 内疚
  ENVY = 'envy',             // 嫉妒
  GRATITUDE = 'gratitude',   // 感激
  HOPE = 'hope',             // 希望
  DESPAIR = 'despair',       // 绝望
  RELIEF = 'relief',         // 解脱
  ANTICIPATION = 'anticipation', // 期待
  BOREDOM = 'boredom',       // 无聊
  CURIOSITY = 'curiosity',   // 好奇
  TRUST = 'trust',           // 信任
  DISTRUST = 'distrust'      // 不信任
}

// 情感强度级别
export enum EmotionIntensity {
  VERY_LOW = 0.1,
  LOW = 0.3,
  MEDIUM = 0.5,
  HIGH = 0.7,
  VERY_HIGH = 0.9
}

// 分析方法
export enum AnalysisMethod {
  KEYWORD_MATCHING = 'keyword_matching',
  SENTIMENT_ANALYSIS = 'sentiment_analysis',
  DEEP_LEARNING = 'deep_learning',
  HYBRID = 'hybrid'
}

/**
 * AI情感分析系统配置
 */
export interface AIEmotionAnalysisSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 分析方法 */
  analysisMethod?: AnalysisMethod;
  /** 是否启用情感历史记录 */
  enableHistory?: boolean;
  /** 历史记录最大长度 */
  maxHistoryLength?: number;
  /** 是否启用实时分析 */
  enableRealtime?: boolean;
  /** 语言设置 */
  language?: 'zh' | 'en' | 'auto';
  /** 是否启用表情符号分析 */
  enableEmoticonAnalysis?: boolean;
  /** 是否启用语调分析 */
  enableToneAnalysis?: boolean;
}

// 情感词典条目
interface EmotionWord {
  word: string;
  emotion: EmotionType;
  intensity: number;
  weight: number;
  context?: string[];
}

// 情感分析选项
export interface EmotionAnalysisOptions {
  /** 是否返回详细信息 */
  detailed?: boolean;
  /** 是否包含次要情感 */
  includeSecondary?: boolean;
  /** 是否包含情感变化 */
  includeChanges?: boolean;
  /** 分析深度 */
  depth?: 'shallow' | 'medium' | 'deep';
  /** 语言 */
  language?: 'zh' | 'en' | 'auto';
  /** 上下文信息 */
  context?: string;
}

// 情感历史记录
interface EmotionHistory {
  timestamp: number;
  text: string;
  result: EmotionAnalysisResult;
  context?: string;
}

/**
 * AI情感分析系统
 */
export class AIEmotionAnalysisSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'AIEmotionAnalysisSystem';

  /** 配置 */
  private config: AIEmotionAnalysisSystemConfig;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 情感词典 */
  private emotionDictionary: EmotionWord[] = [];

  /** 否定词集合 */
  private negationWords: Set<string> = new Set();

  /** 程度副词 */
  private intensityModifiers: Map<string, number> = new Map();

  /** 情感历史记录 */
  private emotionHistory: EmotionHistory[] = [];

  /** 表情符号映射 */
  private emoticonMap: Map<string, { emotion: EmotionType; intensity: number }> = new Map();

  /** 实时分析队列 */
  private realtimeQueue: { text: string; timestamp: number; callback?: (result: EmotionAnalysisResult | null) => void }[] = [];

  /** 实时分析定时器 */
  private realtimeTimer: NodeJS.Timeout | null = null;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIEmotionAnalysisSystemConfig = {}) {
    super(300); // 设置优先级

    this.config = {
      debug: false,
      useLocalModel: false,
      modelPath: '',
      analysisMethod: AnalysisMethod.HYBRID,
      enableHistory: true,
      maxHistoryLength: 100,
      enableRealtime: false,
      language: 'auto',
      enableEmoticonAnalysis: true,
      enableToneAnalysis: true,
      ...config
    };

    // 初始化词典和映射
    this.initializeEmotionDictionary();
    this.initializeNegationWords();
    this.initializeIntensityModifiers();
    this.initializeEmoticonMap();
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 初始化情感分析模型
      if (this.config.debug) {
        console.log('初始化AI情感分析系统...');
      }

      // 启动实时分析
      if (this.config.enableRealtime) {
        this.startRealtimeAnalysis();
      }

      this.initialized = true;

      if (this.config.debug) {
        console.log('AI情感分析系统初始化成功');
      }
    } catch (error) {
      console.error('初始化AI情感分析系统失败:', error);
      throw error;
    }
  }

  /**
   * 分析情感
   * @param text 文本
   * @param options 选项
   * @returns 情感分析结果
   */
  public async analyzeEmotion(
    text: string,
    options: EmotionAnalysisOptions = {}
  ): Promise<EmotionAnalysisResult | null> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const startTime = Date.now();

      // 预处理文本
      const processedText = this.preprocessText(text);

      // 根据配置的分析方法进行分析
      let result: EmotionAnalysisResult;

      switch (this.config.analysisMethod) {
        case AnalysisMethod.KEYWORD_MATCHING:
          result = this.keywordBasedAnalysis(processedText);
          break;
        case AnalysisMethod.SENTIMENT_ANALYSIS:
          result = this.sentimentBasedAnalysis(processedText);
          break;
        case AnalysisMethod.DEEP_LEARNING:
          result = await this.deepLearningAnalysis(processedText);
          break;
        case AnalysisMethod.HYBRID:
        default:
          result = this.hybridAnalysis(processedText);
          break;
      }

      // 如果分析结果置信度较低，使用简单分析作为备选
      if (result.confidence && result.confidence < 0.3) {
        const simpleResult = this.simpleEmotionAnalysis(processedText);
        if (simpleResult.confidence && simpleResult.confidence > result.confidence) {
          result = simpleResult;
          if (this.config.debug) {
            console.log('使用简单分析结果替代低置信度结果');
          }
        }
      }

      // 添加分析时间
      result.timestamp = Date.now();
      const analysisTime = Date.now() - startTime;

      // 处理详细选项
      if (options.detailed) {
        result.detailedEmotions = {
          confidence: result.confidence || 0.8,
          analysis_method: this.config.analysisMethod,
          text_length: text.length,
          analysis_time: analysisTime,
          language: this.detectLanguage(text),
          processed_text: processedText
        };
      }

      // 包含次要情感
      if (options.includeSecondary && result.scores) {
        const sortedEmotions = Object.entries(result.scores)
          .sort(([, a], [, b]) => b - a);

        if (sortedEmotions.length > 1) {
          result.secondaryEmotion = sortedEmotions[1][0];
          result.secondaryIntensity = sortedEmotions[1][1];
        }
      }

      // 包含情感变化
      if (options.includeChanges) {
        result.emotionChanges = this.analyzeEmotionChanges(text, result);
      }

      // 添加到历史记录
      if (this.config.enableHistory) {
        this.addToHistory(text, result, options.context);
      }

      // 触发事件
      this.eventEmitter.emit('emotionAnalyzed', {
        text,
        result,
        analysisTime
      });

      if (this.config.debug) {
        console.log(`情感分析完成: ${text} -> ${result.primaryEmotion} (${result.primaryIntensity.toFixed(2)})`);
      }

      return result;
    } catch (error) {
      console.error('情感分析失败:', error);
      this.eventEmitter.emit('emotionAnalysisError', { text, error });
      return null;
    }
  }

  /**
   * 简单的情感分析实现
   * @param text 文本
   * @returns 情感分析结果
   */
  private simpleEmotionAnalysis(text: string): EmotionAnalysisResult {
    // 简单的关键词匹配
    const happyKeywords = ['开心', '高兴', '快乐', '兴奋', '愉快', '喜悦', '满意'];
    const sadKeywords = ['伤心', '难过', '悲伤', '沮丧', '失望', '痛苦'];
    const angryKeywords = ['愤怒', '生气', '恼火', '愤慨', '暴怒', '气愤'];
    const surprisedKeywords = ['惊讶', '震惊', '意外', '吃惊', '惊奇'];
    const fearKeywords = ['害怕', '恐惧', '担心', '焦虑', '紧张'];
    const disgustKeywords = ['厌恶', '恶心', '讨厌', '反感'];

    const emotions = {
      happy: this.countKeywords(text, happyKeywords),
      sad: this.countKeywords(text, sadKeywords),
      angry: this.countKeywords(text, angryKeywords),
      surprised: this.countKeywords(text, surprisedKeywords),
      fearful: this.countKeywords(text, fearKeywords),
      disgusted: this.countKeywords(text, disgustKeywords)
    };

    // 找到最高分的情感
    let primaryEmotion = 'neutral';
    let maxScore = 0;
    
    for (const [emotion, score] of Object.entries(emotions)) {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion;
      }
    }

    // 计算强度（基于关键词数量）
    const intensity = Math.min(maxScore / 3, 1.0); // 最多3个关键词达到最大强度

    return {
      primaryEmotion,
      primaryIntensity: intensity,
      intensity,
      scores: emotions,
      confidence: maxScore > 0 ? 0.8 : 0.5
    };
  }

  /**
   * 计算关键词数量
   * @param text 文本
   * @param keywords 关键词列表
   * @returns 关键词数量
   */
  private countKeywords(text: string, keywords: string[]): number {
    let count = 0;
    for (const keyword of keywords) {
      if (text.includes(keyword)) {
        count++;
      }
    }
    return count;
  }

  /**
   * 初始化情感词典
   */
  private initializeEmotionDictionary(): void {
    this.emotionDictionary = [
      // 快乐相关
      { word: '开心', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '高兴', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '快乐', emotion: EmotionType.HAPPY, intensity: 0.9, weight: 1.0 },
      { word: '愉快', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '喜悦', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '满意', emotion: EmotionType.HAPPY, intensity: 0.6, weight: 1.0 },
      { word: '欣喜', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },

      // 兴奋相关
      { word: '兴奋', emotion: EmotionType.EXCITED, intensity: 0.9, weight: 1.0 },
      { word: '激动', emotion: EmotionType.EXCITED, intensity: 0.8, weight: 1.0 },
      { word: '狂欢', emotion: EmotionType.EXCITED, intensity: 0.9, weight: 1.0 },

      // 悲伤相关
      { word: '伤心', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '难过', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '悲伤', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '沮丧', emotion: EmotionType.SAD, intensity: 0.7, weight: 1.0 },
      { word: '失望', emotion: EmotionType.SAD, intensity: 0.7, weight: 1.0 },
      { word: '痛苦', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '忧郁', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },

      // 愤怒相关
      { word: '愤怒', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },
      { word: '生气', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },
      { word: '恼火', emotion: EmotionType.ANGRY, intensity: 0.7, weight: 1.0 },
      { word: '愤慨', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },
      { word: '暴怒', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },
      { word: '气愤', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },

      // 惊讶相关
      { word: '惊讶', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },
      { word: '震惊', emotion: EmotionType.SURPRISED, intensity: 0.9, weight: 1.0 },
      { word: '意外', emotion: EmotionType.SURPRISED, intensity: 0.7, weight: 1.0 },
      { word: '吃惊', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },
      { word: '惊奇', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },

      // 恐惧相关
      { word: '害怕', emotion: EmotionType.FEARFUL, intensity: 0.8, weight: 1.0 },
      { word: '恐惧', emotion: EmotionType.FEARFUL, intensity: 0.9, weight: 1.0 },
      { word: '担心', emotion: EmotionType.FEARFUL, intensity: 0.6, weight: 1.0 },
      { word: '焦虑', emotion: EmotionType.FEARFUL, intensity: 0.7, weight: 1.0 },
      { word: '紧张', emotion: EmotionType.FEARFUL, intensity: 0.6, weight: 1.0 },
      { word: '恐慌', emotion: EmotionType.FEARFUL, intensity: 0.9, weight: 1.0 },

      // 厌恶相关
      { word: '厌恶', emotion: EmotionType.DISGUSTED, intensity: 0.8, weight: 1.0 },
      { word: '恶心', emotion: EmotionType.DISGUSTED, intensity: 0.8, weight: 1.0 },
      { word: '讨厌', emotion: EmotionType.DISGUSTED, intensity: 0.7, weight: 1.0 },
      { word: '反感', emotion: EmotionType.DISGUSTED, intensity: 0.7, weight: 1.0 },

      // 平静相关
      { word: '平静', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },
      { word: '安静', emotion: EmotionType.CALM, intensity: 0.6, weight: 1.0 },
      { word: '宁静', emotion: EmotionType.CALM, intensity: 0.8, weight: 1.0 },
      { word: '放松', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },

      // 爱相关
      { word: '爱', emotion: EmotionType.LOVE, intensity: 0.9, weight: 1.0 },
      { word: '喜欢', emotion: EmotionType.LOVE, intensity: 0.7, weight: 1.0 },
      { word: '热爱', emotion: EmotionType.LOVE, intensity: 0.9, weight: 1.0 },
      { word: '深爱', emotion: EmotionType.LOVE, intensity: 0.9, weight: 1.0 },

      // 恨相关
      { word: '恨', emotion: EmotionType.HATE, intensity: 0.9, weight: 1.0 },
      { word: '憎恨', emotion: EmotionType.HATE, intensity: 0.9, weight: 1.0 },
      { word: '仇恨', emotion: EmotionType.HATE, intensity: 0.9, weight: 1.0 },

      // 其他情感
      { word: '困惑', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '迷茫', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '骄傲', emotion: EmotionType.PRIDE, intensity: 0.8, weight: 1.0 },
      { word: '自豪', emotion: EmotionType.PRIDE, intensity: 0.8, weight: 1.0 },
      { word: '羞耻', emotion: EmotionType.SHAME, intensity: 0.8, weight: 1.0 },
      { word: '内疚', emotion: EmotionType.GUILT, intensity: 0.8, weight: 1.0 },
      { word: '嫉妒', emotion: EmotionType.ENVY, intensity: 0.8, weight: 1.0 },
      { word: '感激', emotion: EmotionType.GRATITUDE, intensity: 0.8, weight: 1.0 },
      { word: '感谢', emotion: EmotionType.GRATITUDE, intensity: 0.7, weight: 1.0 },
      { word: '希望', emotion: EmotionType.HOPE, intensity: 0.8, weight: 1.0 },
      { word: '绝望', emotion: EmotionType.DESPAIR, intensity: 0.9, weight: 1.0 },
      { word: '解脱', emotion: EmotionType.RELIEF, intensity: 0.7, weight: 1.0 },
      { word: '期待', emotion: EmotionType.ANTICIPATION, intensity: 0.7, weight: 1.0 },
      { word: '无聊', emotion: EmotionType.BOREDOM, intensity: 0.6, weight: 1.0 },
      { word: '好奇', emotion: EmotionType.CURIOSITY, intensity: 0.7, weight: 1.0 },
      { word: '信任', emotion: EmotionType.TRUST, intensity: 0.8, weight: 1.0 },
      { word: '不信任', emotion: EmotionType.DISTRUST, intensity: 0.8, weight: 1.0 }
    ];
  }

  /**
   * 初始化否定词
   */
  private initializeNegationWords(): void {
    this.negationWords = new Set([
      '不', '没', '无', '非', '未', '否', '别', '勿',
      '不是', '没有', '不会', '不能', '不要', '不用',
      '并非', '并不', '绝不', '决不', '从不', '永不'
    ]);
  }

  /**
   * 初始化程度副词
   */
  private initializeIntensityModifiers(): void {
    this.intensityModifiers = new Map([
      // 增强程度
      ['非常', 1.5],
      ['特别', 1.4],
      ['极其', 1.6],
      ['十分', 1.3],
      ['相当', 1.2],
      ['很', 1.2],
      ['挺', 1.1],
      ['超级', 1.5],
      ['极度', 1.6],
      ['异常', 1.4],

      // 减弱程度
      ['有点', 0.7],
      ['稍微', 0.6],
      ['略微', 0.6],
      ['稍', 0.7],
      ['比较', 0.8],
      ['还算', 0.8],
      ['算是', 0.8],
      ['多少', 0.7],
      ['某种程度上', 0.7]
    ]);
  }

  /**
   * 初始化表情符号映射
   */
  private initializeEmoticonMap(): void {
    this.emoticonMap = new Map([
      // 快乐表情
      ['😊', { emotion: EmotionType.HAPPY, intensity: 0.8 }],
      ['😄', { emotion: EmotionType.HAPPY, intensity: 0.9 }],
      ['😃', { emotion: EmotionType.HAPPY, intensity: 0.8 }],
      ['😀', { emotion: EmotionType.HAPPY, intensity: 0.8 }],
      ['🙂', { emotion: EmotionType.HAPPY, intensity: 0.6 }],
      ['☺️', { emotion: EmotionType.HAPPY, intensity: 0.7 }],
      ['😌', { emotion: EmotionType.CALM, intensity: 0.7 }],

      // 悲伤表情
      ['😢', { emotion: EmotionType.SAD, intensity: 0.8 }],
      ['😭', { emotion: EmotionType.SAD, intensity: 0.9 }],
      ['😞', { emotion: EmotionType.SAD, intensity: 0.7 }],
      ['😔', { emotion: EmotionType.SAD, intensity: 0.7 }],
      ['😟', { emotion: EmotionType.SAD, intensity: 0.6 }],

      // 愤怒表情
      ['😠', { emotion: EmotionType.ANGRY, intensity: 0.8 }],
      ['😡', { emotion: EmotionType.ANGRY, intensity: 0.9 }],
      ['🤬', { emotion: EmotionType.ANGRY, intensity: 0.9 }],
      ['😤', { emotion: EmotionType.ANGRY, intensity: 0.7 }],

      // 惊讶表情
      ['😮', { emotion: EmotionType.SURPRISED, intensity: 0.8 }],
      ['😯', { emotion: EmotionType.SURPRISED, intensity: 0.8 }],
      ['😲', { emotion: EmotionType.SURPRISED, intensity: 0.9 }],

      // 恐惧表情
      ['😨', { emotion: EmotionType.FEARFUL, intensity: 0.8 }],
      ['😰', { emotion: EmotionType.FEARFUL, intensity: 0.8 }],
      ['😱', { emotion: EmotionType.FEARFUL, intensity: 0.9 }],

      // 厌恶表情
      ['🤢', { emotion: EmotionType.DISGUSTED, intensity: 0.8 }],
      ['🤮', { emotion: EmotionType.DISGUSTED, intensity: 0.9 }],
      ['😷', { emotion: EmotionType.DISGUSTED, intensity: 0.6 }],

      // 其他表情
      ['😴', { emotion: EmotionType.CALM, intensity: 0.8 }],
      ['🧘', { emotion: EmotionType.CALM, intensity: 0.9 }],
      ['😕', { emotion: EmotionType.CONFUSED, intensity: 0.7 }],
      ['😵‍💫', { emotion: EmotionType.CONFUSED, intensity: 0.8 }],
      ['🤔', { emotion: EmotionType.CONFUSED, intensity: 0.6 }],
      ['❤️', { emotion: EmotionType.LOVE, intensity: 0.9 }],
      ['💕', { emotion: EmotionType.LOVE, intensity: 0.8 }],
      ['💖', { emotion: EmotionType.LOVE, intensity: 0.9 }]
    ]);
  }

  /**
   * 更新系统
   * @param _deltaTime 时间间隔
   */
  public update(_deltaTime: number): void {
    // 情感分析系统通常不需要每帧更新
    // 可以在这里处理实时分析队列等
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.initialized = false;
    this.emotionHistory = [];
    this.eventEmitter.removeAllListeners();

    // 停止实时分析
    this.stopRealtimeAnalysis();
  }

  /**
   * 启动实时分析
   */
  private startRealtimeAnalysis(): void {
    if (this.realtimeTimer) {
      return;
    }

    // 每100ms处理一次实时分析队列
    this.realtimeTimer = setInterval(() => {
      this.processRealtimeQueue();
    }, 100);

    if (this.config.debug) {
      console.log('实时情感分析已启动');
    }
  }

  /**
   * 停止实时分析
   */
  private stopRealtimeAnalysis(): void {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
      this.realtimeTimer = null;
    }

    // 清空队列
    this.realtimeQueue = [];

    if (this.config.debug) {
      console.log('实时情感分析已停止');
    }
  }

  /**
   * 处理实时分析队列
   */
  private processRealtimeQueue(): void {
    if (this.realtimeQueue.length === 0) {
      return;
    }

    const item = this.realtimeQueue.shift()!;

    // 异步处理分析
    this.analyzeEmotion(item.text, { detailed: false })
      .then(result => {
        if (item.callback) {
          item.callback(result);
        }

        // 触发实时分析事件
        this.eventEmitter.emit('realtimeEmotionAnalyzed', {
          text: item.text,
          result,
          timestamp: item.timestamp
        });
      })
      .catch(error => {
        console.error('实时情感分析失败:', error);
        if (item.callback) {
          item.callback(null);
        }
      });
  }

  /**
   * 添加到实时分析队列
   */
  public addToRealtimeQueue(text: string, callback?: (result: EmotionAnalysisResult | null) => void): void {
    if (!this.config.enableRealtime) {
      console.warn('实时分析未启用');
      return;
    }

    this.realtimeQueue.push({
      text,
      timestamp: Date.now(),
      callback
    });

    // 限制队列长度，避免内存溢出
    if (this.realtimeQueue.length > 100) {
      this.realtimeQueue.shift();
    }
  }

  // ==================== 分析方法 ====================

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fff\w\s\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, ' ') // 保留中文、英文、数字和表情符号
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 检测语言
   */
  private detectLanguage(text: string): string {
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    const englishChars = text.match(/[a-zA-Z]/g);

    const chineseRatio = chineseChars ? chineseChars.length / text.length : 0;
    const englishRatio = englishChars ? englishChars.length / text.length : 0;

    if (chineseRatio > 0.3) return 'zh';
    if (englishRatio > 0.5) return 'en';
    return 'mixed';
  }

  /**
   * 基于关键词的分析
   */
  private keywordBasedAnalysis(text: string): EmotionAnalysisResult {
    const emotionScores: Record<string, number> = {};

    // 初始化所有情感分数
    Object.values(EmotionType).forEach(emotion => {
      emotionScores[emotion] = 0;
    });

    // 分词（简单的空格分割）
    const tokens = text.split(/\s+/);

    // 分析每个词
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];

      // 查找匹配的情感词
      const matchedWords = this.emotionDictionary.filter(item =>
        token.includes(item.word) || item.word.includes(token)
      );

      for (const emotionWord of matchedWords) {
        let intensity = emotionWord.intensity * emotionWord.weight;

        // 检查程度副词
        if (i > 0) {
          const modifier = this.intensityModifiers.get(tokens[i - 1]);
          if (modifier) {
            intensity *= modifier;
          }
        }

        // 检查否定词
        const hasNegation = this.checkNegation(tokens, i);
        if (hasNegation) {
          intensity *= -0.5; // 否定会降低原情感并可能产生相反情感
        }

        emotionScores[emotionWord.emotion] += intensity;
      }
    }

    // 分析表情符号
    if (this.config.enableEmoticonAnalysis) {
      this.analyzeEmoticons(text, emotionScores);
    }

    // 分析语调
    if (this.config.enableToneAnalysis) {
      this.analyzeTone(text, emotionScores);
    }

    return this.processEmotionScores(emotionScores);
  }

  /**
   * 基于情感倾向的分析
   */
  private sentimentBasedAnalysis(text: string): EmotionAnalysisResult {
    // 这里可以集成更复杂的情感分析算法
    // 目前使用简化版本
    const result = this.keywordBasedAnalysis(text);

    // 添加情感倾向调整
    const positiveWords = ['好', '棒', '赞', '优秀', '完美', '满意'];
    const negativeWords = ['坏', '差', '糟', '失败', '问题', '错误'];

    let sentimentScore = 0;
    positiveWords.forEach(word => {
      if (text.includes(word)) sentimentScore += 0.2;
    });
    negativeWords.forEach(word => {
      if (text.includes(word)) sentimentScore -= 0.2;
    });

    // 调整情感分数
    if (sentimentScore > 0) {
      result.scores![EmotionType.HAPPY] = (result.scores![EmotionType.HAPPY] || 0) + sentimentScore;
    } else if (sentimentScore < 0) {
      result.scores![EmotionType.SAD] = (result.scores![EmotionType.SAD] || 0) + Math.abs(sentimentScore);
    }

    return this.processEmotionScores(result.scores!);
  }

  /**
   * 深度学习分析
   */
  private async deepLearningAnalysis(text: string): Promise<EmotionAnalysisResult> {
    try {
      if (this.config.useLocalModel && this.config.modelPath) {
        // 使用本地模型进行分析
        return await this.analyzeWithLocalModel(text);
      } else {
        // 使用远程API进行深度学习分析
        return await this.analyzeWithRemoteAPI(text);
      }
    } catch (error) {
      console.warn('深度学习分析失败，使用混合分析代替:', error);
      return this.hybridAnalysis(text);
    }
  }

  /**
   * 使用本地模型分析
   */
  private async analyzeWithLocalModel(text: string): Promise<EmotionAnalysisResult> {
    // 模拟本地模型分析过程
    if (this.config.debug) {
      console.log(`使用本地模型分析: ${this.config.modelPath}`);
    }

    // 这里应该加载和使用实际的深度学习模型
    // 目前使用增强的关键词分析作为模拟
    const baseResult = this.keywordBasedAnalysis(text);

    // 模拟深度学习的更精确分析
    const enhancedScores: Record<string, number> = {};
    Object.entries(baseResult.scores!).forEach(([emotion, score]) => {
      // 深度学习模型通常能提供更平滑和准确的分数
      enhancedScores[emotion] = score * (0.8 + Math.random() * 0.4); // 添加一些随机性模拟模型不确定性
    });

    const result = this.processEmotionScores(enhancedScores);
    result.confidence = Math.min((result.confidence || 0.5) * 1.2, 1.0); // 深度学习通常有更高置信度

    return result;
  }

  /**
   * 使用远程API分析
   */
  private async analyzeWithRemoteAPI(text: string): Promise<EmotionAnalysisResult> {
    // 模拟远程API调用
    if (this.config.debug) {
      console.log('使用远程API进行深度学习分析');
    }

    // 这里应该调用实际的远程情感分析API
    // 目前使用增强的混合分析作为模拟
    const result = this.hybridAnalysis(text);

    // 模拟API返回的额外信息
    result.detailedEmotions = {
      ...result.detailedEmotions,
      api_version: '1.0',
      model_type: 'transformer',
      processing_time: Math.random() * 100 + 50 // 模拟API处理时间
    };

    return result;
  }

  /**
   * 混合分析
   */
  private hybridAnalysis(text: string): EmotionAnalysisResult {
    // 结合多种分析方法
    const keywordResult = this.keywordBasedAnalysis(text);
    const sentimentResult = this.sentimentBasedAnalysis(text);

    // 合并结果（加权平均）
    const combinedScores: Record<string, number> = {};
    Object.values(EmotionType).forEach(emotion => {
      const keywordScore = keywordResult.scores![emotion] || 0;
      const sentimentScore = sentimentResult.scores![emotion] || 0;
      combinedScores[emotion] = (keywordScore * 0.7 + sentimentScore * 0.3);
    });

    return this.processEmotionScores(combinedScores);
  }

  /**
   * 处理情感分数
   */
  private processEmotionScores(scores: Record<string, number>): EmotionAnalysisResult {
    // 找出主要情感
    let primaryEmotion = EmotionType.NEUTRAL;
    let maxScore = 0;

    Object.entries(scores).forEach(([emotion, score]) => {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion as EmotionType;
      }
    });

    // 计算强度
    const intensity = Math.min(Math.max(maxScore, 0), 1);

    // 计算置信度
    const totalScore = Object.values(scores).reduce((sum, score) => sum + Math.abs(score), 0);
    const confidence = totalScore > 0 ? maxScore / totalScore : 0.5;

    return {
      primaryEmotion,
      primaryIntensity: intensity,
      intensity,
      scores,
      confidence: Math.min(Math.max(confidence, 0), 1),
      timestamp: Date.now()
    };
  }

  /**
   * 检查否定词
   */
  private checkNegation(tokens: string[], currentIndex: number): boolean {
    // 检查前面2个词是否有否定词
    for (let i = Math.max(0, currentIndex - 2); i < currentIndex; i++) {
      if (this.negationWords.has(tokens[i])) {
        return true;
      }
    }
    return false;
  }

  /**
   * 分析表情符号
   */
  private analyzeEmoticons(text: string, scores: Record<string, number>): void {
    Array.from(this.emoticonMap.entries()).forEach(([emoticon, data]) => {
      const count = (text.match(new RegExp(emoticon, 'g')) || []).length;
      if (count > 0) {
        scores[data.emotion] = (scores[data.emotion] || 0) + data.intensity * count;
      }
    });
  }

  /**
   * 分析语调
   */
  private analyzeTone(text: string, scores: Record<string, number>): void {
    // 分析标点符号的语调影响
    const exclamationCount = (text.match(/!/g) || []).length;
    const questionCount = (text.match(/\?/g) || []).length;
    const ellipsisCount = (text.match(/\.\.\./g) || []).length;
    const capsCount = (text.match(/[A-Z]{2,}/g) || []).length;

    // 感叹号通常表示强烈情感
    if (exclamationCount > 0) {
      const intensity = Math.min(exclamationCount * 0.2, 0.6);
      scores[EmotionType.EXCITED] = (scores[EmotionType.EXCITED] || 0) + intensity;

      // 如果有愤怒相关词汇，感叹号会增强愤怒
      if (scores[EmotionType.ANGRY] > 0) {
        scores[EmotionType.ANGRY] += intensity * 0.5;
      }
    }

    // 问号可能表示困惑或好奇
    if (questionCount > 0) {
      const intensity = Math.min(questionCount * 0.15, 0.4);
      scores[EmotionType.CONFUSED] = (scores[EmotionType.CONFUSED] || 0) + intensity;
      scores[EmotionType.CURIOSITY] = (scores[EmotionType.CURIOSITY] || 0) + intensity;
    }

    // 省略号通常表示犹豫、沉思或悲伤
    if (ellipsisCount > 0) {
      const intensity = Math.min(ellipsisCount * 0.2, 0.5);
      scores[EmotionType.SAD] = (scores[EmotionType.SAD] || 0) + intensity * 0.3;
      scores[EmotionType.CONFUSED] = (scores[EmotionType.CONFUSED] || 0) + intensity * 0.2;
    }

    // 大写字母通常表示强调或愤怒
    if (capsCount > 0) {
      const intensity = Math.min(capsCount * 0.3, 0.7);
      scores[EmotionType.ANGRY] = (scores[EmotionType.ANGRY] || 0) + intensity;
      scores[EmotionType.EXCITED] = (scores[EmotionType.EXCITED] || 0) + intensity * 0.5;
    }

    // 分析重复字符（如"哈哈哈"、"呜呜呜"）
    const repeatedChars = text.match(/(.)\1{2,}/g);
    if (repeatedChars) {
      repeatedChars.forEach(match => {
        const char = match[0];
        const length = match.length;
        const intensity = Math.min(length * 0.1, 0.5);

        // 根据重复的字符类型判断情感
        if (['哈', 'h', 'H'].includes(char)) {
          scores[EmotionType.HAPPY] = (scores[EmotionType.HAPPY] || 0) + intensity;
        } else if (['呜', '呵', '嘿'].includes(char)) {
          scores[EmotionType.SAD] = (scores[EmotionType.SAD] || 0) + intensity;
        } else if (['啊', 'a', 'A'].includes(char)) {
          scores[EmotionType.SURPRISED] = (scores[EmotionType.SURPRISED] || 0) + intensity;
        }
      });
    }

    // 分析语气词
    const toneWords = [
      { words: ['呀', '啊', '哇', '哎呀'], emotion: EmotionType.SURPRISED, intensity: 0.3 },
      { words: ['哈', '嘿', '嘻'], emotion: EmotionType.HAPPY, intensity: 0.4 },
      { words: ['唉', '哎', '呜'], emotion: EmotionType.SAD, intensity: 0.4 },
      { words: ['哼', '切', '呸'], emotion: EmotionType.ANGRY, intensity: 0.5 },
      { words: ['嗯', '哦', '额'], emotion: EmotionType.NEUTRAL, intensity: 0.2 }
    ];

    toneWords.forEach(({ words, emotion, intensity }) => {
      words.forEach(word => {
        const count = (text.match(new RegExp(word, 'g')) || []).length;
        if (count > 0) {
          scores[emotion] = (scores[emotion] || 0) + intensity * count;
        }
      });
    });
  }

  /**
   * 分析情感变化
   */
  private analyzeEmotionChanges(text: string, currentResult: EmotionAnalysisResult): { emotion: string, intensity: number, time: number }[] {
    const changes: { emotion: string, intensity: number, time: number }[] = [];

    // 获取最近的历史记录进行对比
    const recentHistory = this.getEmotionHistory(5);

    if (recentHistory.length === 0) {
      // 没有历史记录，返回当前情感作为初始变化
      changes.push({
        emotion: currentResult.primaryEmotion,
        intensity: currentResult.primaryIntensity,
        time: Date.now()
      });
      return changes;
    }

    // 分析情感转换模式
    const lastResult = recentHistory[recentHistory.length - 1].result;

    // 检查主要情感是否发生变化
    if (lastResult.primaryEmotion !== currentResult.primaryEmotion) {
      changes.push({
        emotion: currentResult.primaryEmotion,
        intensity: currentResult.primaryIntensity,
        time: Date.now()
      });
    }

    // 检查情感强度的显著变化
    const intensityDiff = Math.abs(currentResult.primaryIntensity - lastResult.primaryIntensity);
    if (intensityDiff > 0.3) { // 强度变化超过0.3认为是显著变化
      changes.push({
        emotion: currentResult.primaryEmotion,
        intensity: currentResult.primaryIntensity,
        time: Date.now()
      });
    }

    // 分析文本中的情感转折词
    const transitionWords = [
      { words: ['但是', '不过', '然而', '可是'], type: 'contrast' },
      { words: ['突然', '忽然', '瞬间', '立刻'], type: 'sudden' },
      { words: ['渐渐', '慢慢', '逐渐', '越来越'], type: 'gradual' },
      { words: ['最后', '终于', '结果', '最终'], type: 'conclusion' }
    ];

    transitionWords.forEach(({ words, type }) => {
      words.forEach(word => {
        if (text.includes(word)) {
          // 根据转折词类型调整情感变化的权重
          const weight = type === 'sudden' ? 1.2 : type === 'gradual' ? 0.8 : 1.0;

          if (changes.length > 0) {
            changes[changes.length - 1].intensity *= weight;
          }
        }
      });
    });

    return changes;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(text: string, result: EmotionAnalysisResult, context?: string): void {
    const historyItem: EmotionHistory = {
      timestamp: Date.now(),
      text,
      result,
      context
    };

    this.emotionHistory.push(historyItem);

    // 保持历史记录在限制范围内
    if (this.emotionHistory.length > this.config.maxHistoryLength!) {
      this.emotionHistory.shift();
    }
  }

  // ==================== 公共方法 ====================

  /**
   * 获取情感历史记录
   */
  public getEmotionHistory(limit?: number): EmotionHistory[] {
    if (limit) {
      return this.emotionHistory.slice(-limit);
    }
    return [...this.emotionHistory];
  }

  /**
   * 清除情感历史记录
   */
  public clearEmotionHistory(): void {
    this.emotionHistory = [];
    this.eventEmitter.emit('historyCleared');
  }

  /**
   * 获取情感统计
   */
  public getEmotionStatistics(timeRange?: number): Record<string, any> {
    const now = Date.now();
    const cutoff = timeRange ? now - timeRange : 0;

    const relevantHistory = this.emotionHistory.filter(item => item.timestamp > cutoff);

    const emotionCounts: Record<string, number> = {};
    const emotionIntensities: Record<string, number[]> = {};

    relevantHistory.forEach(item => {
      const emotion = item.result.primaryEmotion;
      emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;

      if (!emotionIntensities[emotion]) {
        emotionIntensities[emotion] = [];
      }
      emotionIntensities[emotion].push(item.result.primaryIntensity);
    });

    const statistics: Record<string, any> = {
      totalAnalyses: relevantHistory.length,
      emotionCounts,
      averageIntensities: {}
    };

    Object.entries(emotionIntensities).forEach(([emotion, intensities]) => {
      statistics.averageIntensities[emotion] =
        intensities.reduce((sum, intensity) => sum + intensity, 0) / intensities.length;
    });

    return statistics;
  }

  /**
   * 批量分析情感
   */
  public async batchAnalyzeEmotions(
    texts: string[],
    options: EmotionAnalysisOptions = {}
  ): Promise<EmotionAnalysisResult[]> {
    const results: EmotionAnalysisResult[] = [];

    for (const text of texts) {
      try {
        const result = await this.analyzeEmotion(text, options);
        if (result) {
          results.push(result);
        }
      } catch (error) {
        console.error(`批量分析失败: ${text}`, error);
      }
    }

    return results;
  }

  /**
   * 事件监听
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取支持的情感类型
   */
  public getSupportedEmotions(): EmotionType[] {
    return Object.values(EmotionType);
  }

  /**
   * 获取系统配置
   */
  public getConfig(): AIEmotionAnalysisSystemConfig {
    return { ...this.config };
  }

  /**
   * 更新系统配置
   */
  public updateConfig(newConfig: Partial<AIEmotionAnalysisSystemConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果实时分析设置发生变化，重新启动或停止
    if (oldConfig.enableRealtime !== this.config.enableRealtime) {
      if (this.config.enableRealtime) {
        this.startRealtimeAnalysis();
      } else {
        this.stopRealtimeAnalysis();
      }
    }

    if (this.config.debug) {
      console.log('情感分析系统配置已更新:', this.config);
    }
  }

  /**
   * 获取情感词典
   */
  public getEmotionDictionary(): EmotionWord[] {
    return [...this.emotionDictionary];
  }

  /**
   * 添加自定义情感词
   */
  public addEmotionWord(word: EmotionWord): void {
    this.emotionDictionary.push(word);

    if (this.config.debug) {
      console.log(`添加情感词: ${word.word} -> ${word.emotion}`);
    }
  }

  /**
   * 批量添加情感词
   */
  public addEmotionWords(words: EmotionWord[]): void {
    this.emotionDictionary.push(...words);

    if (this.config.debug) {
      console.log(`批量添加 ${words.length} 个情感词`);
    }
  }

  /**
   * 移除情感词
   */
  public removeEmotionWord(word: string): boolean {
    const index = this.emotionDictionary.findIndex(item => item.word === word);
    if (index !== -1) {
      this.emotionDictionary.splice(index, 1);

      if (this.config.debug) {
        console.log(`移除情感词: ${word}`);
      }
      return true;
    }
    return false;
  }

  /**
   * 获取系统状态
   */
  public getSystemStatus(): {
    initialized: boolean;
    realtimeEnabled: boolean;
    queueLength: number;
    historyLength: number;
    dictionarySize: number;
  } {
    return {
      initialized: this.initialized,
      realtimeEnabled: this.config.enableRealtime || false,
      queueLength: this.realtimeQueue.length,
      historyLength: this.emotionHistory.length,
      dictionarySize: this.emotionDictionary.length
    };
  }

  /**
   * 重置系统
   */
  public reset(): void {
    this.clearEmotionHistory();
    this.realtimeQueue = [];

    // 重新初始化词典
    this.initializeEmotionDictionary();
    this.initializeNegationWords();
    this.initializeIntensityModifiers();
    this.initializeEmoticonMap();

    if (this.config.debug) {
      console.log('情感分析系统已重置');
    }

    this.eventEmitter.emit('systemReset');
  }
}
