/**
 * 世界类
 * 负责管理实体和场景
 */
import { Engine } from './Engine';
import { Entity } from './Entity';
import { Scene } from '../scene/Scene';
import { System } from './System';
import { EventEmitter } from '../utils/EventEmitter';
import { generateUUID } from '../utils/UUID';

/**
 * 世界状态枚举
 */
export enum WorldState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 运行中 */
  RUNNING = 'running',
  /** 已暂停 */
  PAUSED = 'paused',
  /** 已销毁 */
  DISPOSED = 'disposed'
}

/**
 * 世界性能统计
 */
export interface WorldPerformanceStats {
  /** 实体数量 */
  entityCount: number;
  /** 活跃实体数量 */
  activeEntityCount: number;
  /** 场景数量 */
  sceneCount: number;
  /** 系统数量 */
  systemCount: number;
  /** 活跃系统数量 */
  activeSystemCount: number;
  /** 总更新时间（毫秒） */
  totalUpdateTime: number;
  /** 平均更新时间（毫秒） */
  averageUpdateTime: number;
  /** 最大更新时间（毫秒） */
  maxUpdateTime: number;
  /** 帧率 */
  fps: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
}

/**
 * 实体查询缓存
 */
interface EntityQueryCache {
  /** 按组件类型索引的实体 */
  componentIndex: Map<string, Set<Entity>>;
  /** 按标签索引的实体 */
  tagIndex: Map<string, Set<Entity>>;
  /** 按名称索引的实体 */
  nameIndex: Map<string, Set<Entity>>;
  /** 缓存是否有效 */
  valid: boolean;
}

export class World extends EventEmitter {
  /** 引擎实例 */
  private engine: Engine;

  /** 实体映射 */
  private entities: Map<string, Entity> = new Map();

  /** 当前场景 */
  private activeScene: Scene | null = null;

  /** 场景映射 */
  private scenes: Map<string, Scene> = new Map();

  /** 系统列表 */
  private systems: System[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 世界状态 */
  private state: WorldState = WorldState.UNINITIALIZED;

  /** 是否暂停 */
  private paused: boolean = false;

  /** 性能统计 */
  private performanceStats: WorldPerformanceStats;

  /** 实体查询缓存 */
  private queryCache: EntityQueryCache;

  /** 更新计数器 */
  private updateCount: number = 0;

  /** 上次更新时间 */
  private lastUpdateTime: number = 0;

  /** FPS计算相关 */
  private fpsCounter: number = 0;
  private fpsLastTime: number = 0;

  /** 事件过滤器 */
  private eventFilters: Map<string, ((event: any) => boolean)[]> = new Map();

  /**
   * 创建世界实例
   * @param engine 引擎实例
   */
  constructor(engine: Engine) {
    super();
    this.engine = engine;

    // 初始化性能统计
    this.performanceStats = {
      entityCount: 0,
      activeEntityCount: 0,
      sceneCount: 0,
      systemCount: 0,
      activeSystemCount: 0,
      totalUpdateTime: 0,
      averageUpdateTime: 0,
      maxUpdateTime: 0,
      fps: 0,
      memoryUsage: 0
    };

    // 初始化查询缓存
    this.queryCache = {
      componentIndex: new Map(),
      tagIndex: new Map(),
      nameIndex: new Map(),
      valid: false
    };

    // 初始化时间
    this.lastUpdateTime = performance.now();
    this.fpsLastTime = this.lastUpdateTime;
  }

  /**
   * 初始化世界
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.state = WorldState.INITIALIZING;

    // 创建默认场景
    const defaultScene = new Scene('默认场景');
    this.addScene(defaultScene);
    this.setActiveScene(defaultScene);

    // 初始化查询缓存
    this.invalidateQueryCache();

    this.initialized = true;
    this.state = WorldState.RUNNING;
    this.emit('initialized');
  }

  /**
   * 更新世界
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 检查是否暂停
    if (this.paused || this.state !== WorldState.RUNNING) {
      return;
    }

    const startTime = performance.now();

    // 更新所有系统
    for (const system of this.systems) {
      if (system.isEnabled()) {
        system.update(deltaTime);
      }
    }

    // 更新所有实体
    for (const entity of Array.from(this.entities.values())) {
      if (entity.isActive()) {
        entity.update(deltaTime);
      }
    }

    // 更新当前场景
    if (this.activeScene) {
      this.activeScene.update(deltaTime);
    }

    // 更新性能统计
    this.updatePerformanceStats(performance.now() - startTime);
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    // 固定更新所有系统
    for (const system of this.systems) {
      if (system.isEnabled()) {
        system.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新所有实体
    for (const entity of Array.from(this.entities.values())) {
      if (entity.isActive()) {
        entity.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新当前场景
    if (this.activeScene) {
      this.activeScene.fixedUpdate(fixedDeltaTime);
    }
  }

  /**
   * 创建实体
   * @param name 实体名称
   * @returns 创建的实体
   */
  public createEntity(name: string = '实体'): Entity {
    const entity = new Entity(name);
    this.addEntity(entity);
    return entity;
  }

  /**
   * 添加实体
   * @param entity 实体实例
   * @returns 添加的实体
   */
  public addEntity(entity: Entity): Entity {
    // 如果实体已经有ID，则使用该ID，否则生成新ID
    if (!entity.id) {
      entity.id = generateUUID();
    }

    // 设置实体的世界引用
    entity.setWorld(this);

    // 添加到实体映射
    this.entities.set(entity.id, entity);

    // 如果有活跃场景，将实体添加到场景
    if (this.activeScene) {
      this.activeScene.addEntity(entity);
    }

    // 更新查询缓存
    this.addEntityToCache(entity);

    // 发出实体创建事件
    this.emit('entityCreated', entity);

    return entity;
  }

  /**
   * 获取实体
   * @param id 实体ID
   * @returns 实体实例，如果不存在则返回null
   */
  public getEntity(id: string): Entity | null {
    return this.entities.get(id) || null;
  }

  /**
   * 移除实体
   * @param entity 实体实例或ID
   * @returns 是否成功移除
   */
  public removeEntity(entity: Entity | string): boolean {
    const id = typeof entity === 'string' ? entity : entity.id;

    if (!id || !this.entities.has(id)) {
      return false;
    }

    const entityToRemove = this.entities.get(id)!;

    // 如果有活跃场景，从场景中移除实体
    if (this.activeScene) {
      this.activeScene.removeEntity(entityToRemove);
    }

    // 从查询缓存中移除
    this.removeEntityFromCache(entityToRemove);

    // 销毁实体
    (entityToRemove as any).dispose();

    // 从实体映射中移除
    this.entities.delete(id);

    // 发出实体移除事件
    this.emit('entityRemoved', entityToRemove);

    return true;
  }

  /**
   * 获取所有实体
   * @returns 实体数组
   */
  public getAllEntities(): Entity[] {
    return Array.from(this.entities.values());
  }

  /**
   * 根据名称查找实体
   * @param name 实体名称
   * @returns 匹配的实体数组
   */
  public findEntitiesByName(name: string): Entity[] {
    const result: Entity[] = [];

    for (const entity of Array.from(this.entities.values())) {
      if (entity.name === name) {
        result.push(entity);
      }
    }

    return result;
  }

  /**
   * 根据标签查找实体（优化版本，使用缓存）
   * @param tag 实体标签
   * @returns 匹配的实体数组
   */
  public findEntitiesByTag(tag: string): Entity[] {
    this.rebuildQueryCache();
    const entitySet = this.queryCache.tagIndex.get(tag);
    return entitySet ? Array.from(entitySet) : [];
  }

  /**
   * 根据组件类型查找实体（优化版本，使用缓存）
   * @param componentType 组件类型
   * @returns 匹配的实体数组
   */
  public findEntitiesByComponent(componentType: string): Entity[] {
    this.rebuildQueryCache();
    const entitySet = this.queryCache.componentIndex.get(componentType);
    return entitySet ? Array.from(entitySet) : [];
  }

  /**
   * 根据多个组件类型查找实体
   * @param componentTypes 组件类型数组
   * @returns 匹配的实体数组
   */
  public findEntitiesByComponents(componentTypes: string[]): Entity[] {
    if (componentTypes.length === 0) {
      return [];
    }

    this.rebuildQueryCache();

    // 获取第一个组件类型的实体集合
    let result = this.queryCache.componentIndex.get(componentTypes[0]);
    if (!result) {
      return [];
    }

    // 与其他组件类型的实体集合求交集
    for (let i = 1; i < componentTypes.length; i++) {
      const entitySet = this.queryCache.componentIndex.get(componentTypes[i]);
      if (!entitySet) {
        return [];
      }

      result = new Set([...result].filter(entity => entitySet.has(entity)));
      if (result.size === 0) {
        return [];
      }
    }

    return Array.from(result);
  }

  /**
   * 创建场景
   * @param name 场景名称
   * @returns 创建的场景
   */
  public createScene(name: string = '场景'): Scene {
    const scene = new Scene(name);
    this.addScene(scene);
    return scene;
  }

  /**
   * 添加场景
   * @param scene 场景实例
   * @returns 添加的场景
   */
  public addScene(scene: Scene): Scene {
    // 如果场景已经有ID，则使用该ID，否则生成新ID
    if (!scene.id) {
      scene.id = generateUUID();
    }

    // 设置场景的世界引用
    scene.setWorld(this);

    // 添加到场景映射
    this.scenes.set(scene.id, scene);

    // 发出场景添加事件
    this.emit('sceneAdded', scene);

    return scene;
  }

  /**
   * 获取场景
   * @param id 场景ID
   * @returns 场景实例，如果不存在则返回null
   */
  public getScene(id: string): Scene | null {
    return this.scenes.get(id) || null;
  }

  /**
   * 移除场景
   * @param scene 场景实例或ID
   * @returns 是否成功移除
   */
  public removeScene(scene: Scene | string): boolean {
    const id = typeof scene === 'string' ? scene : scene.id;

    if (!id || !this.scenes.has(id)) {
      return false;
    }

    const sceneToRemove = this.scenes.get(id)!;

    // 如果是当前活跃场景，则清除活跃场景
    if (this.activeScene === sceneToRemove) {
      this.activeScene = null;
    }

    // 销毁场景
    (sceneToRemove as any).dispose();

    // 从场景映射中移除
    this.scenes.delete(id);

    // 发出场景移除事件
    this.emit('sceneRemoved', sceneToRemove);

    return true;
  }

  /**
   * 获取所有场景
   * @returns 场景数组
   */
  public getAllScenes(): Scene[] {
    return Array.from(this.scenes.values());
  }

  /**
   * 设置活跃场景
   * @param scene 场景实例或ID
   * @returns 是否成功设置
   */
  public setActiveScene(scene: Scene | string): boolean {
    const targetScene = typeof scene === 'string' ? this.getScene(scene) : scene;

    if (!targetScene) {
      return false;
    }

    // 如果已经是活跃场景，则不做任何操作
    if (this.activeScene === targetScene) {
      return true;
    }

    // 保存旧场景
    const oldScene = this.activeScene;

    // 设置新的活跃场景
    this.activeScene = targetScene;

    // 发出场景切换事件
    this.emit('sceneChanged', targetScene, oldScene);

    return true;
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景实例
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取引擎实例
   * @returns 引擎实例
   */
  public getEngine(): Engine {
    return this.engine;
  }

  /**
   * 添加系统
   * @param system 系统实例
   * @returns 添加的系统
   */
  public addSystem(system: System): System {
    // 设置系统的世界引用
    system.setWorld(this);

    // 添加到系统列表
    this.systems.push(system);

    // 按优先级排序
    this.systems.sort((a, b) => a.getPriority() - b.getPriority());

    // 初始化系统
    system.initialize();

    // 发出系统添加事件
    this.emit('systemAdded', system);

    return system;
  }

  /**
   * 获取系统
   * @param systemClass 系统类
   * @returns 系统实例，如果不存在则返回null
   */
  public getSystem<T extends System>(systemClass: new (...args: any[]) => T): T | null {
    for (const system of this.systems) {
      if (system instanceof systemClass) {
        return system as T;
      }
    }
    return null;
  }

  /**
   * 移除系统
   * @param system 系统实例或类
   * @returns 是否成功移除
   */
  public removeSystem(system: System | (new (...args: any[]) => System)): boolean {
    let index = -1;

    if (typeof system === 'function') {
      // 如果是类，查找该类的实例
      index = this.systems.findIndex(s => s instanceof system);
    } else {
      // 如果是实例，直接查找
      index = this.systems.indexOf(system);
    }

    if (index !== -1) {
      const removedSystem = this.systems[index];
      (removedSystem as any).dispose();
      this.systems.splice(index, 1);

      // 发出系统移除事件
      this.emit('systemRemoved', removedSystem);

      return true;
    }

    return false;
  }

  /**
   * 获取所有系统
   * @returns 系统数组
   */
  public getSystems(): System[] {
    return [...this.systems];
  }

  /**
   * 获取所有实体
   * @returns 实体映射
   */
  public getEntities(): Map<string, Entity> {
    return this.entities;
  }

  /**
   * 清空世界
   */
  public clear(): void {
    // 移除所有系统
    for (const system of Array.from(this.systems)) {
      (system as any).dispose();
    }
    this.systems.length = 0;

    // 移除所有实体
    for (const entity of Array.from(this.entities.values())) {
      (entity as any).dispose();
    }
    this.entities.clear();

    // 移除所有场景
    for (const scene of Array.from(this.scenes.values())) {
      (scene as any).dispose();
    }
    this.scenes.clear();

    // 清除活跃场景
    this.activeScene = null;

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁世界
   */
  public dispose(): void {
    // 清空世界
    this.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
    this.state = WorldState.DISPOSED;
  }

  /**
   * 暂停世界
   */
  public pause(): void {
    if (this.state === WorldState.RUNNING) {
      this.paused = true;
      this.state = WorldState.PAUSED;
      this.emit('paused');
    }
  }

  /**
   * 恢复世界
   */
  public resume(): void {
    if (this.state === WorldState.PAUSED) {
      this.paused = false;
      this.state = WorldState.RUNNING;
      this.emit('resumed');
    }
  }

  /**
   * 获取世界状态
   * @returns 世界状态
   */
  public getState(): WorldState {
    return this.state;
  }

  /**
   * 是否暂停
   * @returns 是否暂停
   */
  public isPaused(): boolean {
    return this.paused;
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): WorldPerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 更新性能统计
   * @param updateTime 更新时间（毫秒）
   */
  private updatePerformanceStats(updateTime: number): void {
    this.updateCount++;
    this.performanceStats.totalUpdateTime += updateTime;
    this.performanceStats.averageUpdateTime = this.performanceStats.totalUpdateTime / this.updateCount;

    if (updateTime > this.performanceStats.maxUpdateTime) {
      this.performanceStats.maxUpdateTime = updateTime;
    }

    // 更新实体和系统统计
    this.performanceStats.entityCount = this.entities.size;
    this.performanceStats.activeEntityCount = Array.from(this.entities.values()).filter(e => e.isActive()).length;
    this.performanceStats.sceneCount = this.scenes.size;
    this.performanceStats.systemCount = this.systems.length;
    this.performanceStats.activeSystemCount = this.systems.filter(s => s.isEnabled()).length;

    // 计算FPS
    const currentTime = performance.now();
    this.fpsCounter++;
    if (currentTime - this.fpsLastTime >= 1000) {
      this.performanceStats.fps = this.fpsCounter;
      this.fpsCounter = 0;
      this.fpsLastTime = currentTime;
    }

    // 更新内存使用量
    if ((performance as any).memory) {
      this.performanceStats.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }
  }

  /**
   * 使查询缓存失效
   */
  private invalidateQueryCache(): void {
    this.queryCache.valid = false;
    this.queryCache.componentIndex.clear();
    this.queryCache.tagIndex.clear();
    this.queryCache.nameIndex.clear();
  }

  /**
   * 重建查询缓存
   */
  private rebuildQueryCache(): void {
    if (this.queryCache.valid) {
      return;
    }

    this.invalidateQueryCache();

    for (const entity of this.entities.values()) {
      this.addEntityToCache(entity);
    }

    this.queryCache.valid = true;
  }

  /**
   * 将实体添加到查询缓存
   * @param entity 实体
   */
  private addEntityToCache(entity: Entity): void {
    // 按组件类型索引
    const components = entity.getAllComponents();
    for (const component of components) {
      const type = component.getType();
      if (!this.queryCache.componentIndex.has(type)) {
        this.queryCache.componentIndex.set(type, new Set());
      }
      this.queryCache.componentIndex.get(type)!.add(entity);
    }

    // 按标签索引
    const tags = entity.getTags();
    for (const tag of tags) {
      if (!this.queryCache.tagIndex.has(tag)) {
        this.queryCache.tagIndex.set(tag, new Set());
      }
      this.queryCache.tagIndex.get(tag)!.add(entity);
    }

    // 按名称索引
    const name = entity.name;
    if (!this.queryCache.nameIndex.has(name)) {
      this.queryCache.nameIndex.set(name, new Set());
    }
    this.queryCache.nameIndex.get(name)!.add(entity);
  }

  /**
   * 从查询缓存中移除实体
   * @param entity 实体
   */
  private removeEntityFromCache(entity: Entity): void {
    // 从组件索引中移除
    for (const entitySet of this.queryCache.componentIndex.values()) {
      entitySet.delete(entity);
    }

    // 从标签索引中移除
    for (const entitySet of this.queryCache.tagIndex.values()) {
      entitySet.delete(entity);
    }

    // 从名称索引中移除
    for (const entitySet of this.queryCache.nameIndex.values()) {
      entitySet.delete(entity);
    }
  }

  /**
   * 添加事件过滤器
   * @param eventType 事件类型
   * @param filter 过滤函数
   */
  public addEventFilter(eventType: string, filter: (event: any) => boolean): void {
    if (!this.eventFilters.has(eventType)) {
      this.eventFilters.set(eventType, []);
    }
    this.eventFilters.get(eventType)!.push(filter);
  }

  /**
   * 移除事件过滤器
   * @param eventType 事件类型
   * @param filter 过滤函数
   */
  public removeEventFilter(eventType: string, filter: (event: any) => boolean): void {
    const filters = this.eventFilters.get(eventType);
    if (filters) {
      const index = filters.indexOf(filter);
      if (index !== -1) {
        filters.splice(index, 1);
      }
    }
  }

  /**
   * 广播事件到所有实体
   * @param eventType 事件类型
   * @param data 事件数据
   */
  public broadcastEvent(eventType: string, data?: any): void {
    // 应用事件过滤器
    const filters = this.eventFilters.get(eventType);
    if (filters) {
      for (const filter of filters) {
        if (!filter(data)) {
          return; // 事件被过滤器阻止
        }
      }
    }

    // 广播到所有实体
    for (const entity of this.entities.values()) {
      entity.emit(eventType, data);
    }

    // 广播到所有系统
    for (const system of this.systems) {
      system.emit(eventType, data);
    }

    // 发出世界级事件
    this.emit(eventType, data);
  }

  /**
   * 序列化世界状态
   * @returns 序列化的世界数据
   */
  public serialize(): any {
    const data = {
      version: '1.0',
      timestamp: Date.now(),
      state: this.state,
      entities: [] as any[],
      scenes: [] as any[],
      systems: [] as any[],
      activeSceneId: this.activeScene?.id || null
    };

    // 序列化实体
    for (const entity of this.entities.values()) {
      if (typeof (entity as any).serialize === 'function') {
        data.entities.push((entity as any).serialize());
      }
    }

    // 序列化场景
    for (const scene of this.scenes.values()) {
      if (typeof (scene as any).serialize === 'function') {
        data.scenes.push((scene as any).serialize());
      }
    }

    // 序列化系统配置
    for (const system of this.systems) {
      data.systems.push({
        type: system.getType(),
        enabled: system.isEnabled(),
        priority: system.getPriority(),
        options: system.getOptions()
      });
    }

    return data;
  }

  /**
   * 反序列化世界状态
   * @param data 序列化的世界数据
   */
  public deserialize(data: any): void {
    if (!data || data.version !== '1.0') {
      throw new Error('不支持的世界数据版本');
    }

    // 清空当前世界
    this.clear();

    // 恢复状态
    this.state = data.state || WorldState.RUNNING;

    // 反序列化场景
    for (const sceneData of data.scenes || []) {
      const scene = new Scene(sceneData.name);
      if (typeof (scene as any).deserialize === 'function') {
        (scene as any).deserialize(sceneData);
      }
      this.addScene(scene);
    }

    // 反序列化实体
    for (const entityData of data.entities || []) {
      const entity = new Entity(entityData.name);
      if (typeof (entity as any).deserialize === 'function') {
        (entity as any).deserialize(entityData);
      }
      this.addEntity(entity);
    }

    // 设置活跃场景
    if (data.activeSceneId) {
      const activeScene = this.getScene(data.activeSceneId);
      if (activeScene) {
        this.setActiveScene(activeScene);
      }
    }

    // 发出反序列化完成事件
    this.emit('deserialized', data);
  }

  /**
   * 获取世界统计信息
   * @returns 统计信息
   */
  public getStatistics(): {
    entities: { total: number; active: number; byComponent: Map<string, number>; byTag: Map<string, number> };
    scenes: { total: number; active: string | null };
    systems: { total: number; active: number; byType: Map<string, boolean> };
    performance: WorldPerformanceStats;
    memory: { cacheSize: number; indexSize: number };
  } {
    // 统计按组件类型分组的实体数量
    const byComponent = new Map<string, number>();
    for (const [componentType, entitySet] of this.queryCache.componentIndex) {
      byComponent.set(componentType, entitySet.size);
    }

    // 统计按标签分组的实体数量
    const byTag = new Map<string, number>();
    for (const [tag, entitySet] of this.queryCache.tagIndex) {
      byTag.set(tag, entitySet.size);
    }

    // 统计系统状态
    const byType = new Map<string, boolean>();
    for (const system of this.systems) {
      byType.set(system.getType(), system.isEnabled());
    }

    return {
      entities: {
        total: this.entities.size,
        active: Array.from(this.entities.values()).filter(e => e.isActive()).length,
        byComponent,
        byTag
      },
      scenes: {
        total: this.scenes.size,
        active: this.activeScene?.id || null
      },
      systems: {
        total: this.systems.length,
        active: this.systems.filter(s => s.isEnabled()).length,
        byType
      },
      performance: this.getPerformanceStats(),
      memory: {
        cacheSize: this.queryCache.componentIndex.size + this.queryCache.tagIndex.size + this.queryCache.nameIndex.size,
        indexSize: Array.from(this.queryCache.componentIndex.values()).reduce((sum, set) => sum + set.size, 0) +
                   Array.from(this.queryCache.tagIndex.values()).reduce((sum, set) => sum + set.size, 0) +
                   Array.from(this.queryCache.nameIndex.values()).reduce((sum, set) => sum + set.size, 0)
      }
    };
  }
}
