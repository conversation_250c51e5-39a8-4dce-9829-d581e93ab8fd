/**
 * 虚拟化身上传控制器
 * 
 * 提供虚拟化身文件上传、数字人创建管理相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AvatarUploadService } from './avatar-upload.service';

/**
 * 上传配置DTO
 */
export class UploadConfigDto {
  /** 目标场景ID */
  targetSceneId: string;

  /** 生成位置 */
  spawnPosition?: {
    x: number;
    y: number;
    z: number;
  };

  /** 生成旋转 */
  spawnRotation?: {
    x: number;
    y: number;
    z: number;
  };

  /** 生成缩放 */
  spawnScale?: {
    x: number;
    y: number;
    z: number;
  };

  /** 是否自动激活 */
  autoActivate?: boolean;

  /** 数字人名称 */
  digitalHumanName?: string;

  /** 数字人配置 */
  digitalHumanConfig?: {
    enableAI?: boolean;
    enableVoice?: boolean;
    enableInteraction?: boolean;
    knowledgeBaseId?: string;
    personality?: string;
  };

  /** 验证选项 */
  validationOptions?: {
    strictMode?: boolean;
    allowPartialData?: boolean;
    skipChecksums?: boolean;
  };
}

/**
 * 创建数字人DTO
 */
export class CreateDigitalHumanDto {
  /** 虚拟化身数据 */
  avatarData: any;

  /** 场景ID */
  sceneId: string;

  /** 位置 */
  position?: {
    x: number;
    y: number;
    z: number;
  };

  /** 数字人名称 */
  name?: string;

  /** 数字人配置 */
  config?: {
    enableAI?: boolean;
    enableVoice?: boolean;
    enableInteraction?: boolean;
    knowledgeBaseId?: string;
    personality?: string;
  };
}

@ApiTags('虚拟化身上传管理')
@Controller('avatar-upload')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AvatarUploadController {
  constructor(private readonly avatarUploadService: AvatarUploadService) {}

  /**
   * 上传虚拟化身文件
   */
  @Post('file')
  @ApiOperation({ summary: '上传虚拟化身文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        config: {
          type: 'string',
          description: 'JSON格式的上传配置',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: '上传成功' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadAvatarFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('config') configStr?: string
  ) {
    try {
      if (!file) {
        throw new HttpException('未提供文件', HttpStatus.BAD_REQUEST);
      }

      // 解析配置
      let config: UploadConfigDto = { targetSceneId: 'default' };
      if (configStr) {
        try {
          config = { ...config, ...JSON.parse(configStr) };
        } catch (error) {
          throw new HttpException('配置格式错误', HttpStatus.BAD_REQUEST);
        }
      }

      // 构建文件信息
      const fileInfo = {
        fileName: file.originalname,
        fileSize: file.size,
        fileType: file.mimetype,
        fileContent: file.buffer,
        uploadTime: new Date()
      };

      return await this.avatarUploadService.uploadAvatarFile(fileInfo, config);
    } catch (error) {
      throw new HttpException(
        `上传虚拟化身文件失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建数字人
   */
  @Post('digital-human')
  @ApiOperation({ summary: '创建数字人' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createDigitalHuman(@Body() createDigitalHumanDto: CreateDigitalHumanDto) {
    try {
      return await this.avatarUploadService.createDigitalHuman(createDigitalHumanDto);
    } catch (error) {
      throw new HttpException(
        `创建数字人失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取数字人列表
   */
  @Get('digital-humans')
  @ApiOperation({ summary: '获取数字人列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDigitalHumans(@Query('sceneId') sceneId?: string) {
    try {
      return await this.avatarUploadService.getDigitalHumans(sceneId);
    } catch (error) {
      throw new HttpException(
        `获取数字人列表失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取数字人详情
   */
  @Get('digital-humans/:digitalHumanId')
  @ApiOperation({ summary: '获取数字人详情' })
  @ApiParam({ name: 'digitalHumanId', description: '数字人ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDigitalHumanDetail(@Param('digitalHumanId') digitalHumanId: string) {
    try {
      return await this.avatarUploadService.getDigitalHumanDetail(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        `获取数字人详情失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除数字人
   */
  @Delete('digital-humans/:digitalHumanId')
  @ApiOperation({ summary: '删除数字人' })
  @ApiParam({ name: 'digitalHumanId', description: '数字人ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async removeDigitalHuman(@Param('digitalHumanId') digitalHumanId: string) {
    try {
      return await this.avatarUploadService.removeDigitalHuman(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        `删除数字人失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新数字人配置
   */
  @Post('digital-humans/:digitalHumanId/config')
  @ApiOperation({ summary: '更新数字人配置' })
  @ApiParam({ name: 'digitalHumanId', description: '数字人ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateDigitalHumanConfig(
    @Param('digitalHumanId') digitalHumanId: string,
    @Body() config: any
  ) {
    try {
      return await this.avatarUploadService.updateDigitalHumanConfig(digitalHumanId, config);
    } catch (error) {
      throw new HttpException(
        `更新数字人配置失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 激活/停用数字人
   */
  @Post('digital-humans/:digitalHumanId/toggle')
  @ApiOperation({ summary: '激活/停用数字人' })
  @ApiParam({ name: 'digitalHumanId', description: '数字人ID' })
  @ApiResponse({ status: 200, description: '操作成功' })
  async toggleDigitalHuman(@Param('digitalHumanId') digitalHumanId: string) {
    try {
      return await this.avatarUploadService.toggleDigitalHuman(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        `切换数字人状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取上传历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取上传历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUploadHistory(@Query('sceneId') sceneId?: string) {
    try {
      return await this.avatarUploadService.getUploadHistory(sceneId);
    } catch (error) {
      throw new HttpException(
        `获取上传历史失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取上传统计信息
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取上传统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUploadStatistics() {
    try {
      return await this.avatarUploadService.getUploadStatistics();
    } catch (error) {
      throw new HttpException(
        `获取上传统计失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 验证虚拟化身文件
   */
  @Post('validate')
  @ApiOperation({ summary: '验证虚拟化身文件' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: '验证完成' })
  @UseInterceptors(FileInterceptor('file'))
  async validateAvatarFile(@UploadedFile() file: Express.Multer.File) {
    try {
      if (!file) {
        throw new HttpException('未提供文件', HttpStatus.BAD_REQUEST);
      }

      const fileInfo = {
        fileName: file.originalname,
        fileSize: file.size,
        fileType: file.mimetype,
        fileContent: file.buffer,
        uploadTime: new Date()
      };

      return await this.avatarUploadService.validateAvatarFile(fileInfo);
    } catch (error) {
      throw new HttpException(
        `验证虚拟化身文件失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取支持的文件格式
   */
  @Get('supported-formats')
  @ApiOperation({ summary: '获取支持的文件格式' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSupportedFormats() {
    try {
      return await this.avatarUploadService.getSupportedFormats();
    } catch (error) {
      throw new HttpException(
        `获取支持格式失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
