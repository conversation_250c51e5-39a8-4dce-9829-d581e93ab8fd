/**
 * 自然语言处理器使用示例
 * 
 * 展示完善后的NLP系统的各种功能
 */

import { 
  NaturalLanguageProcessor, 
  Language, 
  NLPConfig,
  MultiModalInput 
} from '../NaturalLanguageProcessor';

/**
 * NLP系统示例
 */
export class NLPExample {
  private nlpProcessor: NaturalLanguageProcessor;

  constructor() {
    // 配置NLP系统
    const config: Partial<NLPConfig> = {
      defaultLanguage: Language.CHINESE,
      enableMultiLanguage: true,
      enableSentimentAnalysis: true,
      enableEntityRecognition: true,
      enableIntentClassification: true,
      enableDialogueManagement: true,
      enableSpeechProcessing: true,
      enableTranslation: true,
      enableSummarization: true,
      enableSyntaxAnalysis: true,
      enableKnowledgeGraph: true,
      enableEmotionAnalysis: true,
      enableQualityAssessment: true,
      enableRealTimeLearning: true,
      enableMultiModalProcessing: true,
      maxContextLength: 15,
      cacheSize: 2000
    };

    this.nlpProcessor = new NaturalLanguageProcessor(config);
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.nlpProcessor.on('modelsInitialized', () => {
      console.log('✅ NLP模型初始化完成');
    });

    this.nlpProcessor.on('textUnderstand', (result) => {
      console.log('🧠 文本理解完成:', {
        text: result.text,
        language: result.language,
        intent: result.intent,
        sentiment: result.sentiment,
        confidence: result.confidence.toFixed(3)
      });
    });

    this.nlpProcessor.on('textGenerated', (result) => {
      console.log('📝 文本生成完成:', {
        text: result.text,
        language: result.language,
        style: result.style,
        confidence: result.confidence.toFixed(3)
      });
    });

    this.nlpProcessor.on('dialogueProcessed', (data) => {
      console.log('💬 对话处理完成:', {
        sessionId: data.sessionId,
        userInput: data.userInput,
        response: data.response.text
      });
    });
  }

  /**
   * 运行完整示例
   */
  public async runExample(): Promise<void> {
    console.log('=== 自然语言处理器完整功能示例 ===\n');

    try {
      // 1. 基础语言理解
      await this.demonstrateBasicUnderstanding();

      // 2. 对话管理
      await this.demonstrateDialogueManagement();

      // 3. 语言翻译
      await this.demonstrateTranslation();

      // 4. 文本摘要
      await this.demonstrateSummarization();

      // 5. 语法分析
      await this.demonstrateSyntaxAnalysis();

      // 6. 情感计算
      await this.demonstrateEmotionAnalysis();

      // 7. 语音处理
      await this.demonstrateSpeechProcessing();

      // 8. 知识图谱查询
      await this.demonstrateKnowledgeGraph();

      // 9. 多模态处理
      await this.demonstrateMultiModalProcessing();

      // 10. 对话质量评估
      await this.demonstrateQualityAssessment();

      // 11. 实时学习
      await this.demonstrateRealTimeLearning();

      // 12. 系统统计
      this.displaySystemStats();

    } catch (error) {
      console.error('示例运行失败:', error);
    }
  }

  /**
   * 演示基础语言理解
   */
  private async demonstrateBasicUnderstanding(): Promise<void> {
    console.log('1. 基础语言理解演示');
    console.log('='.repeat(30));

    const testTexts = [
      '你好，我想了解一下人工智能的发展历史',
      'Hello, can you help me with machine learning?',
      '今天天气真不错，我很开心！',
      '请帮我预订明天下午3点的会议室'
    ];

    for (const text of testTexts) {
      const understanding = await this.nlpProcessor.understand(text);
      console.log(`输入: ${text}`);
      console.log(`语言: ${understanding.language}`);
      console.log(`意图: ${understanding.intent}`);
      console.log(`情感: ${understanding.sentiment}`);
      console.log(`实体: ${understanding.entities.map(e => `${e.text}(${e.type})`).join(', ')}`);
      console.log(`置信度: ${understanding.confidence.toFixed(3)}`);
      console.log('---');
    }
    console.log();
  }

  /**
   * 演示对话管理
   */
  private async demonstrateDialogueManagement(): Promise<void> {
    console.log('2. 对话管理演示');
    console.log('='.repeat(30));

    const sessionId = 'demo_session_001';
    const userId = 'demo_user_001';

    const conversation = [
      '你好，我是小明',
      '我想了解人工智能',
      '具体来说，机器学习是怎么工作的？',
      '谢谢你的解释，很有帮助'
    ];

    for (const userInput of conversation) {
      console.log(`用户: ${userInput}`);
      const response = await this.nlpProcessor.processDialogue(userInput, sessionId, userId);
      console.log(`系统: ${response.text}`);
      console.log('---');
    }

    // 获取对话上下文
    const context = this.nlpProcessor.getDialogueContext(sessionId);
    if (context) {
      console.log(`对话轮次: ${context.history.length}`);
      console.log(`当前话题: ${context.currentTopic}`);
    }
    console.log();
  }

  /**
   * 演示语言翻译
   */
  private async demonstrateTranslation(): Promise<void> {
    console.log('3. 语言翻译演示');
    console.log('='.repeat(30));

    const translations = [
      { text: '你好，世界！', target: Language.ENGLISH },
      { text: 'Hello, world!', target: Language.CHINESE },
      { text: '今天天气很好', target: Language.ENGLISH }
    ];

    for (const { text, target } of translations) {
      const result = await this.nlpProcessor.translateText(text, target);
      console.log(`原文 (${result.sourceLanguage}): ${result.originalText}`);
      console.log(`译文 (${result.targetLanguage}): ${result.translatedText}`);
      console.log(`置信度: ${result.confidence.toFixed(3)}`);
      console.log('---');
    }
    console.log();
  }

  /**
   * 演示文本摘要
   */
  private async demonstrateSummarization(): Promise<void> {
    console.log('4. 文本摘要演示');
    console.log('='.repeat(30));

    const longText = `
      人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
      并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
      语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
      应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
      人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、
      也可能超过人的智能。
    `;

    const summary = await this.nlpProcessor.summarizeText(longText.trim());
    console.log('原文长度:', summary.originalText.length);
    console.log('摘要:', summary.summary);
    console.log('关键词:', summary.keywords.join(', '));
    console.log('要点:', summary.keyPoints.join('; '));
    console.log('压缩比:', summary.compressionRatio.toFixed(3));
    console.log();
  }

  /**
   * 演示语法分析
   */
  private async demonstrateSyntaxAnalysis(): Promise<void> {
    console.log('5. 语法分析演示');
    console.log('='.repeat(30));

    const text = '我今天去了北京大学参观';
    const analysis = await this.nlpProcessor.analyzeSyntax(text);

    console.log('输入文本:', text);
    console.log('词性标注:');
    analysis.tokens.forEach(token => {
      console.log(`  ${token.text} (${token.pos})`);
    });

    console.log('依存关系:');
    analysis.dependencies.forEach(dep => {
      console.log(`  ${analysis.tokens[dep.head]?.text} --${dep.relation}--> ${analysis.tokens[dep.dependent]?.text}`);
    });

    if (analysis.grammaticalErrors.length > 0) {
      console.log('语法错误:');
      analysis.grammaticalErrors.forEach(error => {
        console.log(`  ${error.type}: ${error.message}`);
      });
    }
    console.log();
  }

  /**
   * 演示情感计算
   */
  private async demonstrateEmotionAnalysis(): Promise<void> {
    console.log('6. 情感计算演示');
    console.log('='.repeat(30));

    const emotionalTexts = [
      '我今天非常开心，因为收到了好消息！',
      '这件事让我很生气和失望',
      '我对这个结果感到很惊讶',
      '今天的天气还不错'
    ];

    for (const text of emotionalTexts) {
      const emotions = await this.nlpProcessor.analyzeEmotions(text);
      console.log(`文本: ${text}`);
      console.log(`主导情感: ${emotions.dominantEmotion}`);
      console.log(`激活度: ${emotions.arousal.toFixed(3)}`);
      console.log(`效价: ${emotions.valence.toFixed(3)}`);
      console.log('情感分布:');
      emotions.emotions.forEach(emotion => {
        if (emotion.score > 0) {
          console.log(`  ${emotion.emotion}: ${emotion.score.toFixed(3)}`);
        }
      });
      console.log('---');
    }
    console.log();
  }

  /**
   * 演示语音处理
   */
  private async demonstrateSpeechProcessing(): Promise<void> {
    console.log('7. 语音处理演示');
    console.log('='.repeat(30));

    // 模拟音频数据
    const mockAudioData = new ArrayBuffer(16000 * 2); // 2秒的16kHz音频

    // 语音转文本
    const sttResult = await this.nlpProcessor.speechToText(mockAudioData);
    console.log('语音转文本结果:');
    console.log(`  文本: ${sttResult.text}`);
    console.log(`  语言: ${sttResult.language}`);
    console.log(`  置信度: ${sttResult.confidence.toFixed(3)}`);
    console.log(`  时长: ${sttResult.duration.toFixed(2)}秒`);

    // 文本转语音
    const ttsResult = await this.nlpProcessor.textToSpeech('你好，欢迎使用语音合成功能');
    console.log('\n文本转语音结果:');
    console.log(`  文本: ${ttsResult.text}`);
    console.log(`  语言: ${ttsResult.language}`);
    console.log(`  置信度: ${ttsResult.confidence.toFixed(3)}`);
    console.log(`  音频大小: ${ttsResult.audioData?.byteLength} 字节`);
    console.log();
  }

  /**
   * 演示知识图谱查询
   */
  private async demonstrateKnowledgeGraph(): Promise<void> {
    console.log('8. 知识图谱查询演示');
    console.log('='.repeat(30));

    const query = '人工智能的应用领域';
    const kgResult = await this.nlpProcessor.queryKnowledgeGraph(query);

    console.log(`查询: ${query}`);
    console.log('知识实体:');
    kgResult.entities.forEach(entity => {
      console.log(`  ${entity.name} (${entity.type}) - 置信度: ${entity.confidence.toFixed(3)}`);
    });

    console.log('知识关系:');
    kgResult.relations.forEach(relation => {
      console.log(`  ${relation.subject} --${relation.predicate}--> ${relation.object}`);
    });

    console.log('知识事实:');
    kgResult.facts.forEach(fact => {
      console.log(`  ${fact.statement} (置信度: ${fact.confidence.toFixed(3)})`);
    });
    console.log();
  }

  /**
   * 演示多模态处理
   */
  private async demonstrateMultiModalProcessing(): Promise<void> {
    console.log('9. 多模态处理演示');
    console.log('='.repeat(30));

    const multiModalInput: MultiModalInput = {
      text: '这是一段文本描述',
      audio: new ArrayBuffer(8000),
      image: new ArrayBuffer(1024),
      metadata: {
        source: 'demo',
        timestamp: Date.now()
      }
    };

    const result = await this.nlpProcessor.processMultiModalInput(multiModalInput);
    console.log('多模态输入处理结果:');
    console.log(`  理解文本: ${result.text}`);
    console.log(`  语言: ${result.language}`);
    console.log(`  意图: ${result.intent}`);
    console.log(`  情感: ${result.sentiment}`);
    console.log(`  置信度: ${result.confidence.toFixed(3)}`);
    console.log();
  }

  /**
   * 演示对话质量评估
   */
  private async demonstrateQualityAssessment(): Promise<void> {
    console.log('10. 对话质量评估演示');
    console.log('='.repeat(30));

    const sessionId = 'quality_demo_session';
    const context = this.nlpProcessor.getDialogueContext(sessionId);
    
    if (context && context.history.length > 0) {
      const assessment = await this.nlpProcessor.assessDialogueQuality(context.history, context);
      
      console.log('对话质量评估结果:');
      console.log(`  连贯性: ${assessment.coherence.toFixed(3)}`);
      console.log(`  相关性: ${assessment.relevance.toFixed(3)}`);
      console.log(`  信息量: ${assessment.informativeness.toFixed(3)}`);
      console.log(`  参与度: ${assessment.engagement.toFixed(3)}`);
      console.log(`  自然度: ${assessment.naturalness.toFixed(3)}`);
      console.log(`  总体评分: ${assessment.overallScore.toFixed(3)}`);
      
      if (assessment.feedback.length > 0) {
        console.log('改进建议:');
        assessment.feedback.forEach(feedback => {
          console.log(`  - ${feedback}`);
        });
      }
    } else {
      console.log('没有足够的对话数据进行质量评估');
    }
    console.log();
  }

  /**
   * 演示实时学习
   */
  private async demonstrateRealTimeLearning(): Promise<void> {
    console.log('11. 实时学习演示');
    console.log('='.repeat(30));

    const sessionId = 'learning_demo_session';
    
    // 添加用户反馈
    this.nlpProcessor.addUserFeedback(sessionId, 4.5); // 高评分
    this.nlpProcessor.addUserFeedback('session2', 2.0); // 低评分
    this.nlpProcessor.addUserFeedback('session3', 3.8); // 中等评分

    console.log('用户反馈已添加到学习系统');
    
    // 导出学习数据
    const learningData = this.nlpProcessor.exportLearningData();
    console.log(`学习数据条目数: ${learningData.length}`);
    
    if (learningData.length > 0) {
      console.log('最新学习数据示例:');
      const latest = learningData[learningData.length - 1];
      console.log(`  输入: ${latest.input}`);
      console.log(`  评分: ${latest.rating}`);
      console.log(`  时间: ${new Date(latest.timestamp).toLocaleString()}`);
    }
    console.log();
  }

  /**
   * 显示系统统计
   */
  private displaySystemStats(): void {
    console.log('12. 系统统计信息');
    console.log('='.repeat(30));

    const stats = this.nlpProcessor.getExtendedStats();
    
    console.log('基础统计:');
    console.log(`  总处理次数: ${stats.totalProcessed}`);
    console.log(`  平均置信度: ${stats.averageConfidence.toFixed(3)}`);
    console.log(`  缓存命中率: ${(stats.cacheHitRate * 100).toFixed(1)}%`);
    
    console.log('\n功能使用统计:');
    console.log(`  翻译次数: ${stats.translationCount}`);
    console.log(`  摘要次数: ${stats.summaryCount}`);
    console.log(`  语音处理次数: ${stats.speechProcessingCount}`);
    console.log(`  平均质量评分: ${stats.averageQualityScore.toFixed(3)}`);
    
    console.log('\n缓存统计:');
    console.log(`  理解缓存: ${stats.cacheStats.understandingCacheSize}`);
    console.log(`  生成缓存: ${stats.cacheStats.generationCacheSize}`);
    console.log(`  翻译缓存: ${stats.cacheStats.translationCacheSize}`);
    console.log(`  摘要缓存: ${stats.cacheStats.summaryCacheSize}`);
    console.log(`  语法缓存: ${stats.cacheStats.syntaxCacheSize}`);
    
    console.log('\n学习统计:');
    console.log(`  学习数据大小: ${stats.learningDataSize}`);
    console.log(`  用户反馈数量: ${stats.userFeedbackCount}`);
    
    console.log('\n语言分布:');
    Object.entries(stats.languageDistribution).forEach(([lang, count]) => {
      console.log(`  ${lang}: ${count}`);
    });
    
    console.log('\n意图分布:');
    Object.entries(stats.intentDistribution).forEach(([intent, count]) => {
      console.log(`  ${intent}: ${count}`);
    });
    
    console.log('\n情感分布:');
    Object.entries(stats.sentimentDistribution).forEach(([sentiment, count]) => {
      console.log(`  ${sentiment}: ${count}`);
    });
  }
}

// 运行示例
if (require.main === module) {
  const example = new NLPExample();
  example.runExample().catch(console.error);
}
