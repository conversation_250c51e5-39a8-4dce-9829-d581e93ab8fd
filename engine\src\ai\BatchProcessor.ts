/**
 * 批处理器
 * 用于批量处理AI模型请求，提高性能
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 批处理请求
 */
export interface BatchRequest<T, R> {
  /** 请求ID */
  id: string;
  /** 输入数据 */
  input: T;
  /** 选项 */
  options?: any;
  /** 解析回调 */
  resolve: (result: R) => void;
  /** 拒绝回调 */
  reject: (error: Error) => void;
  /** 提交时间 */
  submitTime: number;
  /** 优先级 (数字越大优先级越高) */
  priority?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 标签 */
  tags?: string[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 批处理策略枚举
 */
export enum BatchStrategy {
  /** 时间优先 - 优先考虑等待时间 */
  TIME_FIRST = 'time_first',
  /** 大小优先 - 优先考虑批处理大小 */
  SIZE_FIRST = 'size_first',
  /** 混合策略 - 平衡时间和大小 */
  HYBRID = 'hybrid',
  /** 优先级优先 - 优先考虑请求优先级 */
  PRIORITY_FIRST = 'priority_first'
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 是否启用缓存 */
  enabled?: boolean;
  /** 缓存大小 */
  maxSize?: number;
  /** 缓存TTL（毫秒） */
  ttl?: number;
  /** 缓存键生成函数 */
  keyGenerator?: (input: any) => string;
}

/**
 * 批处理配置
 */
export interface BatchProcessorConfig {
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 最大等待时间（毫秒） */
  maxWaitTime?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用动态批处理大小 */
  dynamicBatchSize?: boolean;
  /** 最小批处理大小 */
  minBatchSize?: number;
  /** 性能监控窗口大小 */
  performanceWindowSize?: number;
  /** 批处理超时时间（毫秒） */
  batchTimeout?: number;
  /** 最大队列大小 */
  maxQueueSize?: number;
  /** 批处理策略 */
  strategy?: BatchStrategy;
  /** 是否启用优先级队列 */
  enablePriorityQueue?: boolean;
  /** 默认优先级 */
  defaultPriority?: number;
  /** 是否启用重试 */
  enableRetry?: boolean;
  /** 默认最大重试次数 */
  defaultMaxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 缓存配置 */
  cache?: CacheConfig;
  /** 最大内存使用（MB） */
  maxMemoryUsage?: number;
  /** 是否启用详细统计 */
  enableDetailedStats?: boolean;
  /** 错误处理策略 */
  errorHandling?: 'fail_fast' | 'continue' | 'retry';
}

/**
 * 批处理性能统计
 */
export interface BatchPerformanceStats {
  /** 批处理总数 */
  batchCount: number;
  /** 请求总数 */
  requestCount: number;
  /** 平均批处理大小 */
  averageBatchSize: number;
  /** 平均处理时间（毫秒） */
  averageProcessTime: number;
  /** 平均等待时间（毫秒） */
  averageWaitTime: number;
  /** 当前队列大小 */
  queueSize: number;
  /** 当前批处理大小 */
  currentBatchSize: number;
  /** 最大批处理大小 */
  maxBatchSize: number;
  /** 最大等待时间（毫秒） */
  maxWaitTime: number;
}

/**
 * 批处理详细统计
 */
export interface BatchProcessorStats {
  /** 批处理次数 */
  batchCount: number;
  /** 请求总数 */
  requestCount: number;
  /** 成功次数 */
  successCount: number;
  /** 失败次数 */
  failureCount: number;
  /** 重试次数 */
  retryCount: number;
  /** 平均处理时间（毫秒） */
  averageProcessTime: number;
  /** 平均等待时间（毫秒） */
  averageWaitTime: number;
  /** 平均批处理大小 */
  averageBatchSize: number;
  /** 当前批处理大小 */
  currentBatchSize: number;
  /** 队列长度 */
  queueLength: number;
  /** 是否正在处理 */
  isProcessing: boolean;
  /** 缓存大小 */
  cacheSize: number;
  /** 内存使用（字节） */
  memoryUsage: number;
  /** 批处理策略 */
  strategy: BatchStrategy;
}

/**
 * 批处理器
 */
export class BatchProcessor<T, R> {
  /** 配置 */
  private config: Required<BatchProcessorConfig>;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<BatchProcessorConfig> = {
    maxBatchSize: 16,
    maxWaitTime: 100,
    debug: false,
    dynamicBatchSize: true,
    minBatchSize: 1,
    performanceWindowSize: 10,
    batchTimeout: 5000,
    maxQueueSize: 1000,
    strategy: BatchStrategy.HYBRID,
    enablePriorityQueue: false,
    defaultPriority: 0,
    enableRetry: true,
    defaultMaxRetries: 3,
    retryDelay: 1000,
    cache: {
      enabled: false,
      maxSize: 1000,
      ttl: 300000, // 5分钟
      keyGenerator: (input: any) => JSON.stringify(input)
    },
    maxMemoryUsage: 512, // 512MB
    enableDetailedStats: true,
    errorHandling: 'retry'
  };

  /** 请求队列 */
  private queue: BatchRequest<T, R>[] = [];

  /** 优先级队列 */
  private priorityQueues: Map<number, BatchRequest<T, R>[]> = new Map();

  /** 处理函数 */
  private processFn: (inputs: T[], options?: any[]) => Promise<R[]>;

  /** 是否正在处理 */
  private isProcessing: boolean = false;

  /** 定时器 */
  private timer: NodeJS.Timeout | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 批处理计数 */
  private batchCount: number = 0;

  /** 请求计数 */
  private requestCount: number = 0;

  /** 成功计数 */
  private successCount: number = 0;

  /** 失败计数 */
  private failureCount: number = 0;

  /** 重试计数 */
  private retryCount: number = 0;

  /** 处理时间历史 */
  private processTimeHistory: number[] = [];

  /** 等待时间历史 */
  private waitTimeHistory: number[] = [];

  /** 批处理大小历史 */
  private batchSizeHistory: number[] = [];

  /** 当前批处理大小 */
  private currentBatchSize: number;

  /** 结果缓存 */
  private resultCache: Map<string, { result: R; timestamp: number }> = new Map();

  /** 当前内存使用（估算，字节） */
  private currentMemoryUsage: number = 0;

  /**
   * 构造函数
   * @param processFn 处理函数
   * @param config 配置
   */
  constructor(
    processFn: (inputs: T[], options?: any[]) => Promise<R[]>,
    config: BatchProcessorConfig = {}
  ) {
    this.processFn = processFn;

    this.config = {
      ...BatchProcessor.DEFAULT_CONFIG,
      ...config
    };

    this.currentBatchSize = this.config.maxBatchSize;
  }

  /**
   * 提交请求
   * @param input 输入数据
   * @param options 选项
   * @returns 处理结果
   */
  public async process(input: T, options?: any): Promise<R> {
    // 检查缓存
    if (this.config.cache?.enabled) {
      const cacheKey = this.config.cache.keyGenerator!(input);
      const cached = this.resultCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.config.cache.ttl!) {
        return cached.result;
      }
    }

    // 检查队列是否已满
    const totalQueueSize = this.getTotalQueueSize();
    if (totalQueueSize >= this.config.maxQueueSize) {
      throw new Error('批处理队列已满');
    }

    // 检查内存使用
    if (this.currentMemoryUsage > this.config.maxMemoryUsage * 1024 * 1024) {
      throw new Error('内存使用超出限制');
    }

    // 创建请求
    return new Promise<R>((resolve, reject) => {
      const priority = options?.priority ?? this.config.defaultPriority;
      const request: BatchRequest<T, R> = {
        id: `batch-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        input,
        options,
        resolve,
        reject,
        submitTime: Date.now(),
        priority,
        retryCount: 0,
        maxRetries: options?.maxRetries ?? this.config.defaultMaxRetries,
        tags: options?.tags || [],
        metadata: options?.metadata || {}
      };

      // 添加到适当的队列
      this.addToQueue(request);
      this.requestCount++;

      // 更新内存使用估算
      this.updateMemoryUsage(request, 'add');

      // 检查是否需要立即处理
      this.checkAndTriggerProcessing();
    });
  }

  /**
   * 添加请求到队列
   */
  private addToQueue(request: BatchRequest<T, R>): void {
    if (this.config.enablePriorityQueue) {
      const priority = request.priority!;
      if (!this.priorityQueues.has(priority)) {
        this.priorityQueues.set(priority, []);
      }
      this.priorityQueues.get(priority)!.push(request);
    } else {
      this.queue.push(request);
    }
  }

  /**
   * 获取总队列大小
   */
  private getTotalQueueSize(): number {
    if (this.config.enablePriorityQueue) {
      return Array.from(this.priorityQueues.values())
        .reduce((total, queue) => total + queue.length, 0);
    }
    return this.queue.length;
  }

  /**
   * 更新内存使用估算
   */
  private updateMemoryUsage(request: BatchRequest<T, R>, operation: 'add' | 'remove'): void {
    // 简单的内存使用估算
    const requestSize = JSON.stringify(request.input).length * 2; // 假设每个字符2字节
    if (operation === 'add') {
      this.currentMemoryUsage += requestSize;
    } else {
      this.currentMemoryUsage -= requestSize;
    }
  }

  /**
   * 检查并触发处理
   */
  private checkAndTriggerProcessing(): void {
    const totalSize = this.getTotalQueueSize();

    // 如果队列长度达到批处理大小，立即处理
    if (totalSize >= this.currentBatchSize) {
      this.processBatch();
    } else if (!this.timer && !this.isProcessing) {
      // 否则，设置定时器
      this.timer = setTimeout(() => {
        this.processBatch();
      }, this.config.maxWaitTime);
    }
  }

  /**
   * 获取下一批请求
   */
  private getNextBatch(): BatchRequest<T, R>[] {
    const batch: BatchRequest<T, R>[] = [];
    const maxBatchSize = Math.min(this.currentBatchSize, this.getTotalQueueSize());

    if (this.config.enablePriorityQueue) {
      // 按优先级从高到低处理
      const priorities = Array.from(this.priorityQueues.keys()).sort((a, b) => b - a);

      for (const priority of priorities) {
        const queue = this.priorityQueues.get(priority)!;
        while (queue.length > 0 && batch.length < maxBatchSize) {
          const request = queue.shift()!;
          batch.push(request);
          this.updateMemoryUsage(request, 'remove');
        }

        // 如果队列为空，删除该优先级
        if (queue.length === 0) {
          this.priorityQueues.delete(priority);
        }

        if (batch.length >= maxBatchSize) {
          break;
        }
      }
    } else {
      // 普通FIFO队列
      const takeCount = Math.min(maxBatchSize, this.queue.length);
      for (let i = 0; i < takeCount; i++) {
        const request = this.queue.shift()!;
        batch.push(request);
        this.updateMemoryUsage(request, 'remove');
      }
    }

    return batch;
  }

  /**
   * 处理批次
   */
  private async processBatch(): Promise<void> {
    // 如果队列为空或正在处理，直接返回
    if (this.getTotalQueueSize() === 0 || this.isProcessing) {
      return;
    }

    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    // 设置处理状态
    this.isProcessing = true;

    // 获取批次请求
    const batch = this.getNextBatch();

    if (batch.length === 0) {
      this.isProcessing = false;
      return;
    }

    // 提取输入和选项
    const inputs = batch.map(request => request.input);
    const options = batch.map(request => request.options);
    const batchSize = batch.length;

    // 记录批处理大小
    this.batchSizeHistory.push(batchSize);
    if (this.batchSizeHistory.length > this.config.performanceWindowSize) {
      this.batchSizeHistory.shift();
    }

    // 记录等待时间
    const now = Date.now();
    const waitTimes = batch.map(request => now - request.submitTime);
    const averageWaitTime = waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length;
    this.waitTimeHistory.push(averageWaitTime);
    if (this.waitTimeHistory.length > this.config.performanceWindowSize) {
      this.waitTimeHistory.shift();
    }

    // 设置批处理超时
    const timeoutId = setTimeout(() => {
      // 如果批处理超时，拒绝所有请求
      batch.forEach(request => {
        request.reject(new Error('批处理超时'));
      });

      // 重置处理状态
      this.isProcessing = false;

      // 如果队列不为空，继续处理
      if (this.queue.length > 0) {
        this.processBatch();
      }
    }, this.config.batchTimeout);

    try {
      // 处理批次
      const startTime = Date.now();
      const results = await this.processFn(inputs, options);
      const endTime = Date.now();

      // 清除超时
      clearTimeout(timeoutId);

      // 记录处理时间
      const processTime = endTime - startTime;
      this.processTimeHistory.push(processTime);
      if (this.processTimeHistory.length > this.config.performanceWindowSize) {
        this.processTimeHistory.shift();
      }

      // 更新批处理计数
      this.batchCount++;

      // 如果启用动态批处理大小，调整批处理大小
      if (this.config.dynamicBatchSize) {
        this.adjustBatchSize(processTime, batchSize);
      }

      // 如果结果数量与请求数量不匹配，拒绝所有请求
      if (results.length !== batch.length) {
        batch.forEach(request => {
          request.reject(new Error('批处理结果数量与请求数量不匹配'));
        });
      } else {
        // 否则，解析每个请求并缓存结果
        batch.forEach((request, index) => {
          const result = results[index];
          request.resolve(result);

          // 缓存结果
          if (this.config.cache?.enabled) {
            const cacheKey = this.config.cache.keyGenerator!(request.input);
            this.resultCache.set(cacheKey, {
              result,
              timestamp: Date.now()
            });
          }
        });

        this.successCount += batch.length;
      }

      // 清理过期缓存
      if (this.config.cache?.enabled) {
        this.cleanupCache();
      }

      // 触发批处理完成事件
      this.eventEmitter.emit('batchComplete', {
        batchSize,
        processTime,
        waitTime: averageWaitTime,
        successCount: this.successCount,
        failureCount: this.failureCount
      });

      // 如果启用调试，输出日志
      if (this.config.debug) {
        console.log(`[BatchProcessor] 批处理完成: ${batchSize}个请求, 处理时间: ${processTime}ms, 等待时间: ${averageWaitTime.toFixed(2)}ms`);
      }
    } catch (error) {
      // 清除超时
      clearTimeout(timeoutId);

      // 处理错误和重试
      await this.handleBatchError(batch, error as Error);

      // 触发批处理错误事件
      this.eventEmitter.emit('batchError', {
        error,
        batchSize,
        retryCount: this.retryCount
      });

      // 如果启用调试，输出错误日志
      if (this.config.debug) {
        console.error(`[BatchProcessor] 批处理错误: ${error}`);
      }
    } finally {
      // 重置处理状态
      this.isProcessing = false;

      // 如果队列不为空，继续处理
      if (this.getTotalQueueSize() > 0) {
        this.processBatch();
      }
    }
  }

  /**
   * 调整批处理大小
   * @param processTime 处理时间
   * @param batchSize 批处理大小
   */
  private adjustBatchSize(_processTime: number, _batchSize: number): void {
    // 如果处理时间历史不足，直接返回
    if (this.processTimeHistory.length < this.config.performanceWindowSize) {
      return;
    }

    // 计算平均处理时间
    const averageProcessTime = this.processTimeHistory.reduce((sum, time) => sum + time, 0) / this.processTimeHistory.length;

    // 计算平均等待时间
    const averageWaitTime = this.waitTimeHistory.reduce((sum, time) => sum + time, 0) / this.waitTimeHistory.length;

    // 根据策略调整批处理大小
    switch (this.config.strategy) {
      case BatchStrategy.TIME_FIRST:
        this.adjustBatchSizeByTime(averageWaitTime);
        break;
      case BatchStrategy.SIZE_FIRST:
        this.adjustBatchSizeByThroughput(averageProcessTime);
        break;
      case BatchStrategy.HYBRID:
        this.adjustBatchSizeHybrid(averageProcessTime, averageWaitTime);
        break;
      case BatchStrategy.PRIORITY_FIRST:
        // 优先级策略不调整批处理大小
        break;
    }
  }

  /**
   * 基于时间调整批处理大小
   */
  private adjustBatchSizeByTime(averageWaitTime: number): void {
    if (averageWaitTime > this.config.maxWaitTime * 0.8) {
      // 等待时间过长，减小批处理大小
      this.currentBatchSize = Math.max(this.currentBatchSize * 0.8, this.config.minBatchSize);
    } else if (averageWaitTime < this.config.maxWaitTime * 0.3) {
      // 等待时间较短，可以增加批处理大小
      this.currentBatchSize = Math.min(this.currentBatchSize * 1.2, this.config.maxBatchSize);
    }
  }

  /**
   * 基于吞吐量调整批处理大小
   */
  private adjustBatchSizeByThroughput(averageProcessTime: number): void {
    const avgBatchSize = this.batchSizeHistory.reduce((sum, size) => sum + size, 0) / this.batchSizeHistory.length;
    const throughput = avgBatchSize / averageProcessTime; // 每毫秒处理的请求数

    // 如果吞吐量较低，尝试增加批处理大小
    if (throughput < 0.1 && this.currentBatchSize < this.config.maxBatchSize) {
      this.currentBatchSize = Math.min(this.currentBatchSize * 1.3, this.config.maxBatchSize);
    } else if (throughput > 0.5 && this.currentBatchSize > this.config.minBatchSize) {
      this.currentBatchSize = Math.max(this.currentBatchSize * 0.9, this.config.minBatchSize);
    }
  }

  /**
   * 混合策略调整批处理大小
   */
  private adjustBatchSizeHybrid(averageProcessTime: number, averageWaitTime: number): void {
    // 综合考虑处理时间和等待时间
    const timeScore = averageWaitTime / this.config.maxWaitTime;
    const processScore = averageProcessTime / this.config.maxWaitTime;
    const combinedScore = (timeScore + processScore) / 2;

    if (combinedScore > 0.7) {
      // 性能较差，减小批处理大小
      this.currentBatchSize = Math.max(this.currentBatchSize * 0.85, this.config.minBatchSize);
    } else if (combinedScore < 0.3) {
      // 性能较好，可以增加批处理大小
      this.currentBatchSize = Math.min(this.currentBatchSize * 1.15, this.config.maxBatchSize);
    }

    if (this.config.debug) {
      console.log(`[BatchProcessor] 调整批处理大小: ${this.currentBatchSize}, 综合评分: ${combinedScore.toFixed(2)}`);
    }
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): BatchPerformanceStats {
    // 计算平均批处理大小
    const averageBatchSize = this.batchSizeHistory.length > 0
      ? this.batchSizeHistory.reduce((sum, size) => sum + size, 0) / this.batchSizeHistory.length
      : 0;

    // 计算平均处理时间
    const averageProcessTime = this.processTimeHistory.length > 0
      ? this.processTimeHistory.reduce((sum, time) => sum + time, 0) / this.processTimeHistory.length
      : 0;

    // 计算平均等待时间
    const averageWaitTime = this.waitTimeHistory.length > 0
      ? this.waitTimeHistory.reduce((sum, time) => sum + time, 0) / this.waitTimeHistory.length
      : 0;

    return {
      batchCount: this.batchCount,
      requestCount: this.requestCount,
      averageBatchSize,
      averageProcessTime,
      averageWaitTime,
      queueSize: this.queue.length,
      currentBatchSize: this.currentBatchSize,
      maxBatchSize: this.config.maxBatchSize,
      maxWaitTime: this.config.maxWaitTime
    };
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    // 拒绝所有请求
    this.queue.forEach(request => {
      request.reject(new Error('批处理队列已清空'));
    });

    // 清空队列
    this.queue = [];

    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 处理批处理错误
   */
  private async handleBatchError(batch: BatchRequest<T, R>[], error: Error): Promise<void> {
    this.failureCount += batch.length;

    if (this.config.enableRetry && this.config.errorHandling === 'retry') {
      // 尝试重试
      const retryableBatch: BatchRequest<T, R>[] = [];

      for (const request of batch) {
        if (request.retryCount! < request.maxRetries!) {
          request.retryCount!++;
          retryableBatch.push(request);
          this.retryCount++;
        } else {
          // 超过最大重试次数，拒绝请求
          request.reject(new Error(`批处理失败，已重试${request.maxRetries}次: ${error.message}`));
        }
      }

      // 将可重试的请求重新加入队列
      if (retryableBatch.length > 0) {
        // 延迟重试
        setTimeout(() => {
          retryableBatch.forEach(request => {
            this.addToQueue(request);
            this.updateMemoryUsage(request, 'add');
          });
          this.checkAndTriggerProcessing();
        }, this.config.retryDelay);
      }
    } else {
      // 不重试，直接拒绝所有请求
      batch.forEach(request => {
        request.reject(error);
      });
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache(): void {
    if (!this.config.cache?.enabled) return;

    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, cached] of this.resultCache.entries()) {
      if (now - cached.timestamp > this.config.cache.ttl!) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.resultCache.delete(key));

    // 如果缓存大小超过限制，删除最旧的条目
    if (this.resultCache.size > this.config.cache.maxSize!) {
      const entries = Array.from(this.resultCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const deleteCount = this.resultCache.size - this.config.cache.maxSize!;
      for (let i = 0; i < deleteCount; i++) {
        this.resultCache.delete(entries[i][0]);
      }
    }
  }

  /**
   * 获取详细统计信息
   */
  public getDetailedStats(): BatchProcessorStats {
    const averageProcessTime = this.processTimeHistory.length > 0
      ? this.processTimeHistory.reduce((sum, time) => sum + time, 0) / this.processTimeHistory.length
      : 0;

    const averageWaitTime = this.waitTimeHistory.length > 0
      ? this.waitTimeHistory.reduce((sum, time) => sum + time, 0) / this.waitTimeHistory.length
      : 0;

    const averageBatchSize = this.batchSizeHistory.length > 0
      ? this.batchSizeHistory.reduce((sum, size) => sum + size, 0) / this.batchSizeHistory.length
      : 0;

    return {
      batchCount: this.batchCount,
      requestCount: this.requestCount,
      successCount: this.successCount,
      failureCount: this.failureCount,
      retryCount: this.retryCount,
      averageProcessTime,
      averageWaitTime,
      averageBatchSize,
      currentBatchSize: this.currentBatchSize,
      queueLength: this.getTotalQueueSize(),
      isProcessing: this.isProcessing,
      cacheSize: this.resultCache.size,
      memoryUsage: this.currentMemoryUsage,
      strategy: this.config.strategy
    };
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.clearQueue();

    // 清空优先级队列
    this.priorityQueues.clear();

    // 清空缓存
    this.resultCache.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
