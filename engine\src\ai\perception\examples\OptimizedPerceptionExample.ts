/**
 * 优化感知系统使用示例
 * 
 * 展示完善后的优化感知系统的各种高级功能
 */

import * as THREE from 'three';
import { 
  OptimizedPerceptionSystem,
  PerceptionModality 
} from '../OptimizedPerceptionSystem';

/**
 * 优化感知系统示例
 */
export class OptimizedPerceptionExample {
  private perceptionSystem: OptimizedPerceptionSystem;
  private scene: THREE.Scene;
  private entities: Map<string, THREE.Object3D> = new Map();

  constructor() {
    this.perceptionSystem = new OptimizedPerceptionSystem();
    this.scene = new THREE.Scene();
    
    this.setupEventListeners();
    this.initializeTestEnvironment();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.perceptionSystem.on('perceptionProcessed', (data) => {
      console.log('感知处理完成:', {
        timestamp: data.timestamp,
        confidence: data.confidence,
        entitiesCount: data.worldModel.entities.size
      });
    });
  }

  /**
   * 初始化测试环境
   */
  private initializeTestEnvironment(): void {
    // 创建测试实体
    for (let i = 0; i < 20; i++) {
      const entity = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        new THREE.MeshBasicMaterial({ color: Math.random() * 0xffffff })
      );
      
      entity.position.set(
        (Math.random() - 0.5) * 200,
        (Math.random() - 0.5) * 200,
        (Math.random() - 0.5) * 200
      );
      
      const entityId = `entity_${i}`;
      this.entities.set(entityId, entity);
      this.scene.add(entity);
      
      // 更新空间索引
      this.perceptionSystem.updateEntityPosition(entityId, entity.position);
    }
  }

  /**
   * 运行完整示例
   */
  public async runExample(): Promise<void> {
    console.log('=== 优化感知系统完整功能示例 ===\n');

    try {
      // 1. 基础优化查询
      await this.demonstrateBasicOptimizedQuery();

      // 2. 多级缓存系统
      await this.demonstrateMultiLevelCache();

      // 3. 内存池管理
      await this.demonstrateMemoryPoolManagement();

      // 4. 预测性缓存
      await this.demonstratePredictiveCache();

      // 5. 负载均衡
      await this.demonstrateLoadBalancing();

      // 6. 数据压缩
      await this.demonstrateDataCompression();

      // 7. 错误恢复
      await this.demonstrateErrorRecovery();

      // 8. 分布式支持
      await this.demonstrateDistributedSupport();

      // 9. 安全功能
      await this.demonstrateSecurity();

      // 10. 性能监控
      await this.demonstratePerformanceMonitoring();

      // 11. 高级功能控制
      await this.demonstrateAdvancedFeatures();

    } catch (error) {
      console.error('示例运行失败:', error);
    }
  }

  /**
   * 演示基础优化查询
   */
  private async demonstrateBasicOptimizedQuery(): Promise<void> {
    console.log('1. 基础优化查询演示');
    console.log('='.repeat(30));

    const query = {
      position: new THREE.Vector3(0, 0, 0),
      radius: 50,
      modalities: [PerceptionModality.VISUAL, PerceptionModality.AUDITORY],
      priority: 1.0,
      timestamp: Date.now()
    };

    const startTime = performance.now();
    const results = await this.perceptionSystem.queryPerceptionOptimized(query);
    const endTime = performance.now();

    console.log(`查询结果: ${results.length} 个感知数据`);
    console.log(`查询时间: ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`空间索引效率: 已优化`);
    console.log();
  }

  /**
   * 演示多级缓存系统
   */
  private async demonstrateMultiLevelCache(): Promise<void> {
    console.log('2. 多级缓存系统演示');
    console.log('='.repeat(30));

    // 执行多次相同查询以测试缓存
    const query = {
      position: new THREE.Vector3(10, 10, 10),
      radius: 30,
      modalities: [PerceptionModality.VISUAL],
      priority: 0.8,
      timestamp: Date.now()
    };

    // 第一次查询（缓存未命中）
    const start1 = performance.now();
    await this.perceptionSystem.queryPerceptionOptimized(query);
    const time1 = performance.now() - start1;

    // 第二次查询（缓存命中）
    const start2 = performance.now();
    await this.perceptionSystem.queryPerceptionOptimized(query);
    const time2 = performance.now() - start2;

    console.log(`首次查询时间: ${time1.toFixed(2)}ms`);
    console.log(`缓存命中时间: ${time2.toFixed(2)}ms`);
    console.log(`性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    
    const stats = this.perceptionSystem.getPerformanceStats();
    console.log(`缓存命中率: ${(stats.cacheHitRate * 100).toFixed(1)}%`);
    console.log();
  }

  /**
   * 演示内存池管理
   */
  private async demonstrateMemoryPoolManagement(): Promise<void> {
    console.log('3. 内存池管理演示');
    console.log('='.repeat(30));

    const report = this.perceptionSystem.getExtendedPerformanceReport();
    
    console.log('内存池统计:');
    if (report.memoryPoolStats) {
      for (const [poolName, stats] of Object.entries(report.memoryPoolStats)) {
        console.log(`  ${poolName}:`);
        console.log(`    已分配: ${(stats as any).allocated}`);
        console.log(`    可用: ${(stats as any).available}`);
        console.log(`    命中率: ${((stats as any).hitRate * 100).toFixed(1)}%`);
      }
    }
    
    console.log(`总内存使用: ${(report.stats.memoryUsage / 1024).toFixed(2)}KB`);
    console.log();
  }

  /**
   * 演示预测性缓存
   */
  private async demonstratePredictiveCache(): Promise<void> {
    console.log('4. 预测性缓存演示');
    console.log('='.repeat(30));

    // 模拟规律性查询模式
    const basePosition = new THREE.Vector3(0, 0, 0);
    
    for (let i = 0; i < 5; i++) {
      const query = {
        position: basePosition.clone().add(new THREE.Vector3(i * 10, 0, 0)),
        radius: 25,
        modalities: [PerceptionModality.VISUAL],
        priority: 0.9,
        timestamp: Date.now()
      };
      
      await this.perceptionSystem.queryPerceptionOptimized(query);
      await new Promise(resolve => setTimeout(resolve, 100)); // 模拟时间间隔
    }

    const report = this.perceptionSystem.getExtendedPerformanceReport();
    console.log('预测缓存统计:');
    console.log(`  模式数量: ${report.predictiveCacheStats?.patterns || 0}`);
    console.log(`  平均置信度: ${(report.predictiveCacheStats?.averageConfidence * 100 || 0).toFixed(1)}%`);
    console.log();
  }

  /**
   * 演示负载均衡
   */
  private async demonstrateLoadBalancing(): Promise<void> {
    console.log('5. 负载均衡演示');
    console.log('='.repeat(30));

    // 并发查询测试
    const queries = [];
    for (let i = 0; i < 10; i++) {
      queries.push({
        position: new THREE.Vector3(
          Math.random() * 100 - 50,
          Math.random() * 100 - 50,
          Math.random() * 100 - 50
        ),
        radius: 20,
        modalities: [PerceptionModality.VISUAL, PerceptionModality.AUDITORY],
        priority: Math.random(),
        timestamp: Date.now()
      });
    }

    const startTime = performance.now();
    const promises = queries.map(query => 
      this.perceptionSystem.queryPerceptionOptimized(query)
    );
    
    await Promise.all(promises);
    const endTime = performance.now();

    console.log(`并发查询数量: ${queries.length}`);
    console.log(`总处理时间: ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`平均查询时间: ${((endTime - startTime) / queries.length).toFixed(2)}ms`);
    console.log();
  }

  /**
   * 演示数据压缩
   */
  private async demonstrateDataCompression(): Promise<void> {
    console.log('6. 数据压缩演示');
    console.log('='.repeat(30));

    // 启用压缩功能
    this.perceptionSystem.enableAdvancedFeatures({ compression: true });

    const query = {
      position: new THREE.Vector3(0, 0, 0),
      radius: 100,
      modalities: [PerceptionModality.VISUAL, PerceptionModality.AUDITORY, PerceptionModality.SPATIAL],
      priority: 1.0,
      timestamp: Date.now()
    };

    const results = await this.perceptionSystem.queryPerceptionOptimized(query);
    
    console.log('数据压缩功能已启用');
    console.log(`处理数据量: ${results.length} 项`);
    console.log('压缩算法: 简化RLE压缩');
    console.log();
  }

  /**
   * 演示错误恢复
   */
  private async demonstrateErrorRecovery(): Promise<void> {
    console.log('7. 错误恢复演示');
    console.log('='.repeat(30));

    try {
      // 模拟错误场景
      const invalidQuery = {
        position: new THREE.Vector3(NaN, NaN, NaN),
        radius: -1,
        modalities: [],
        priority: 2.0, // 超出范围
        timestamp: Date.now()
      };

      await this.perceptionSystem.queryPerceptionOptimized(invalidQuery);
    } catch (error) {
      console.log('错误恢复机制已触发');
      console.log('降级策略: 返回空结果');
      console.log('断路器状态: 监控中');
    }
    console.log();
  }

  /**
   * 演示分布式支持
   */
  private async demonstrateDistributedSupport(): Promise<void> {
    console.log('8. 分布式支持演示');
    console.log('='.repeat(30));

    // 启用分布式模式
    this.perceptionSystem.enableAdvancedFeatures({ distributedMode: true });

    const report = this.perceptionSystem.getExtendedPerformanceReport();
    console.log('分布式节点信息:');
    console.log(`  节点数量: ${report.distributedNodes?.length || 0}`);
    console.log('  负载均衡: 已启用');
    console.log('  节点发现: 自动');
    console.log();
  }

  /**
   * 演示安全功能
   */
  private async demonstrateSecurity(): Promise<void> {
    console.log('9. 安全功能演示');
    console.log('='.repeat(30));

    // 启用加密
    this.perceptionSystem.enableAdvancedFeatures({ encryption: true });

    const report = this.perceptionSystem.getExtendedPerformanceReport();
    console.log('安全状态:');
    console.log(`  数据加密: ${report.securityStatus?.encryptionEnabled ? '已启用' : '已禁用'}`);
    console.log(`  数字签名: ${report.securityStatus?.signatureEnabled ? '已启用' : '已禁用'}`);
    console.log('  访问控制: 已配置');
    console.log();
  }

  /**
   * 演示性能监控
   */
  private async demonstratePerformanceMonitoring(): Promise<void> {
    console.log('10. 性能监控演示');
    console.log('='.repeat(30));

    const stats = this.perceptionSystem.getPerformanceStats();
    const report = this.perceptionSystem.getExtendedPerformanceReport();

    console.log('性能指标:');
    console.log(`  总查询次数: ${stats.totalQueries}`);
    console.log(`  平均查询时间: ${stats.averageQueryTime.toFixed(2)}ms`);
    console.log(`  缓存命中率: ${(stats.cacheHitRate * 100).toFixed(1)}%`);
    console.log(`  空间索引效率: ${(stats.spatialIndexEfficiency * 100).toFixed(1)}%`);
    console.log(`  内存使用: ${(stats.memoryUsage / 1024).toFixed(2)}KB`);

    console.log('\n查询时间分布:');
    if (report.queryTimeDistribution) {
      console.log(`  最小值: ${report.queryTimeDistribution.min?.toFixed(2)}ms`);
      console.log(`  最大值: ${report.queryTimeDistribution.max?.toFixed(2)}ms`);
      console.log(`  中位数: ${report.queryTimeDistribution.median?.toFixed(2)}ms`);
      console.log(`  95分位: ${report.queryTimeDistribution.p95?.toFixed(2)}ms`);
    }

    console.log('\n缓存分布:');
    console.log(`  L1缓存: ${report.cacheDistribution?.l1 || 0} 项`);
    console.log(`  L2缓存: ${report.cacheDistribution?.l2 || 0} 项`);
    console.log(`  L3缓存: ${report.cacheDistribution?.l3 || 0} 项`);
    console.log();
  }

  /**
   * 演示高级功能控制
   */
  private async demonstrateAdvancedFeatures(): Promise<void> {
    console.log('11. 高级功能控制演示');
    console.log('='.repeat(30));

    // 启用所有高级功能
    this.perceptionSystem.enableAdvancedFeatures({
      compression: true,
      encryption: true,
      distributedMode: true,
      predictiveCache: true
    });

    console.log('已启用的高级功能:');
    console.log('  ✅ 数据压缩');
    console.log('  ✅ 数据加密');
    console.log('  ✅ 分布式模式');
    console.log('  ✅ 预测性缓存');
    console.log('  ✅ 多级缓存');
    console.log('  ✅ 内存池管理');
    console.log('  ✅ 负载均衡');
    console.log('  ✅ 错误恢复');
    console.log('  ✅ 性能监控');

    // 执行优化参数调整
    this.perceptionSystem.optimizeParameters();
    console.log('\n系统参数已自动优化');

    // 执行系统清理
    this.perceptionSystem.cleanup();
    console.log('系统清理完成');
    console.log();
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.perceptionSystem.cleanup();
    this.entities.clear();
    console.log('示例资源已清理');
  }
}

// 运行示例
if (require.main === module) {
  const example = new OptimizedPerceptionExample();
  example.runExample()
    .then(() => {
      console.log('示例运行完成');
      example.dispose();
    })
    .catch(console.error);
}
