/**
 * 实体类
 * 游戏世界中的基本对象，由组件组成
 */
import { Component } from './Component';
import { World } from './World';
import { Transform } from '../scene/Transform';
import { EventEmitter } from '../utils/EventEmitter';
import { generateUUID } from '../utils/UUID';

export class Entity extends EventEmitter {
  /** 实体ID */
  public id: string = '';

  /** 实体名称 */
  public name: string;

  /** 是否活跃 */
  private active: boolean = true;

  /** 标签列表 */
  private tags: Set<string> = new Set();

  /** 组件映射 */
  private components: Map<string, Component> = new Map();

  /** 子实体列表 */
  private children: Entity[] = [];

  /** 父实体 */
  private parent: Entity | null = null;

  /** 世界引用 */
  private world: World | null = null;

  /** 变换组件 */
  private _transform: Transform;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 创建时间 */
  private createdAt: number;

  /** 实体层级深度 */
  private depth: number = 0;

  /**
   * 创建实体实例
   * @param name 实体名称
   */
  constructor(name: string = '实体') {
    super();
    this.name = name;
    this.id = generateUUID();
    this.createdAt = Date.now();

    // 创建变换组件
    this._transform = new Transform();
    this.addComponent(this._transform);
  }

  /**
   * 更新实体
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.active) {
      return;
    }

    // 更新所有组件
    for (const component of Array.from(this.components.values())) {
      if (component.isEnabled()) {
        component.update(deltaTime);
      }
    }

    // 更新所有子实体
    for (const child of this.children) {
      child.update(deltaTime);
    }
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    if (!this.active) {
      return;
    }

    // 固定更新所有组件
    for (const component of Array.from(this.components.values())) {
      if (component.isEnabled()) {
        component.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新所有子实体
    for (const child of this.children) {
      child.fixedUpdate(fixedDeltaTime);
    }
  }

  /**
   * 添加组件
   * @param component 组件实例
   * @returns 添加的组件
   */
  public addComponent<T extends Component>(component: T): T {
    const type = component.getType();

    // 检查是否已存在同类型组件
    if (this.components.has(type)) {
      console.warn(`实体 ${this.name} 已经有一个 ${type} 组件`);
      return this.components.get(type) as T;
    }

    // 设置组件的实体引用
    component.setEntity(this);

    // 添加到组件映射
    this.components.set(type, component);

    // 发出组件添加事件
    this.emit('componentAdded', component);

    return component;
  }

  /**
   * 获取组件
   * @param type 组件类型
   * @returns 组件实例，如果不存在则返回null
   */
  public getComponent<T extends Component>(type: string): T | null {
    return (this.components.get(type) as T) || null;
  }

  /**
   * 移除组件
   * @param component 组件实例或类型
   * @returns 是否成功移除
   */
  public removeComponent(component: Component | string): boolean {
    const type = typeof component === 'string' ? component : component.getType();

    // 不能移除变换组件
    if (type === 'Transform') {
      console.warn('不能移除变换组件');
      return false;
    }

    if (!this.components.has(type)) {
      return false;
    }

    const componentToRemove = this.components.get(type)!;

    // 销毁组件
    (componentToRemove as any).dispose();

    // 从组件映射中移除
    this.components.delete(type);

    // 发出组件移除事件
    this.emit('componentRemoved', componentToRemove);

    return true;
  }

  /**
   * 获取所有组件
   * @returns 组件数组
   */
  public getAllComponents(): Component[] {
    return Array.from(this.components.values());
  }

  /**
   * 是否有组件
   * @param type 组件类型
   * @returns 是否有该类型的组件
   */
  public hasComponent(type: string): boolean {
    return this.components.has(type);
  }

  /**
   * 添加子实体
   * @param child 子实体
   * @returns 添加的子实体
   */
  public addChild(child: Entity): Entity {
    // 如果已经是子实体，则不做任何操作
    if (this.children.includes(child)) {
      return child;
    }

    // 如果有父实体，则从父实体中移除
    if (child.parent) {
      child.parent.removeChild(child);
    }

    // 设置父实体
    child.parent = this;

    // 更新子实体的深度
    child.updateDepth(this.depth + 1);

    // 添加到子实体列表
    this.children.push(child);

    // 更新变换
    child._transform.setParent(this._transform);

    // 发出子实体添加事件
    this.emit('childAdded', child);

    return child;
  }

  /**
   * 移除子实体
   * @param child 子实体
   * @returns 是否成功移除
   */
  public removeChild(child: Entity): boolean {
    const index = this.children.indexOf(child);

    if (index === -1) {
      return false;
    }

    // 清除父实体引用
    child.parent = null;

    // 从子实体列表中移除
    this.children.splice(index, 1);

    // 更新变换
    child._transform.setParent(null);

    // 发出子实体移除事件
    this.emit('childRemoved', child);

    return true;
  }

  /**
   * 获取所有子实体
   * @returns 子实体数组
   */
  public getChildren(): Entity[] {
    return [...this.children];
  }

  /**
   * 根据名称获取子实体
   * @param name 子实体名称
   * @returns 子实体，如果不存在则返回null
   */
  public getChildByName(name: string): Entity | null {
    return this.children.find(child => child.name === name) || null;
  }

  /**
   * 深度查找子实体（递归查找所有后代）
   * @param name 实体名称
   * @returns 找到的实体，如果不存在则返回null
   */
  public findChildByName(name: string): Entity | null {
    // 首先在直接子实体中查找
    const directChild = this.getChildByName(name);
    if (directChild) {
      return directChild;
    }

    // 递归在子实体的后代中查找
    for (const child of this.children) {
      const found = child.findChildByName(name);
      if (found) {
        return found;
      }
    }

    return null;
  }

  /**
   * 根据标签查找子实体
   * @param tag 标签
   * @param deep 是否深度查找
   * @returns 匹配的子实体数组
   */
  public findChildrenByTag(tag: string, deep: boolean = false): Entity[] {
    const result: Entity[] = [];

    for (const child of this.children) {
      if (child.hasTag(tag)) {
        result.push(child);
      }

      if (deep) {
        result.push(...child.findChildrenByTag(tag, true));
      }
    }

    return result;
  }

  /**
   * 根据组件类型查找子实体
   * @param componentType 组件类型
   * @param deep 是否深度查找
   * @returns 匹配的子实体数组
   */
  public findChildrenByComponent(componentType: string, deep: boolean = false): Entity[] {
    const result: Entity[] = [];

    for (const child of this.children) {
      if (child.hasComponent(componentType)) {
        result.push(child);
      }

      if (deep) {
        result.push(...child.findChildrenByComponent(componentType, true));
      }
    }

    return result;
  }

  /**
   * 遍历所有子实体
   * @param callback 回调函数
   * @param deep 是否深度遍历
   */
  public forEachChild(callback: (child: Entity, index: number) => void, deep: boolean = false): void {
    this.children.forEach((child, index) => {
      callback(child, index);

      if (deep) {
        child.forEachChild(callback, true);
      }
    });
  }

  /**
   * 获取所有后代实体（包括子实体的子实体）
   * @returns 所有后代实体数组
   */
  public getAllDescendants(): Entity[] {
    const descendants: Entity[] = [];

    for (const child of this.children) {
      descendants.push(child);
      descendants.push(...child.getAllDescendants());
    }

    return descendants;
  }

  /**
   * 获取父实体
   * @returns 父实体，如果没有则返回null
   */
  public getParent(): Entity | null {
    return this.parent;
  }

  /**
   * 获取变换组件
   * @returns 变换组件
   */
  public getTransform(): Transform {
    return this._transform;
  }

  /**
   * 获取变换组件（属性访问器）
   * @returns 变换组件
   */
  public get transform(): Transform {
    return this._transform;
  }

  /**
   * 设置世界引用
   * @param world 世界实例
   */
  public setWorld(world: World): void {
    this.world = world;
  }

  /**
   * 获取世界引用
   * @returns 世界实例
   */
  public getWorld(): World | null {
    return this.world;
  }

  /**
   * 设置活跃状态
   * @param active 是否活跃
   */
  public setActive(active: boolean): void {
    if (this.active === active) {
      return;
    }

    this.active = active;

    // 发出活跃状态变更事件
    this.emit('activeChanged', active);

    // 更新所有子实体的活跃状态
    for (const child of this.children) {
      child.setActive(active);
    }
  }

  /**
   * 是否活跃
   * @returns 是否活跃
   */
  public isActive(): boolean {
    // 如果父实体不活跃，则子实体也不活跃
    if (this.parent && !this.parent.isActive()) {
      return false;
    }

    return this.active;
  }

  /**
   * 添加标签
   * @param tag 标签
   */
  public addTag(tag: string): void {
    this.tags.add(tag);
  }

  /**
   * 移除标签
   * @param tag 标签
   * @returns 是否成功移除
   */
  public removeTag(tag: string): boolean {
    return this.tags.delete(tag);
  }

  /**
   * 是否有标签
   * @param tag 标签
   * @returns 是否有该标签
   */
  public hasTag(tag: string): boolean {
    return this.tags.has(tag);
  }

  /**
   * 获取所有标签
   * @returns 标签数组
   */
  public getTags(): string[] {
    return Array.from(this.tags);
  }

  /**
   * 克隆实体
   * @param name 新实体名称（可选）
   * @param deep 是否深度克隆子实体
   * @returns 克隆的实体
   */
  public clone(name?: string, deep: boolean = true): Entity {
    // 创建新实体
    const clonedEntity = new Entity(name || this.name);

    // 复制基本属性
    clonedEntity.setActive(this.active);

    // 复制标签
    for (const tag of this.tags) {
      clonedEntity.addTag(tag);
    }

    // 复制组件（除了Transform，因为构造函数已经创建了）
    for (const [type, component] of this.components) {
      if (type !== 'Transform') {
        // 如果组件有clone方法，使用它；否则尝试序列化/反序列化
        if (typeof (component as any).clone === 'function') {
          const clonedComponent = (component as any).clone();
          clonedEntity.addComponent(clonedComponent);
        } else {
          // 尝试通过序列化/反序列化克隆组件
          try {
            // 暂时跳过无法克隆的组件
            console.warn(`无法克隆组件 ${type}，跳过`);
          } catch (error) {
            console.warn(`克隆组件 ${type} 失败:`, error);
          }
        }
      }
    }

    // 复制Transform组件的属性
    const sourceTransform = this._transform;
    const targetTransform = clonedEntity._transform;

    // 使用公共方法获取和设置Transform属性
    targetTransform.setPosition(sourceTransform.getPosition());
    targetTransform.setRotation(sourceTransform.getRotation());
    targetTransform.setScale(sourceTransform.getScale());

    // 深度克隆子实体
    if (deep) {
      for (const child of this.children) {
        const clonedChild = child.clone(undefined, true);
        clonedEntity.addChild(clonedChild);
      }
    }

    // 发出克隆事件
    this.emit('cloned', clonedEntity);

    return clonedEntity;
  }

  /**
   * 序列化实体
   * @param includeChildren 是否包含子实体
   * @returns 序列化数据
   */
  public serialize(includeChildren: boolean = true): any {
    const data: any = {
      id: this.id,
      name: this.name,
      active: this.active,
      tags: Array.from(this.tags),
      createdAt: this.createdAt,
      depth: this.depth,
      components: {},
      children: []
    };

    // 序列化组件
    for (const [type, component] of this.components) {
      if (typeof (component as any).serialize === 'function') {
        data.components[type] = (component as any).serialize();
      } else {
        // 尝试简单序列化
        try {
          data.components[type] = JSON.parse(JSON.stringify(component));
        } catch (error) {
          console.warn(`无法序列化组件 ${type}:`, error);
        }
      }
    }

    // 序列化子实体
    if (includeChildren) {
      for (const child of this.children) {
        data.children.push(child.serialize(true));
      }
    }

    return data;
  }

  /**
   * 反序列化实体
   * @param data 序列化数据
   * @param world 世界实例（用于创建子实体）
   */
  public deserialize(data: any, world?: World): void {
    if (data.id) this.id = data.id;
    if (data.name) this.name = data.name;
    if (data.active !== undefined) this.setActive(data.active);
    if (data.createdAt) this.createdAt = data.createdAt;
    if (data.depth !== undefined) this.depth = data.depth;

    // 反序列化标签
    if (data.tags && Array.isArray(data.tags)) {
      this.tags.clear();
      for (const tag of data.tags) {
        this.addTag(tag);
      }
    }

    // 反序列化组件
    if (data.components) {
      for (const [type, componentData] of Object.entries(data.components)) {
        const component = this.getComponent(type);
        if (component && typeof (component as any).deserialize === 'function') {
          (component as any).deserialize(componentData);
        }
      }
    }

    // 反序列化子实体
    if (data.children && Array.isArray(data.children) && world) {
      for (const childData of data.children) {
        const child = new Entity(childData.name || '子实体');
        child.deserialize(childData, world);
        this.addChild(child);
      }
    }

    // 发出反序列化事件
    this.emit('deserialized', data);
  }

  /**
   * 更新实体深度
   * @param newDepth 新深度
   */
  private updateDepth(newDepth: number): void {
    this.depth = newDepth;

    // 递归更新所有子实体的深度
    for (const child of this.children) {
      child.updateDepth(newDepth + 1);
    }
  }

  /**
   * 获取实体深度
   * @returns 实体在层级中的深度
   */
  public getDepth(): number {
    return this.depth;
  }

  /**
   * 获取根实体
   * @returns 根实体
   */
  public getRoot(): Entity {
    let root: Entity = this;
    while (root.parent) {
      root = root.parent;
    }
    return root;
  }

  /**
   * 是否是根实体
   * @returns 是否是根实体
   */
  public isRoot(): boolean {
    return this.parent === null;
  }

  /**
   * 是否是叶子实体（没有子实体）
   * @returns 是否是叶子实体
   */
  public isLeaf(): boolean {
    return this.children.length === 0;
  }

  /**
   * 获取兄弟实体
   * @returns 兄弟实体数组
   */
  public getSiblings(): Entity[] {
    if (!this.parent) {
      return [];
    }
    return this.parent.children.filter(child => child !== this);
  }

  /**
   * 获取实体路径（从根到当前实体的路径）
   * @returns 路径字符串
   */
  public getPath(): string {
    const path: string[] = [];
    let current: Entity | null = this;

    while (current) {
      path.unshift(current.name);
      current = current.parent;
    }

    return path.join('/');
  }

  /**
   * 是否已销毁
   * @returns 是否已销毁
   */
  public isDestroyed(): boolean {
    return this.destroyed;
  }

  /**
   * 获取创建时间
   * @returns 创建时间戳
   */
  public getCreatedAt(): number {
    return this.createdAt;
  }

  /**
   * 获取实体信息
   * @returns 实体信息对象
   */
  public getInfo(): {
    id: string;
    name: string;
    active: boolean;
    destroyed: boolean;
    depth: number;
    childCount: number;
    componentCount: number;
    tagCount: number;
    createdAt: number;
    path: string;
  } {
    return {
      id: this.id,
      name: this.name,
      active: this.active,
      destroyed: this.destroyed,
      depth: this.depth,
      childCount: this.children.length,
      componentCount: this.components.size,
      tagCount: this.tags.size,
      createdAt: this.createdAt,
      path: this.getPath()
    };
  }

  /**
   * 销毁实体
   */
  public dispose(): void {
    if (this.destroyed) {
      return;
    }

    // 标记为已销毁
    this.destroyed = true;

    // 发出销毁前事件
    this.emit('beforeDestroy');

    // 移除所有子实体
    while (this.children.length > 0) {
      const child = this.children[0];
      this.removeChild(child);
      (child as any).dispose();
    }

    // 移除所有组件
    for (const component of Array.from(this.components.values())) {
      if (typeof (component as any).dispose === 'function') {
        (component as any).dispose();
      }
    }
    this.components.clear();

    // 如果有父实体，从父实体中移除
    if (this.parent) {
      this.parent.removeChild(this);
    }

    // 清除世界引用
    this.world = null;

    // 清除标签
    this.tags.clear();

    // 发出销毁后事件
    this.emit('destroyed');

    // 移除所有事件监听器
    this.removeAllListeners();
  }

  /**
   * 设置实体名称
   * @param name 新名称
   */
  public setName(name: string): void {
    const oldName = this.name;
    this.name = name;

    // 更新Transform组件的对象名称（如果Transform有相应方法）
    // 暂时跳过，因为Transform类可能没有setName方法

    // 发出名称变更事件
    this.emit('nameChanged', { oldName, newName: name });
  }

  /**
   * 获取实体名称
   * @returns 实体名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置实体ID
   * @param id 新ID
   */
  public setId(id: string): void {
    const oldId = this.id;
    this.id = id;

    // 发出ID变更事件
    this.emit('idChanged', { oldId, newId: id });
  }

  /**
   * 获取实体ID
   * @returns 实体ID
   */
  public getId(): string {
    return this.id;
  }

  /**
   * 比较两个实体是否相等
   * @param other 另一个实体
   * @returns 是否相等
   */
  public equals(other: Entity): boolean {
    return this.id === other.id;
  }

  /**
   * 获取实体的字符串表示
   * @returns 字符串表示
   */
  public toString(): string {
    return `Entity(id: ${this.id}, name: ${this.name}, active: ${this.active}, children: ${this.children.length}, components: ${this.components.size})`;
  }
}
