/**
 * 虚拟化身保存系统
 * 
 * 提供虚拟化身数据的完整保存功能，支持多种保存格式和存储方式
 * 包括数据库保存、文件系统保存、云存储保存等
 */

import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from 'events';
import { AvatarData } from './AvatarCustomizationSystem';

/**
 * 保存配置接口
 */
export interface SaveConfig {
  /** 保存格式 */
  format: 'json' | 'binary' | 'gltf' | 'fbx';
  /** 保存位置 */
  location: 'database' | 'filesystem' | 'cloud';
  /** 压缩选项 */
  compression?: {
    enabled: boolean;
    level: number; // 1-9
    algorithm: 'gzip' | 'brotli' | 'lz4';
  };
  /** 加密选项 */
  encryption?: {
    enabled: boolean;
    algorithm: 'aes-256' | 'rsa';
    key?: string;
  };
  /** 元数据选项 */
  metadata?: {
    includeTimestamp: boolean;
    includeVersion: boolean;
    includeChecksum: boolean;
    customFields?: Record<string, any>;
  };
}

/**
 * 保存结果接口
 */
export interface SaveResult {
  /** 保存是否成功 */
  success: boolean;
  /** 保存的文件路径或ID */
  saveId: string;
  /** 文件大小（字节） */
  fileSize: number;
  /** 保存时间 */
  timestamp: Date;
  /** 校验和 */
  checksum?: string;
  /** 错误信息 */
  error?: string;
  /** 保存的元数据 */
  metadata?: Record<string, any>;
}

/**
 * 虚拟化身保存系统配置
 */
export interface AvatarSaveSystemConfig {
  /** 默认保存配置 */
  defaultSaveConfig: SaveConfig;
  /** 最大文件大小（MB） */
  maxFileSize: number;
  /** 保存目录 */
  saveDirectory: string;
  /** 数据库连接配置 */
  databaseConfig?: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
  };
  /** 云存储配置 */
  cloudConfig?: {
    provider: 'aws' | 'azure' | 'gcp';
    bucket: string;
    region: string;
    credentials: Record<string, string>;
  };
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 虚拟化身保存系统
 */
export class AvatarSaveSystem extends System {
  /** 系统名称 */
  public static readonly NAME = 'AvatarSaveSystem';

  /** 配置 */
  private config: AvatarSaveSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 保存队列 */
  private saveQueue: Map<string, {
    avatarData: AvatarData;
    config: SaveConfig;
    resolve: (result: SaveResult) => void;
    reject: (error: Error) => void;
  }> = new Map();

  /** 正在处理的保存任务 */
  private processingSaves: Set<string> = new Set();

  /** 保存历史记录 */
  private saveHistory: Map<string, SaveResult[]> = new Map();

  /**
   * 构造函数
   */
  constructor(world: World, config: Partial<AvatarSaveSystemConfig> = {}) {
    super(world);

    this.config = {
      defaultSaveConfig: {
        format: 'json',
        location: 'filesystem',
        compression: {
          enabled: true,
          level: 6,
          algorithm: 'gzip'
        },
        metadata: {
          includeTimestamp: true,
          includeVersion: true,
          includeChecksum: true
        }
      },
      maxFileSize: 100, // 100MB
      saveDirectory: './saves/avatars',
      debug: false,
      ...config
    };

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 创建保存目录
    this.ensureSaveDirectory();

    // 启动保存队列处理
    this.startSaveQueueProcessor();

    if (this.config.debug) {
      console.log('虚拟化身保存系统已初始化');
    }
  }

  /**
   * 确保保存目录存在
   */
  private ensureSaveDirectory(): void {
    try {
      const fs = require('fs');
      const path = require('path');
      
      if (!fs.existsSync(this.config.saveDirectory)) {
        fs.mkdirSync(this.config.saveDirectory, { recursive: true });
      }
    } catch (error) {
      console.warn('无法创建保存目录:', error);
    }
  }

  /**
   * 启动保存队列处理器
   */
  private startSaveQueueProcessor(): void {
    setInterval(() => {
      this.processSaveQueue();
    }, 100); // 每100ms处理一次队列
  }

  /**
   * 处理保存队列
   */
  private async processSaveQueue(): Promise<void> {
    for (const [saveId, saveTask] of this.saveQueue) {
      if (!this.processingSaves.has(saveId)) {
        this.processingSaves.add(saveId);
        this.saveQueue.delete(saveId);

        try {
          const result = await this.performSave(saveTask.avatarData, saveTask.config);
          saveTask.resolve(result);
        } catch (error) {
          saveTask.reject(error);
        } finally {
          this.processingSaves.delete(saveId);
        }
      }
    }
  }

  /**
   * 保存虚拟化身
   */
  public async saveAvatar(
    avatarData: AvatarData,
    config?: Partial<SaveConfig>
  ): Promise<SaveResult> {
    const saveConfig = { ...this.config.defaultSaveConfig, ...config };
    const saveId = this.generateSaveId(avatarData.id);

    return new Promise((resolve, reject) => {
      this.saveQueue.set(saveId, {
        avatarData,
        config: saveConfig,
        resolve,
        reject
      });

      this.eventEmitter.emit('saveQueued', { saveId, avatarId: avatarData.id });
    });
  }

  /**
   * 执行保存操作
   */
  private async performSave(avatarData: AvatarData, config: SaveConfig): Promise<SaveResult> {
    const startTime = Date.now();
    
    try {
      // 验证数据
      this.validateAvatarData(avatarData);

      // 序列化数据
      const serializedData = await this.serializeAvatarData(avatarData, config.format);

      // 压缩数据
      let finalData = serializedData;
      if (config.compression?.enabled) {
        finalData = await this.compressData(serializedData, config.compression);
      }

      // 加密数据
      if (config.encryption?.enabled) {
        finalData = await this.encryptData(finalData, config.encryption);
      }

      // 验证文件大小
      const fileSizeInMB = finalData.length / (1024 * 1024);
      if (fileSizeInMB > this.config.maxFileSize) {
        throw new Error(`文件大小 ${fileSizeInMB.toFixed(2)}MB 超过限制 ${this.config.maxFileSize}MB`);
      }

      // 执行保存
      const saveResult = await this.executeSave(avatarData, finalData, config);

      // 记录保存历史
      this.recordSaveHistory(avatarData.id, saveResult);

      // 触发事件
      this.eventEmitter.emit('avatarSaved', saveResult);

      if (this.config.debug) {
        const duration = Date.now() - startTime;
        console.log(`虚拟化身 ${avatarData.id} 保存完成，耗时 ${duration}ms`);
      }

      return saveResult;

    } catch (error) {
      const errorResult: SaveResult = {
        success: false,
        saveId: '',
        fileSize: 0,
        timestamp: new Date(),
        error: error.message
      };

      this.eventEmitter.emit('saveFailed', { avatarId: avatarData.id, error });
      
      if (this.config.debug) {
        console.error(`虚拟化身 ${avatarData.id} 保存失败:`, error);
      }

      return errorResult;
    }
  }

  /**
   * 生成保存ID
   */
  private generateSaveId(avatarId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${avatarId}_${timestamp}_${random}`;
  }

  /**
   * 验证虚拟化身数据
   */
  private validateAvatarData(avatarData: AvatarData): void {
    if (!avatarData.id) {
      throw new Error('虚拟化身ID不能为空');
    }

    // 可以添加更多验证逻辑
  }

  /**
   * 序列化虚拟化身数据
   */
  private async serializeAvatarData(avatarData: AvatarData, format: string): Promise<Buffer> {
    switch (format) {
      case 'json':
        return Buffer.from(JSON.stringify(avatarData, null, 2), 'utf8');
      case 'binary':
        // 简化的二进制序列化
        return Buffer.from(JSON.stringify(avatarData), 'utf8');
      case 'gltf':
      case 'fbx':
        // 这里应该实现3D模型格式的序列化
        // 暂时使用JSON格式
        return Buffer.from(JSON.stringify(avatarData, null, 2), 'utf8');
      default:
        throw new Error(`不支持的保存格式: ${format}`);
    }
  }

  /**
   * 压缩数据
   */
  private async compressData(data: Buffer, compression: NonNullable<SaveConfig['compression']>): Promise<Buffer> {
    try {
      const zlib = require('zlib');
      
      switch (compression.algorithm) {
        case 'gzip':
          return zlib.gzipSync(data, { level: compression.level });
        case 'brotli':
          return zlib.brotliCompressSync(data, {
            params: {
              [zlib.constants.BROTLI_PARAM_QUALITY]: compression.level
            }
          });
        case 'lz4':
          // LZ4压缩需要额外的库，这里简化处理
          return zlib.deflateSync(data, { level: compression.level });
        default:
          throw new Error(`不支持的压缩算法: ${compression.algorithm}`);
      }
    } catch (error) {
      throw new Error(`数据压缩失败: ${error.message}`);
    }
  }

  /**
   * 加密数据
   */
  private async encryptData(data: Buffer, encryption: NonNullable<SaveConfig['encryption']>): Promise<Buffer> {
    try {
      const crypto = require('crypto');

      switch (encryption.algorithm) {
        case 'aes-256':
          const key = encryption.key || crypto.randomBytes(32);
          const iv = crypto.randomBytes(16);
          const cipher = crypto.createCipher('aes-256-cbc', key);

          let encrypted = cipher.update(data);
          encrypted = Buffer.concat([encrypted, cipher.final()]);

          return Buffer.concat([iv, encrypted]);

        case 'rsa':
          // RSA加密实现
          const publicKey = encryption.key;
          if (!publicKey) {
            throw new Error('RSA加密需要提供公钥');
          }
          return crypto.publicEncrypt(publicKey, data);

        default:
          throw new Error(`不支持的加密算法: ${encryption.algorithm}`);
      }
    } catch (error) {
      throw new Error(`数据加密失败: ${error.message}`);
    }
  }

  /**
   * 执行保存操作
   */
  private async executeSave(avatarData: AvatarData, data: Buffer, config: SaveConfig): Promise<SaveResult> {
    switch (config.location) {
      case 'filesystem':
        return await this.saveToFileSystem(avatarData, data, config);
      case 'database':
        return await this.saveToDatabase(avatarData, data, config);
      case 'cloud':
        return await this.saveToCloud(avatarData, data, config);
      default:
        throw new Error(`不支持的保存位置: ${config.location}`);
    }
  }

  /**
   * 保存到文件系统
   */
  private async saveToFileSystem(avatarData: AvatarData, data: Buffer, config: SaveConfig): Promise<SaveResult> {
    try {
      const fs = require('fs');
      const path = require('path');
      const crypto = require('crypto');

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = this.getFileExtension(config.format);
      const fileName = `${avatarData.id}_${timestamp}.${extension}`;
      const filePath = path.join(this.config.saveDirectory, fileName);

      // 写入文件
      await fs.promises.writeFile(filePath, data);

      // 计算校验和
      const checksum = crypto.createHash('sha256').update(data).digest('hex');

      // 生成元数据
      const metadata = this.generateMetadata(avatarData, config, {
        filePath,
        fileName,
        checksum
      });

      // 保存元数据文件
      if (config.metadata?.includeTimestamp || config.metadata?.includeVersion || config.metadata?.includeChecksum) {
        const metadataPath = filePath + '.meta';
        await fs.promises.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      }

      return {
        success: true,
        saveId: fileName,
        fileSize: data.length,
        timestamp: new Date(),
        checksum,
        metadata
      };

    } catch (error) {
      throw new Error(`文件系统保存失败: ${error.message}`);
    }
  }

  /**
   * 保存到数据库
   */
  private async saveToDatabase(avatarData: AvatarData, data: Buffer, config: SaveConfig): Promise<SaveResult> {
    try {
      // 这里应该实现数据库保存逻辑
      // 暂时模拟保存过程
      const saveId = `db_${avatarData.id}_${Date.now()}`;

      // 模拟数据库操作延迟
      await new Promise(resolve => setTimeout(resolve, 100));

      const metadata = this.generateMetadata(avatarData, config, {
        saveId,
        database: this.config.databaseConfig?.database || 'default'
      });

      return {
        success: true,
        saveId,
        fileSize: data.length,
        timestamp: new Date(),
        metadata
      };

    } catch (error) {
      throw new Error(`数据库保存失败: ${error.message}`);
    }
  }

  /**
   * 保存到云存储
   */
  private async saveToCloud(avatarData: AvatarData, data: Buffer, config: SaveConfig): Promise<SaveResult> {
    try {
      // 这里应该实现云存储保存逻辑
      // 暂时模拟保存过程
      const saveId = `cloud_${avatarData.id}_${Date.now()}`;

      // 模拟云存储上传延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const metadata = this.generateMetadata(avatarData, config, {
        saveId,
        bucket: this.config.cloudConfig?.bucket || 'default',
        region: this.config.cloudConfig?.region || 'us-east-1'
      });

      return {
        success: true,
        saveId,
        fileSize: data.length,
        timestamp: new Date(),
        metadata
      };

    } catch (error) {
      throw new Error(`云存储保存失败: ${error.message}`);
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(format: string): string {
    switch (format) {
      case 'json':
        return 'json';
      case 'binary':
        return 'bin';
      case 'gltf':
        return 'gltf';
      case 'fbx':
        return 'fbx';
      default:
        return 'dat';
    }
  }

  /**
   * 生成元数据
   */
  private generateMetadata(avatarData: AvatarData, config: SaveConfig, additionalData: Record<string, any> = {}): Record<string, any> {
    const metadata: Record<string, any> = {
      avatarId: avatarData.id,
      format: config.format,
      location: config.location,
      ...additionalData
    };

    if (config.metadata?.includeTimestamp) {
      metadata.timestamp = new Date().toISOString();
    }

    if (config.metadata?.includeVersion) {
      metadata.version = '1.0.0'; // 可以从配置或包信息获取
    }

    if (config.metadata?.customFields) {
      Object.assign(metadata, config.metadata.customFields);
    }

    return metadata;
  }

  /**
   * 记录保存历史
   */
  private recordSaveHistory(avatarId: string, result: SaveResult): void {
    if (!this.saveHistory.has(avatarId)) {
      this.saveHistory.set(avatarId, []);
    }

    const history = this.saveHistory.get(avatarId)!;
    history.push(result);

    // 限制历史记录数量
    if (history.length > 10) {
      history.shift();
    }
  }

  /**
   * 获取保存历史
   */
  public getSaveHistory(avatarId: string): SaveResult[] {
    return this.saveHistory.get(avatarId) || [];
  }

  /**
   * 删除保存的虚拟化身
   */
  public async deleteSavedAvatar(saveId: string): Promise<boolean> {
    try {
      // 根据saveId的格式判断存储位置
      if (saveId.startsWith('db_')) {
        return await this.deleteFromDatabase(saveId);
      } else if (saveId.startsWith('cloud_')) {
        return await this.deleteFromCloud(saveId);
      } else {
        return await this.deleteFromFileSystem(saveId);
      }
    } catch (error) {
      if (this.config.debug) {
        console.error(`删除保存的虚拟化身失败: ${saveId}`, error);
      }
      return false;
    }
  }

  /**
   * 从文件系统删除
   */
  private async deleteFromFileSystem(saveId: string): Promise<boolean> {
    try {
      const fs = require('fs');
      const path = require('path');

      const filePath = path.join(this.config.saveDirectory, saveId);
      const metadataPath = filePath + '.meta';

      // 删除主文件
      if (fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath);
      }

      // 删除元数据文件
      if (fs.existsSync(metadataPath)) {
        await fs.promises.unlink(metadataPath);
      }

      return true;
    } catch (error) {
      throw new Error(`文件系统删除失败: ${error.message}`);
    }
  }

  /**
   * 从数据库删除
   */
  private async deleteFromDatabase(saveId: string): Promise<boolean> {
    // 模拟数据库删除
    await new Promise(resolve => setTimeout(resolve, 50));
    return true;
  }

  /**
   * 从云存储删除
   */
  private async deleteFromCloud(saveId: string): Promise<boolean> {
    // 模拟云存储删除
    await new Promise(resolve => setTimeout(resolve, 200));
    return true;
  }

  /**
   * 获取保存统计信息
   */
  public getSaveStatistics(): {
    totalSaves: number;
    totalSize: number;
    averageSize: number;
    savesByLocation: Record<string, number>;
    savesByFormat: Record<string, number>;
  } {
    let totalSaves = 0;
    let totalSize = 0;
    const savesByLocation: Record<string, number> = {};
    const savesByFormat: Record<string, number> = {};

    for (const history of this.saveHistory.values()) {
      for (const save of history) {
        if (save.success) {
          totalSaves++;
          totalSize += save.fileSize;

          const location = save.metadata?.location || 'unknown';
          const format = save.metadata?.format || 'unknown';

          savesByLocation[location] = (savesByLocation[location] || 0) + 1;
          savesByFormat[format] = (savesByFormat[format] || 0) + 1;
        }
      }
    }

    return {
      totalSaves,
      totalSize,
      averageSize: totalSaves > 0 ? totalSize / totalSaves : 0,
      savesByLocation,
      savesByFormat
    };
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 系统更新
   */
  public update(deltaTime: number): void {
    // 保存系统通常不需要每帧更新
    // 可以在这里添加定期清理或状态检查逻辑
  }

  /**
   * 系统销毁
   */
  public destroy(): void {
    this.saveQueue.clear();
    this.processingSaves.clear();
    this.saveHistory.clear();
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('虚拟化身保存系统已销毁');
    }
  }
}
