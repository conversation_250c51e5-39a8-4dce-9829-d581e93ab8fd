/**
 * 虚拟化身保存相关的视觉脚本节点
 * 
 * 提供虚拟化身保存、加载、删除等功能的节点实现
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeSlot, SlotType } from '../NodeSlot';
import { AvatarSaveSystem, SaveConfig, SaveResult } from '../../avatar/AvatarSaveSystem';
import { AvatarData } from '../../avatar/AvatarCustomizationSystem';

/**
 * 保存虚拟化身节点
 */
export class SaveAvatarNode extends VisualScriptNode {
  constructor() {
    super('SaveAvatar', '保存虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));
    this.addInputSlot(new NodeSlot('format', '保存格式', SlotType.String, 'json'));
    this.addInputSlot(new NodeSlot('location', '保存位置', SlotType.String, 'filesystem'));
    this.addInputSlot(new NodeSlot('compression', '启用压缩', SlotType.Boolean, true));
    this.addInputSlot(new NodeSlot('encryption', '启用加密', SlotType.Boolean, false));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '保存成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('saveId', '保存ID', SlotType.String));
    this.addOutputSlot(new NodeSlot('fileSize', '文件大小', SlotType.Number));
    this.addOutputSlot(new NodeSlot('saveResult', '保存结果', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const avatarData = this.getInputValue('avatarData') as AvatarData;
    const format = this.getInputValue('format') as string;
    const location = this.getInputValue('location') as string;
    const compression = this.getInputValue('compression') as boolean;
    const encryption = this.getInputValue('encryption') as boolean;

    if (!avatarData) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身数据不能为空');
    }

    try {
      // 获取保存系统
      const saveSystem = this.context?.world?.getSystem('AvatarSaveSystem') as AvatarSaveSystem;
      if (!saveSystem) {
        throw new Error('虚拟化身保存系统未找到');
      }

      // 构建保存配置
      const saveConfig: Partial<SaveConfig> = {
        format: format as any,
        location: location as any,
        compression: compression ? {
          enabled: true,
          level: 6,
          algorithm: 'gzip'
        } : { enabled: false, level: 0, algorithm: 'gzip' },
        encryption: encryption ? {
          enabled: true,
          algorithm: 'aes-256'
        } : { enabled: false, algorithm: 'aes-256' }
      };

      // 执行保存
      const result = await saveSystem.saveAvatar(avatarData, saveConfig);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('saveId', result.saveId);
      this.setOutputValue('fileSize', result.fileSize);
      this.setOutputValue('saveResult', result);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return result;
    } catch (error) {
      console.error('保存虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 加载虚拟化身节点
 */
export class LoadAvatarNode extends VisualScriptNode {
  constructor() {
    super('LoadAvatar', '加载虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('saveId', '保存ID', SlotType.String));
    this.addInputSlot(new NodeSlot('location', '保存位置', SlotType.String, 'filesystem'));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '加载成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));
    this.addOutputSlot(new NodeSlot('metadata', '元数据', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const saveId = this.getInputValue('saveId') as string;
    const location = this.getInputValue('location') as string;

    if (!saveId) {
      this.triggerOutput('onError');
      throw new Error('保存ID不能为空');
    }

    try {
      // 这里应该实现加载逻辑
      // 暂时模拟加载过程
      await new Promise(resolve => setTimeout(resolve, 100));

      const mockAvatarData: AvatarData = {
        id: `loaded_${saveId}`,
        userId: 'user123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockMetadata = {
        saveId,
        location,
        loadedAt: new Date().toISOString()
      };

      // 设置输出值
      this.setOutputValue('success', true);
      this.setOutputValue('avatarData', mockAvatarData);
      this.setOutputValue('metadata', mockMetadata);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return mockAvatarData;
    } catch (error) {
      console.error('加载虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 删除保存的虚拟化身节点
 */
export class DeleteSavedAvatarNode extends VisualScriptNode {
  constructor() {
    super('DeleteSavedAvatar', '删除保存的虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('saveId', '保存ID', SlotType.String));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '删除成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const saveId = this.getInputValue('saveId') as string;

    if (!saveId) {
      this.triggerOutput('onError');
      throw new Error('保存ID不能为空');
    }

    try {
      // 获取保存系统
      const saveSystem = this.context?.world?.getSystem('AvatarSaveSystem') as AvatarSaveSystem;
      if (!saveSystem) {
        throw new Error('虚拟化身保存系统未找到');
      }

      // 执行删除
      const success = await saveSystem.deleteSavedAvatar(saveId);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('删除保存的虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取保存历史节点
 */
export class GetSaveHistoryNode extends VisualScriptNode {
  constructor() {
    super('GetSaveHistory', '获取保存历史', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('history', '保存历史', SlotType.Array));
    this.addOutputSlot(new NodeSlot('count', '保存次数', SlotType.Number));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;

    if (!avatarId) {
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取保存系统
      const saveSystem = this.context?.world?.getSystem('AvatarSaveSystem') as AvatarSaveSystem;
      if (!saveSystem) {
        throw new Error('虚拟化身保存系统未找到');
      }

      // 获取保存历史
      const history = saveSystem.getSaveHistory(avatarId);

      // 设置输出值
      this.setOutputValue('history', history);
      this.setOutputValue('count', history.length);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return history;
    } catch (error) {
      console.error('获取保存历史失败:', error);
      throw error;
    }
  }
}

/**
 * 获取保存统计信息节点
 */
export class GetSaveStatisticsNode extends VisualScriptNode {
  constructor() {
    super('GetSaveStatistics', '获取保存统计', '虚拟化身');

    // 输出插槽
    this.addOutputSlot(new NodeSlot('totalSaves', '总保存次数', SlotType.Number));
    this.addOutputSlot(new NodeSlot('totalSize', '总文件大小', SlotType.Number));
    this.addOutputSlot(new NodeSlot('averageSize', '平均文件大小', SlotType.Number));
    this.addOutputSlot(new NodeSlot('statistics', '统计信息', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    try {
      // 获取保存系统
      const saveSystem = this.context?.world?.getSystem('AvatarSaveSystem') as AvatarSaveSystem;
      if (!saveSystem) {
        throw new Error('虚拟化身保存系统未找到');
      }

      // 获取统计信息
      const statistics = saveSystem.getSaveStatistics();

      // 设置输出值
      this.setOutputValue('totalSaves', statistics.totalSaves);
      this.setOutputValue('totalSize', statistics.totalSize);
      this.setOutputValue('averageSize', statistics.averageSize);
      this.setOutputValue('statistics', statistics);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return statistics;
    } catch (error) {
      console.error('获取保存统计失败:', error);
      throw error;
    }
  }
}

/**
 * 注册虚拟化身保存节点
 */
export function registerAvatarSaveNodes(registry: any): void {
  registry.registerNode('SaveAvatarNode', SaveAvatarNode);
  registry.registerNode('LoadAvatarNode', LoadAvatarNode);
  registry.registerNode('DeleteSavedAvatarNode', DeleteSavedAvatarNode);
  registry.registerNode('GetSaveHistoryNode', GetSaveHistoryNode);
  registry.registerNode('GetSaveStatisticsNode', GetSaveStatisticsNode);
}
