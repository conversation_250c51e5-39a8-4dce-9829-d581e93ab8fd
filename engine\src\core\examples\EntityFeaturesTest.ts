/**
 * Entity功能测试
 * 验证修复后的Entity功能是否正常工作
 */
import { Entity } from '../Entity';
import { World } from '../World';
import { Engine } from '../Engine';
import { Component } from '../Component';

/**
 * 测试组件
 */
class TestComponent extends Component {
  public value: number;

  constructor(value: number = 0) {
    super('TestComponent');
    this.value = value;
  }

  public clone(): TestComponent {
    return new TestComponent(this.value);
  }

  public serialize(): any {
    return { value: this.value };
  }

  public deserialize(data: any): void {
    if (data.value !== undefined) {
      this.value = data.value;
    }
  }
}

/**
 * Entity功能测试类
 */
export class EntityFeaturesTest {
  private engine: Engine;
  private world: World;

  constructor() {
    this.engine = new Engine();
    this.world = new World(this.engine);
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== Entity功能测试开始 ===\n');

    try {
      this.testBasicFeatures();
      this.testHierarchy();
      this.testCloning();
      this.testSerialization();
      this.testQuerying();
      this.testLifecycle();
      this.testUtilityMethods();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    }
  }

  /**
   * 测试基本功能
   */
  private testBasicFeatures(): void {
    console.log('1. 测试基本功能');

    // 创建实体
    const entity = new Entity('测试实体');
    
    // 验证基本属性
    console.log(`实体ID: ${entity.getId()}`);
    console.log(`实体名称: ${entity.getName()}`);
    console.log(`是否活跃: ${entity.isActive()}`);
    console.log(`创建时间: ${new Date(entity.getCreatedAt()).toLocaleString()}`);

    // 测试标签
    entity.addTag('player');
    entity.addTag('character');
    console.log(`标签: ${entity.getTags().join(', ')}`);

    // 测试组件
    const testComponent = new TestComponent(42);
    entity.addComponent(testComponent);
    console.log(`组件数量: ${entity.getAllComponents().length}`);
    console.log(`有TestComponent: ${entity.hasComponent('TestComponent')}`);

    console.log('---\n');
  }

  /**
   * 测试层级结构
   */
  private testHierarchy(): void {
    console.log('2. 测试层级结构');

    // 创建父子实体
    const parent = new Entity('父实体');
    const child1 = new Entity('子实体1');
    const child2 = new Entity('子实体2');
    const grandchild = new Entity('孙子实体');

    // 建立层级关系
    parent.addChild(child1);
    parent.addChild(child2);
    child1.addChild(grandchild);

    // 验证层级关系
    console.log(`父实体子数量: ${parent.getChildren().length}`);
    console.log(`子实体1深度: ${child1.getDepth()}`);
    console.log(`孙子实体深度: ${grandchild.getDepth()}`);
    console.log(`孙子实体路径: ${grandchild.getPath()}`);
    console.log(`孙子实体根: ${grandchild.getRoot().getName()}`);

    // 测试查找功能
    const found = parent.findChildByName('孙子实体');
    console.log(`深度查找结果: ${found ? found.getName() : '未找到'}`);

    // 测试遍历
    console.log('遍历所有后代:');
    parent.forEachChild((child, index) => {
      console.log(`  ${index}: ${child.getName()} (深度: ${child.getDepth()})`);
    }, true);

    console.log('---\n');
  }

  /**
   * 测试克隆功能
   */
  private testCloning(): void {
    console.log('3. 测试克隆功能');

    // 创建原始实体
    const original = new Entity('原始实体');
    original.addTag('original');
    original.addComponent(new TestComponent(100));

    // 创建子实体
    const child = new Entity('子实体');
    child.addComponent(new TestComponent(200));
    original.addChild(child);

    // 克隆实体
    const cloned = original.clone('克隆实体', true);

    // 验证克隆结果
    console.log(`原始实体ID: ${original.getId()}`);
    console.log(`克隆实体ID: ${cloned.getId()}`);
    console.log(`克隆实体名称: ${cloned.getName()}`);
    console.log(`克隆实体标签: ${cloned.getTags().join(', ')}`);
    console.log(`克隆实体子数量: ${cloned.getChildren().length}`);

    const clonedComponent = cloned.getComponent<TestComponent>('TestComponent');
    console.log(`克隆组件值: ${clonedComponent?.value}`);

    console.log('---\n');
  }

  /**
   * 测试序列化功能
   */
  private testSerialization(): void {
    console.log('4. 测试序列化功能');

    // 创建实体
    const entity = new Entity('序列化测试');
    entity.addTag('serializable');
    entity.addComponent(new TestComponent(300));

    // 序列化
    const serialized = entity.serialize();
    console.log('序列化数据:', JSON.stringify(serialized, null, 2));

    // 创建新实体并反序列化
    const newEntity = new Entity('新实体');
    newEntity.deserialize(serialized, this.world);

    console.log(`反序列化后名称: ${newEntity.getName()}`);
    console.log(`反序列化后标签: ${newEntity.getTags().join(', ')}`);

    const deserializedComponent = newEntity.getComponent<TestComponent>('TestComponent');
    console.log(`反序列化后组件值: ${deserializedComponent?.value}`);

    console.log('---\n');
  }

  /**
   * 测试查询功能
   */
  private testQuerying(): void {
    console.log('5. 测试查询功能');

    // 创建测试场景
    const root = new Entity('根实体');
    
    for (let i = 0; i < 3; i++) {
      const child = new Entity(`子实体${i}`);
      child.addTag('child');
      if (i % 2 === 0) {
        child.addComponent(new TestComponent(i * 10));
      }
      root.addChild(child);

      // 添加孙子实体
      const grandchild = new Entity(`孙子实体${i}`);
      grandchild.addTag('grandchild');
      child.addChild(grandchild);
    }

    // 测试各种查询
    const childrenByTag = root.findChildrenByTag('child');
    console.log(`按标签查找子实体数量: ${childrenByTag.length}`);

    const childrenByComponent = root.findChildrenByComponent('TestComponent');
    console.log(`按组件查找子实体数量: ${childrenByComponent.length}`);

    const allDescendants = root.getAllDescendants();
    console.log(`所有后代数量: ${allDescendants.length}`);

    const grandchildrenByTag = root.findChildrenByTag('grandchild', true);
    console.log(`深度查找孙子实体数量: ${grandchildrenByTag.length}`);

    console.log('---\n');
  }

  /**
   * 测试生命周期
   */
  private testLifecycle(): void {
    console.log('6. 测试生命周期');

    const entity = new Entity('生命周期测试');
    
    // 监听事件
    entity.addEventListener('nameChanged', (data) => {
      console.log(`名称变更: ${data.oldName} -> ${data.newName}`);
    });

    entity.addEventListener('activeChanged', (active) => {
      console.log(`活跃状态变更: ${active}`);
    });

    entity.addEventListener('beforeDestroy', () => {
      console.log('实体即将销毁');
    });

    entity.addEventListener('destroyed', () => {
      console.log('实体已销毁');
    });

    // 触发事件
    entity.setName('新名称');
    entity.setActive(false);
    entity.setActive(true);

    console.log(`是否已销毁: ${entity.isDestroyed()}`);
    entity.dispose();
    console.log(`是否已销毁: ${entity.isDestroyed()}`);

    console.log('---\n');
  }

  /**
   * 测试工具方法
   */
  private testUtilityMethods(): void {
    console.log('7. 测试工具方法');

    const entity1 = new Entity('实体1');
    const entity2 = new Entity('实体2');
    const entity3 = new Entity('实体1'); // 同名但不同ID

    // 测试比较
    console.log(`实体1 == 实体2: ${entity1.equals(entity2)}`);
    console.log(`实体1 == 实体3: ${entity1.equals(entity3)}`);

    // 测试信息获取
    const info = entity1.getInfo();
    console.log('实体信息:', info);

    // 测试字符串表示
    console.log(`实体字符串: ${entity1.toString()}`);

    // 测试层级关系
    const parent = new Entity('父实体');
    const child = new Entity('子实体');
    parent.addChild(child);

    console.log(`子实体是否为根: ${child.isRoot()}`);
    console.log(`子实体是否为叶子: ${child.isLeaf()}`);
    console.log(`父实体是否为叶子: ${parent.isLeaf()}`);

    const siblings = child.getSiblings();
    console.log(`子实体兄弟数量: ${siblings.length}`);

    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new EntityFeaturesTest();
  test.runAllTests().catch(console.error);
}
