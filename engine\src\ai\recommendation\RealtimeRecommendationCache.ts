/**
 * 实时推荐缓存
 * 提供高性能的推荐结果缓存和管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Recommendation, CacheConfig } from '../AIRecommendationEngine';

// 缓存项
export interface CacheItem {
  key: string;
  value: Recommendation[];
  timestamp: number;
  ttl: number;                // 生存时间(秒)
  accessCount: number;        // 访问次数
  lastAccessed: number;       // 最后访问时间
  size: number;               // 数据大小(bytes)
}

// 缓存统计
export interface CacheStats {
  totalItems: number;
  totalSize: number;          // 总大小(bytes)
  hitCount: number;           // 命中次数
  missCount: number;          // 未命中次数
  hitRate: number;            // 命中率
  evictionCount: number;      // 驱逐次数
  averageAccessTime: number;  // 平均访问时间(ms)
  memoryUsage: number;        // 内存使用量(bytes)
  compressionRatio: number;   // 压缩比率
  persistenceCount: number;   // 持久化次数
  syncCount: number;          // 同步次数
  preloadCount: number;       // 预加载次数
  errorCount: number;         // 错误次数
}

// 缓存策略
export enum CacheEvictionPolicy {
  LRU = 'lru',               // 最近最少使用
  LFU = 'lfu',               // 最少使用频率
  FIFO = 'fifo',             // 先进先出
  TTL = 'ttl',               // 基于TTL
  SIZE = 'size'              // 基于大小
}

// 缓存配置
export interface RealtimeCacheConfig extends CacheConfig {
  evictionPolicy?: CacheEvictionPolicy;
  maxMemoryUsage?: number;    // 最大内存使用(MB)
  compressionEnabled?: boolean;
  persistenceEnabled?: boolean;
  cleanupInterval?: number;   // 清理间隔(秒)
  persistencePath?: string;   // 持久化文件路径
  syncEnabled?: boolean;      // 是否启用同步
  syncInterval?: number;      // 同步间隔(秒)
  preloadEnabled?: boolean;   // 是否启用预加载
  preloadThreshold?: number;  // 预加载阈值
  compressionLevel?: number;  // 压缩级别(1-9)
  batchSize?: number;         // 批量操作大小
}

/**
 * 实时推荐缓存
 */
export class RealtimeRecommendationCache {
  private config: RealtimeCacheConfig;
  private eventEmitter: EventEmitter = new EventEmitter();
  
  // 缓存存储
  private cache: Map<string, CacheItem> = new Map();
  
  // 访问顺序记录(用于LRU)
  private accessOrder: string[] = [];
  
  // 访问频率记录(用于LFU)
  private accessFrequency: Map<string, number> = new Map();
  
  // 缓存统计
  private stats: CacheStats = {
    totalItems: 0,
    totalSize: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    evictionCount: 0,
    averageAccessTime: 0,
    memoryUsage: 0,
    compressionRatio: 1.0,
    persistenceCount: 0,
    syncCount: 0,
    preloadCount: 0,
    errorCount: 0
  };
  
  // 清理定时器
  private cleanupTimer?: NodeJS.Timeout;

  // 同步定时器
  private syncTimer?: NodeJS.Timeout;

  // 压缩工具
  private compressionEnabled: boolean = false;

  // 持久化路径
  private persistencePath: string = './cache';

  constructor(config: Partial<RealtimeCacheConfig> = {}) {
    this.config = {
      enabled: true,
      ttl: 3600,
      maxSize: 1000,
      evictionPolicy: CacheEvictionPolicy.LRU,
      maxMemoryUsage: 100,
      compressionEnabled: false,
      persistenceEnabled: false,
      cleanupInterval: 300,
      persistencePath: './cache',
      syncEnabled: false,
      syncInterval: 60,
      preloadEnabled: true,
      preloadThreshold: 0.8,
      compressionLevel: 6,
      batchSize: 100,
      ...config
    };

    this.compressionEnabled = this.config.compressionEnabled || false;
    this.persistencePath = this.config.persistencePath || './cache';

    if (this.config.enabled) {
      this.startCleanupTimer();
      if (this.config.syncEnabled) {
        this.startSyncTimer();
      }
      if (this.config.persistenceEnabled) {
        this.loadFromPersistence();
      }
    }
  }

  /**
   * 获取缓存项
   */
  public async get(key: string): Promise<Recommendation[] | null> {
    const startTime = Date.now();

    try {
      const item = this.cache.get(key);
      
      if (!item) {
        this.stats.missCount++;
        this.updateHitRate();
        return null;
      }

      // 检查TTL
      if (this.isExpired(item)) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
        this.stats.missCount++;
        this.updateHitRate();
        return null;
      }

      // 更新访问信息
      item.accessCount++;
      item.lastAccessed = Date.now();
      this.updateAccessOrder(key);
      this.updateAccessFrequency(key);

      this.stats.hitCount++;
      this.updateHitRate();

      // 触发缓存命中事件
      this.eventEmitter.emit('cache.hit', { key, accessTime: Date.now() - startTime });

      return item.value;

    } catch (error) {
      console.error('缓存获取失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'get', key, error });
      return null;
    } finally {
      this.updateAverageAccessTime(Date.now() - startTime);
    }
  }

  /**
   * 设置缓存项
   */
  public async set(key: string, value: Recommendation[]): Promise<void> {
    try {
      const size = this.calculateSize(value);
      const now = Date.now();

      const item: CacheItem = {
        key,
        value,
        timestamp: now,
        ttl: this.config.ttl!,
        accessCount: 1,
        lastAccessed: now,
        size
      };

      // 检查是否需要驱逐
      await this.ensureCapacity(size);

      // 存储缓存项
      this.cache.set(key, item);
      this.updateAccessOrder(key);
      this.updateAccessFrequency(key);

      // 更新统计
      this.stats.totalItems = this.cache.size;
      this.stats.totalSize += size;

      // 触发缓存设置事件
      this.eventEmitter.emit('cache.set', { key, size, ttl: item.ttl });

    } catch (error) {
      console.error('缓存设置失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'set', key, error });
    }
  }

  /**
   * 删除缓存项
   */
  public async delete(key: string): Promise<boolean> {
    try {
      const item = this.cache.get(key);
      if (!item) {
        return false;
      }

      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.accessFrequency.delete(key);

      // 更新统计
      this.stats.totalItems = this.cache.size;
      this.stats.totalSize -= item.size;

      // 触发缓存删除事件
      this.eventEmitter.emit('cache.delete', { key, size: item.size });

      return true;

    } catch (error) {
      console.error('缓存删除失败:', error);
      this.eventEmitter.emit('cache.error', { operation: 'delete', key, error });
      return false;
    }
  }

  /**
   * 批量获取缓存项
   */
  public async batchGet(keys: string[]): Promise<Map<string, Recommendation[] | null>> {
    const results = new Map<string, Recommendation[] | null>();
    const batchSize = this.config.batchSize || 100;

    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);
      const batchPromises = batch.map(async key => {
        const value = await this.get(key);
        return { key, value };
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ key, value }) => {
        results.set(key, value);
      });
    }

    return results;
  }

  /**
   * 批量设置缓存项
   */
  public async batchSet(entries: Array<{ key: string; value: Recommendation[] }>): Promise<void> {
    const batchSize = this.config.batchSize || 100;

    for (let i = 0; i < entries.length; i += batchSize) {
      const batch = entries.slice(i, i + batchSize);
      const batchPromises = batch.map(entry => this.set(entry.key, entry.value));
      await Promise.all(batchPromises);
    }
  }

  /**
   * 批量删除缓存项
   */
  public async batchDelete(keys: string[]): Promise<number> {
    let deletedCount = 0;
    const batchSize = this.config.batchSize || 100;

    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);
      const batchPromises = batch.map(async key => {
        const deleted = await this.delete(key);
        return deleted ? 1 : 0;
      });

      const batchResults = await Promise.all(batchPromises);
      deletedCount += batchResults.reduce((sum, count) => sum + count, 0);
    }

    return deletedCount;
  }

  /**
   * 清除用户相关缓存
   */
  public async clearUserCache(userId: string): Promise<void> {
    const keysToDelete: string[] = [];

    Array.from(this.cache.keys()).forEach(key => {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    });

    const deletedCount = await this.batchDelete(keysToDelete);
    this.eventEmitter.emit('cache.user.cleared', { userId, clearedCount: deletedCount });
  }

  /**
   * 清空所有缓存
   */
  public async clear(): Promise<void> {
    const itemCount = this.cache.size;
    
    this.cache.clear();
    this.accessOrder = [];
    this.accessFrequency.clear();

    // 重置统计
    this.stats.totalItems = 0;
    this.stats.totalSize = 0;

    this.eventEmitter.emit('cache.cleared', { clearedCount: itemCount });
  }

  /**
   * 获取缓存统计
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 预热缓存
   */
  public async warmup(entries: Array<{ key: string; value: Recommendation[] }>): Promise<void> {
    const startTime = Date.now();
    let successCount = 0;

    for (const entry of entries) {
      try {
        await this.set(entry.key, entry.value);
        successCount++;
      } catch (error) {
        console.error(`缓存预热失败: ${entry.key}`, error);
      }
    }

    const duration = Date.now() - startTime;
    this.eventEmitter.emit('cache.warmup.completed', {
      totalEntries: entries.length,
      successCount,
      duration
    });
  }

  /**
   * 获取内存使用情况
   */
  public getMemoryUsage(): {
    totalMemory: number;
    usedMemory: number;
    freeMemory: number;
    memoryUtilization: number;
    largestItems: Array<{ key: string; size: number }>;
  } {
    const totalMemory = (this.config.maxMemoryUsage || 100) * 1024 * 1024; // 转换为字节
    const usedMemory = this.stats.totalSize;
    const freeMemory = totalMemory - usedMemory;
    const memoryUtilization = usedMemory / totalMemory;

    // 找出最大的缓存项
    const largestItems = Array.from(this.cache.entries())
      .map(([key, item]) => ({ key, size: item.size }))
      .sort((a, b) => b.size - a.size)
      .slice(0, 10);

    return {
      totalMemory,
      usedMemory,
      freeMemory,
      memoryUtilization,
      largestItems
    };
  }

  /**
   * 获取性能分析报告
   */
  public getPerformanceReport(): {
    cacheEfficiency: number;
    averageItemSize: number;
    accessPatterns: Map<string, number>;
    hotKeys: string[];
    coldKeys: string[];
    evictionAnalysis: {
      totalEvictions: number;
      evictionRate: number;
      mostEvictedTypes: string[];
    };
  } {
    const cacheEfficiency = this.stats.hitRate;
    const averageItemSize = this.stats.totalItems > 0 ? this.stats.totalSize / this.stats.totalItems : 0;

    // 访问模式分析
    const accessPatterns = new Map<string, number>();
    Array.from(this.cache.entries()).forEach(([key, item]) => {
      const keyType = key.split(':')[0] || 'unknown';
      accessPatterns.set(keyType, (accessPatterns.get(keyType) || 0) + item.accessCount);
    });

    // 热点和冷点键
    const sortedByAccess = Array.from(this.cache.entries())
      .sort((a, b) => b[1].accessCount - a[1].accessCount);

    const hotKeys = sortedByAccess.slice(0, 10).map(([key]) => key);
    const coldKeys = sortedByAccess.slice(-10).map(([key]) => key);

    // 驱逐分析
    const evictionRate = this.stats.totalItems > 0 ? this.stats.evictionCount / this.stats.totalItems : 0;
    const mostEvictedTypes = Array.from(accessPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([type]) => type);

    return {
      cacheEfficiency,
      averageItemSize,
      accessPatterns,
      hotKeys,
      coldKeys,
      evictionAnalysis: {
        totalEvictions: this.stats.evictionCount,
        evictionRate,
        mostEvictedTypes
      }
    };
  }

  /**
   * 检查缓存健康状态
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
    metrics: {
      hitRate: number;
      memoryUtilization: number;
      evictionRate: number;
      errorRate: number;
    };
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查命中率
    if (this.stats.hitRate < 0.5) {
      issues.push('缓存命中率过低');
      recommendations.push('考虑调整TTL或缓存策略');
    }

    // 检查内存使用
    const memoryUsage = this.getMemoryUsage();
    if (memoryUsage.memoryUtilization > 0.9) {
      issues.push('内存使用接近限制');
      recommendations.push('考虑增加内存限制或启用压缩');
    }

    // 检查驱逐频率
    const evictionRate = this.stats.totalItems > 0 ? this.stats.evictionCount / this.stats.totalItems : 0;
    if (evictionRate > 0.5) {
      issues.push('缓存驱逐频率过高');
      recommendations.push('考虑增加缓存大小或调整驱逐策略');
    }

    // 检查错误率
    const totalOperations = this.stats.hitCount + this.stats.missCount;
    const errorRate = totalOperations > 0 ? this.stats.errorCount / totalOperations : 0;
    if (errorRate > 0.05) {
      issues.push('错误率过高');
      recommendations.push('检查系统资源和网络连接');
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'warning';
    }

    return {
      status,
      issues,
      recommendations,
      metrics: {
        hitRate: this.stats.hitRate,
        memoryUtilization: memoryUsage.memoryUtilization,
        evictionRate,
        errorRate
      }
    };
  }

  // 私有方法
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl * 1000;
  }

  private calculateSize(value: Recommendation[]): number {
    const jsonString = JSON.stringify(value);
    const originalSize = jsonString.length * 2; // 假设每个字符2字节

    if (this.compressionEnabled) {
      // 模拟压缩后的大小（实际应该使用真实的压缩算法）
      const compressionRatio = 0.6; // 假设压缩比为60%
      return Math.floor(originalSize * compressionRatio);
    }

    return originalSize;
  }

  /**
   * 压缩数据
   */
  private compressData(data: Recommendation[]): string {
    const jsonString = JSON.stringify(data);

    if (!this.compressionEnabled) {
      return jsonString;
    }

    // 简单的压缩模拟（实际应该使用 zlib 或其他压缩库）
    try {
      // 这里应该使用真实的压缩算法，比如 zlib.gzip
      const compressed = this.simpleCompress(jsonString);

      // 更新压缩比率统计
      const originalSize = jsonString.length;
      const compressedSize = compressed.length;
      this.stats.compressionRatio = compressedSize / originalSize;

      return compressed;
    } catch (error) {
      console.warn('数据压缩失败，使用原始数据:', error);
      this.stats.errorCount++;
      return jsonString;
    }
  }

  /**
   * 解压数据
   */
  private decompressData(compressedData: string): Recommendation[] {
    try {
      if (!this.compressionEnabled) {
        return JSON.parse(compressedData);
      }

      // 简单的解压模拟
      const decompressed = this.simpleDecompress(compressedData);
      return JSON.parse(decompressed);
    } catch (error) {
      console.error('数据解压失败:', error);
      this.stats.errorCount++;
      throw error;
    }
  }

  /**
   * 简单压缩模拟（实际应该使用专业压缩库）
   */
  private simpleCompress(data: string): string {
    // 这是一个简化的压缩模拟，实际应该使用 zlib.gzip 等
    return Buffer.from(data).toString('base64');
  }

  /**
   * 简单解压模拟
   */
  private simpleDecompress(compressedData: string): string {
    // 这是一个简化的解压模拟
    return Buffer.from(compressedData, 'base64').toString();
  }

  /**
   * 持久化缓存到磁盘
   */
  private async persistToDisk(): Promise<void> {
    if (!this.config.persistenceEnabled) {
      return;
    }

    try {
      const cacheData = {
        cache: Array.from(this.cache.entries()),
        accessOrder: this.accessOrder,
        accessFrequency: Array.from(this.accessFrequency.entries()),
        stats: this.stats,
        timestamp: Date.now()
      };

      const serializedData = JSON.stringify(cacheData);

      // 在实际实现中，这里应该使用 fs.writeFile
      // await fs.writeFile(this.persistencePath, serializedData);

      this.stats.persistenceCount++;
      this.eventEmitter.emit('cache.persisted', {
        size: serializedData.length,
        itemCount: this.cache.size
      });

    } catch (error) {
      console.error('缓存持久化失败:', error);
      this.stats.errorCount++;
      this.eventEmitter.emit('cache.error', { operation: 'persist', error });
    }
  }

  /**
   * 从磁盘加载缓存
   */
  private async loadFromPersistence(): Promise<void> {
    if (!this.config.persistenceEnabled) {
      return;
    }

    try {
      // 在实际实现中，这里应该使用 fs.readFile
      // const data = await fs.readFile(this.persistencePath, 'utf8');
      // const cacheData = JSON.parse(data);

      // 模拟加载过程
      const cacheData = {
        cache: [],
        accessOrder: [],
        accessFrequency: [],
        stats: this.stats,
        timestamp: Date.now()
      };

      // 恢复缓存数据
      this.cache.clear();
      this.accessOrder = [];
      this.accessFrequency.clear();

      for (const [key, item] of cacheData.cache as [string, CacheItem][]) {
        // 检查数据是否过期
        if (!this.isExpired(item)) {
          this.cache.set(key, item);
        }
      }

      this.accessOrder = cacheData.accessOrder as string[];
      this.accessFrequency = new Map(cacheData.accessFrequency as [string, number][]);

      this.eventEmitter.emit('cache.loaded', {
        itemCount: this.cache.size,
        timestamp: cacheData.timestamp
      });

    } catch (error) {
      console.warn('缓存加载失败，使用空缓存:', error);
      this.stats.errorCount++;
    }
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      this.performSync();
    }, (this.config.syncInterval || 60) * 1000);
  }

  /**
   * 执行同步操作
   */
  private async performSync(): Promise<void> {
    try {
      // 持久化当前缓存
      await this.persistToDisk();

      // 同步统计信息
      this.stats.syncCount++;

      this.eventEmitter.emit('cache.synced', {
        timestamp: Date.now(),
        itemCount: this.cache.size
      });

    } catch (error) {
      console.error('缓存同步失败:', error);
      this.stats.errorCount++;
      this.eventEmitter.emit('cache.error', { operation: 'sync', error });
    }
  }

  /**
   * 智能预加载
   */
  public async preloadRecommendations(
    userId: string,
    patterns: Array<{ type: string; context: any }>
  ): Promise<void> {
    if (!this.config.preloadEnabled) {
      return;
    }

    try {
      const preloadPromises = patterns.map(async pattern => {
        const cacheKey = this.generatePreloadKey(userId, pattern);

        // 检查是否已经缓存
        if (this.cache.has(cacheKey)) {
          return;
        }

        // 这里应该调用推荐引擎生成预加载数据
        // const recommendations = await this.recommendationEngine.generate(pattern);
        // await this.set(cacheKey, recommendations);

        // 模拟预加载
        const mockRecommendations: Recommendation[] = [];
        await this.set(cacheKey, mockRecommendations);
      });

      await Promise.all(preloadPromises);

      this.stats.preloadCount += patterns.length;
      this.eventEmitter.emit('cache.preloaded', {
        userId,
        patternCount: patterns.length
      });

    } catch (error) {
      console.error('预加载失败:', error);
      this.stats.errorCount++;
      this.eventEmitter.emit('cache.error', { operation: 'preload', error });
    }
  }

  /**
   * 生成预加载缓存键
   */
  private generatePreloadKey(userId: string, pattern: { type: string; context: any }): string {
    return `preload:${userId}:${pattern.type}:${JSON.stringify(pattern.context)}`;
  }

  /**
   * 优化缓存性能
   */
  public async optimizeCache(): Promise<{
    removedExpired: number;
    compactedItems: number;
    memoryFreed: number;
  }> {
    const startTime = Date.now();
    let removedExpired = 0;
    let compactedItems = 0;
    let memoryFreed = 0;

    try {
      // 1. 清理过期项
      const expiredKeys: string[] = [];
      Array.from(this.cache.entries()).forEach(([key, item]) => {
        if (this.isExpired(item)) {
          expiredKeys.push(key);
          memoryFreed += item.size;
        }
      });

      for (const key of expiredKeys) {
        await this.delete(key);
        removedExpired++;
      }

      // 2. 压缩低频访问项
      if (this.compressionEnabled) {
        const lowFrequencyItems = Array.from(this.cache.entries())
          .filter(([, item]) => item.accessCount < 2)
          .slice(0, 50); // 限制批量处理数量

        for (const [key, item] of lowFrequencyItems) {
          try {
            const compressed = this.compressData(item.value);
            const originalSize = item.size;
            const newSize = compressed.length * 2;

            if (newSize < originalSize) {
              // 更新缓存项（这里简化处理，实际应该存储压缩标记）
              item.size = newSize;
              memoryFreed += (originalSize - newSize);
              compactedItems++;
            }
          } catch (error) {
            console.warn(`压缩缓存项失败: ${key}`, error);
          }
        }
      }

      // 3. 更新统计信息
      this.stats.totalSize -= memoryFreed;

      const optimizationResult = {
        removedExpired,
        compactedItems,
        memoryFreed
      };

      this.eventEmitter.emit('cache.optimized', {
        ...optimizationResult,
        duration: Date.now() - startTime
      });

      return optimizationResult;

    } catch (error) {
      console.error('缓存优化失败:', error);
      this.stats.errorCount++;
      throw error;
    }
  }

  /**
   * 获取缓存键的生成器（用于生成一致的缓存键）
   */
  public generateCacheKey(
    userId: string,
    type: string,
    context: Record<string, any>
  ): string {
    const contextHash = this.hashObject(context);
    return `${type}:${userId}:${contextHash}`;
  }

  /**
   * 对象哈希生成器
   */
  private hashObject(obj: Record<string, any>): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  private async ensureCapacity(newItemSize: number): Promise<void> {
    // 检查数量限制
    while (this.cache.size >= this.config.maxSize!) {
      await this.evictItem();
    }

    // 检查内存限制
    const maxMemoryBytes = this.config.maxMemoryUsage! * 1024 * 1024;
    while (this.stats.totalSize + newItemSize > maxMemoryBytes) {
      await this.evictItem();
    }
  }

  private async evictItem(): Promise<void> {
    let keyToEvict: string | null = null;

    switch (this.config.evictionPolicy) {
      case CacheEvictionPolicy.LRU:
        keyToEvict = this.accessOrder[0] || null;
        break;
      
      case CacheEvictionPolicy.LFU:
        keyToEvict = this.findLeastFrequentlyUsed();
        break;
      
      case CacheEvictionPolicy.FIFO:
        keyToEvict = this.cache.keys().next().value || null;
        break;
      
      case CacheEvictionPolicy.TTL:
        keyToEvict = this.findExpiredItem();
        break;
      
      case CacheEvictionPolicy.SIZE:
        keyToEvict = this.findLargestItem();
        break;
    }

    if (keyToEvict) {
      await this.delete(keyToEvict);
      this.stats.evictionCount++;
      this.eventEmitter.emit('cache.eviction', { key: keyToEvict, policy: this.config.evictionPolicy });
    }
  }

  private findLeastFrequentlyUsed(): string | null {
    let minFrequency = Infinity;
    let leastUsedKey: string | null = null;

    Array.from(this.accessFrequency.entries()).forEach(([key, frequency]) => {
      if (frequency < minFrequency) {
        minFrequency = frequency;
        leastUsedKey = key;
      }
    });

    return leastUsedKey;
  }

  private findExpiredItem(): string | null {
    for (const [key, item] of Array.from(this.cache.entries())) {
      if (this.isExpired(item)) {
        return key;
      }
    }
    return null;
  }

  private findLargestItem(): string | null {
    let maxSize = 0;
    let largestKey: string | null = null;

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (item.size > maxSize) {
        maxSize = item.size;
        largestKey = key;
      }
    });

    return largestKey;
  }

  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private updateAccessFrequency(key: string): void {
    const current = this.accessFrequency.get(key) || 0;
    this.accessFrequency.set(key, current + 1);
  }

  private updateHitRate(): void {
    const total = this.stats.hitCount + this.stats.missCount;
    this.stats.hitRate = total > 0 ? this.stats.hitCount / total : 0;
  }

  private updateAverageAccessTime(accessTime: number): void {
    const total = this.stats.hitCount + this.stats.missCount;
    if (total === 1) {
      this.stats.averageAccessTime = accessTime;
    } else {
      this.stats.averageAccessTime = 
        (this.stats.averageAccessTime * (total - 1) + accessTime) / total;
    }
  }

  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval! * 1000);
  }

  private async performCleanup(): Promise<void> {
    const expiredKeys: string[] = [];

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (this.isExpired(item)) {
        expiredKeys.push(key);
      }
    });

    for (const key of expiredKeys) {
      await this.delete(key);
    }

    if (expiredKeys.length > 0) {
      this.eventEmitter.emit('cache.cleanup', { expiredCount: expiredKeys.length });
    }
  }

  /**
   * 手动清理过期缓存
   */
  public async cleanup(): Promise<void> {
    await this.performCleanup();
  }

  /**
   * 销毁缓存
   */
  public async destroy(): Promise<void> {
    // 清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }

    // 最后一次持久化
    if (this.config.persistenceEnabled) {
      await this.persistToDisk();
    }

    // 清理缓存和事件监听器
    await this.clear();
    this.eventEmitter.removeAllListeners();

    // 触发销毁事件
    this.eventEmitter.emit('cache.destroyed', {
      timestamp: Date.now(),
      finalStats: this.getStats()
    });
  }

  /**
   * 事件监听
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
