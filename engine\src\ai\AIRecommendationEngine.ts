/**
 * AI智能推荐引擎
 * 提供多种推荐算法和个性化推荐功能
 */
import { System } from '../core/System';
import { EventEmitter } from '../utils/EventEmitter';
import { UserBehaviorAnalyzer } from './recommendation/UserBehaviorAnalyzer';
import { ContentFeatureExtractor } from './recommendation/ContentFeatureExtractor';
import { RealtimeRecommendationCache } from './recommendation/RealtimeRecommendationCache';

// 推荐类型枚举
export enum RecommendationType {
  ASSET = 'asset',                    // 资产推荐
  SCENE_TEMPLATE = 'scene_template',  // 场景模板推荐
  COLLABORATOR = 'collaborator',      // 协作者推荐
  LEARNING_PATH = 'learning_path',    // 学习路径推荐
  MATERIAL = 'material',              // 材质推荐
  COMPONENT = 'component'             // 组件推荐
}

// 推荐上下文
export interface RecommendationContext {
  userId: string;
  projectId?: string;
  sceneId?: string;
  currentActivity: string;
  timeOfDay: number;
  deviceType: string;
  userPreferences: UserPreferences;
  sessionData: SessionData;
}

// 用户偏好
export interface UserPreferences {
  preferredStyles: string[];
  skillLevel: SkillLevel;
  interests: string[];
  workingHours: TimeRange;
  collaborationStyle: CollaborationStyle;
}

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export enum CollaborationStyle {
  INDEPENDENT = 'independent',
  COLLABORATIVE = 'collaborative',
  MENTORING = 'mentoring',
  LEADING = 'leading'
}

// 时间范围
export interface TimeRange {
  start: number; // 小时 (0-23)
  end: number;   // 小时 (0-23)
}

// 会话数据
export interface SessionData {
  duration: number;           // 会话时长(分钟)
  actionsCount: number;       // 操作次数
  errorsCount: number;        // 错误次数
  completedTasks: string[];   // 完成的任务
  currentFocus: string;       // 当前关注点
}

// 推荐结果
export interface Recommendation {
  id: string;
  type: RecommendationType;
  title: string;
  description: string;
  confidence: number;         // 置信度 (0-1)
  relevanceScore: number;     // 相关性评分 (0-1)
  metadata: RecommendationMetadata;
  actions: RecommendationAction[];
}

// 推荐元数据
export interface RecommendationMetadata {
  category: string;
  tags: string[];
  difficulty: SkillLevel;
  estimatedTime: number;      // 预估时间(分钟)
  popularity: number;         // 受欢迎程度 (0-1)
  lastUpdated: Date;
  source: string;             // 推荐来源
}

// 推荐操作
export interface RecommendationAction {
  type: string;
  label: string;
  data: any;
  primary: boolean;
}

// 用户画像
export interface UserProfile {
  userId: string;
  demographics: UserDemographics;
  behaviorPatterns: BehaviorPattern[];
  skillAssessment: SkillAssessment;
  preferences: UserPreferences;
  collaborationHistory: CollaborationRecord[];
  learningProgress: LearningProgress;
}

// 用户人口统计信息
export interface UserDemographics {
  ageGroup: string;
  location: string;
  profession: string;
  experience: number;         // 经验年数
}

// 行为模式
export interface BehaviorPattern {
  pattern: string;
  frequency: number;
  context: string[];
  timePattern: number[];      // 24小时模式
}

// 技能评估
export interface SkillAssessment {
  overallLevel: SkillLevel;
  specificSkills: Map<string, number>; // 技能名称 -> 熟练度(0-1)
  learningSpeed: number;      // 学习速度 (0-1)
  adaptability: number;       // 适应性 (0-1)
}

// 协作记录
export interface CollaborationRecord {
  projectId: string;
  collaborators: string[];
  role: string;
  duration: number;
  satisfaction: number;       // 满意度 (0-1)
  effectiveness: number;      // 效率 (0-1)
}

// 学习进度
export interface LearningProgress {
  completedCourses: string[];
  currentGoals: string[];
  weakAreas: string[];
  strongAreas: string[];
  lastActivity: Date;
}

// 推荐算法接口
export interface RecommendationAlgorithm {
  name: string;
  version: string;
  recommend(request: RecommendationRequest): Promise<Recommendation[]>;
  train?(data: TrainingData): Promise<void>;
  evaluate?(testData: TestData): Promise<EvaluationResult>;
}

// 推荐请求
export interface RecommendationRequest {
  type: RecommendationType;
  context: RecommendationContext;
  count: number;
  filters?: RecommendationFilter[];
  userProfile?: UserProfile;
}

// 推荐过滤器
export interface RecommendationFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'contains';
  value: any;
}

// 训练数据
export interface TrainingData {
  interactions: UserInteraction[];
  feedback: UserFeedback[];
  contextData: ContextData[];
}

// 用户交互
export interface UserInteraction {
  userId: string;
  itemId: string;
  action: string;
  timestamp: Date;
  context: any;
  duration?: number;
}

// 用户反馈
export interface UserFeedback {
  userId: string;
  recommendationId: string;
  rating: number;             // 评分 (1-5)
  comment?: string;
  timestamp: Date;
}

// 上下文数据
export interface ContextData {
  timestamp: Date;
  context: RecommendationContext;
  outcomes: string[];
}

// 测试数据
export interface TestData {
  testCases: TestCase[];
}

export interface TestCase {
  input: RecommendationRequest;
  expectedOutput: Recommendation[];
  actualOutput?: Recommendation[];
}

// 评估结果
export interface EvaluationResult {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  coverage: number;
  diversity: number;
  novelty: number;
}

// 推荐配置
export interface AIRecommendationConfig {
  algorithms?: AlgorithmConfig[];
  caching?: CacheConfig;
  evaluation?: EvaluationConfig;
  privacy?: PrivacyConfig;
  debug?: boolean;
}

export interface AlgorithmConfig {
  name: string;
  weight: number;
  parameters: Record<string, any>;
  enabled: boolean;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number;                // 缓存时间(秒)
  maxSize: number;            // 最大缓存条目数
}

export interface EvaluationConfig {
  enabled: boolean;
  interval: number;           // 评估间隔(小时)
  metrics: string[];
}

export interface PrivacyConfig {
  anonymizeData: boolean;
  dataRetention: number;      // 数据保留天数
  consentRequired: boolean;
}

/**
 * AI智能推荐引擎
 */
export class AIRecommendationEngine extends System {
  static readonly NAME = 'AIRecommendationEngine';

  // 推荐算法集合
  private algorithms: Map<RecommendationType, RecommendationAlgorithm> = new Map();

  // 用户行为分析器
  private behaviorAnalyzer: UserBehaviorAnalyzer;

  // 内容特征提取器
  private featureExtractor: ContentFeatureExtractor;

  // 实时推荐缓存
  private realtimeCache: RealtimeRecommendationCache;

  // 事件发射器
  private eventEmitter: EventEmitter = new EventEmitter();

  // 配置
  private config: AIRecommendationConfig;

  constructor(config: AIRecommendationConfig = {}) {
    super(350); // 设置系统优先级

    this.config = {
      algorithms: [],
      caching: {
        enabled: true,
        ttl: 3600,
        maxSize: 1000
      },
      evaluation: {
        enabled: true,
        interval: 24,
        metrics: ['accuracy', 'precision', 'recall']
      },
      privacy: {
        anonymizeData: true,
        dataRetention: 90,
        consentRequired: true
      },
      debug: false,
      ...config
    };

    this.initializeComponents();
    this.initializeAlgorithms();

    if (this.config.debug) {
      console.log('AI推荐引擎初始化完成');
    }
  }

  /**
   * 初始化组件
   */
  private initializeComponents(): void {
    this.behaviorAnalyzer = new UserBehaviorAnalyzer({
      debug: this.config.debug
    });

    this.featureExtractor = new ContentFeatureExtractor({
      debug: this.config.debug
    });

    this.realtimeCache = new RealtimeRecommendationCache(this.config.caching!);
  }

  /**
   * 初始化推荐算法
   */
  private initializeAlgorithms(): void {
    // 注册不同类型的推荐算法
    this.algorithms.set(RecommendationType.ASSET, new AssetRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.SCENE_TEMPLATE, new SceneTemplateRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.COLLABORATOR, new CollaboratorRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.LEARNING_PATH, new LearningPathRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.MATERIAL, new MaterialRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.COMPONENT, new ComponentRecommendationAlgorithm());
  }

  /**
   * 获取个性化推荐
   */
  public async getPersonalizedRecommendations(
    userId: string,
    type: RecommendationType,
    context: RecommendationContext,
    count: number = 10
  ): Promise<Recommendation[]> {
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(userId, type, context);
      const cachedResult = await this.realtimeCache.get(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      // 分析用户画像
      const userProfile = await this.behaviorAnalyzer.analyzeUser(userId);

      // 获取推荐引擎
      const algorithm = this.algorithms.get(type);
      if (!algorithm) {
        throw new Error(`不支持的推荐类型: ${type}`);
      }

      // 生成推荐
      const recommendations = await algorithm.recommend({
        type,
        context,
        count,
        userProfile
      });

      // 后处理推荐结果
      const processedRecommendations = await this.postProcessRecommendations(
        recommendations,
        userProfile,
        context
      );

      // 缓存结果
      await this.realtimeCache.set(cacheKey, processedRecommendations);

      // 记录推荐历史
      await this.recordRecommendationHistory(userId, type, processedRecommendations);

      // 触发推荐事件
      this.eventEmitter.emit('recommendation.generated', {
        userId,
        type,
        count: processedRecommendations.length,
        context
      });

      return processedRecommendations;

    } catch (error) {
      console.error('获取个性化推荐失败:', error);
      this.eventEmitter.emit('recommendation.error', { userId, type, error });
      return [];
    }
  }

  /**
   * 后处理推荐结果
   */
  private async postProcessRecommendations(
    recommendations: Recommendation[],
    userProfile: UserProfile,
    context: RecommendationContext
  ): Promise<Recommendation[]> {
    // 过滤重复推荐
    const uniqueRecommendations = this.removeDuplicates(recommendations);

    // 根据用户偏好调整评分
    const adjustedRecommendations = this.adjustScoresByPreferences(
      uniqueRecommendations,
      userProfile.preferences
    );

    // 多样性优化
    const diversifiedRecommendations = this.diversifyRecommendations(adjustedRecommendations);

    // 按相关性排序
    return diversifiedRecommendations.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 移除重复推荐
   */
  private removeDuplicates(recommendations: Recommendation[]): Recommendation[] {
    const seen = new Set<string>();
    return recommendations.filter(rec => {
      if (seen.has(rec.id)) {
        return false;
      }
      seen.add(rec.id);
      return true;
    });
  }

  /**
   * 根据用户偏好调整评分
   */
  private adjustScoresByPreferences(
    recommendations: Recommendation[],
    preferences: UserPreferences
  ): Recommendation[] {
    return recommendations.map(rec => {
      let adjustmentFactor = 1.0;

      // 根据偏好风格调整
      if (preferences.preferredStyles.some(style =>
        rec.metadata.tags.includes(style))) {
        adjustmentFactor *= 1.2;
      }

      // 根据技能水平调整
      if (rec.metadata.difficulty === preferences.skillLevel) {
        adjustmentFactor *= 1.1;
      }

      return {
        ...rec,
        relevanceScore: Math.min(1.0, rec.relevanceScore * adjustmentFactor)
      };
    });
  }

  /**
   * 多样性优化
   */
  private diversifyRecommendations(recommendations: Recommendation[]): Recommendation[] {
    const diversified: Recommendation[] = [];
    const categoryCount: Map<string, number> = new Map();
    const maxPerCategory = 3;

    for (const rec of recommendations) {
      const category = rec.metadata.category;
      const currentCount = categoryCount.get(category) || 0;

      if (currentCount < maxPerCategory) {
        diversified.push(rec);
        categoryCount.set(category, currentCount + 1);
      }
    }

    return diversified;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    userId: string,
    type: RecommendationType,
    context: RecommendationContext
  ): string {
    const contextHash = this.hashContext(context);
    return `rec:${userId}:${type}:${contextHash}`;
  }

  /**
   * 计算上下文哈希
   */
  private hashContext(context: RecommendationContext): string {
    const key = `${context.projectId || ''}:${context.sceneId || ''}:${context.currentActivity}`;
    // 使用简单的哈希算法替代Buffer，确保浏览器兼容性
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36).substring(0, 8);
  }

  /**
   * 记录推荐历史
   */
  private async recordRecommendationHistory(
    userId: string,
    type: RecommendationType,
    recommendations: Recommendation[]
  ): Promise<void> {
    // 这里应该将推荐历史保存到数据库
    // 暂时只记录到内存中
    console.log(`记录推荐历史: 用户${userId}, 类型${type}, 数量${recommendations.length}`);
  }

  /**
   * 更新用户反馈
   */
  public async updateUserFeedback(
    userId: string,
    recommendationId: string,
    feedback: UserFeedback
  ): Promise<void> {
    try {
      // 记录反馈
      await this.recordFeedback(userId, recommendationId, feedback);

      // 更新用户画像
      await this.behaviorAnalyzer.updateUserProfileFromFeedback(userId, feedback);

      // 触发反馈事件
      this.eventEmitter.emit('feedback.received', {
        userId,
        recommendationId,
        rating: feedback.rating
      });

      // 如果反馈较差，触发模型重训练
      if (feedback.rating <= 2) {
        this.eventEmitter.emit('model.retrain.requested', {
          userId,
          recommendationId,
          reason: 'poor_feedback'
        });
      }

    } catch (error) {
      console.error('更新用户反馈失败:', error);
      this.eventEmitter.emit('feedback.error', { userId, recommendationId, error });
    }
  }

  /**
   * 记录反馈
   */
  private async recordFeedback(
    userId: string,
    recommendationId: string,
    feedback: UserFeedback
  ): Promise<void> {
    // 这里应该将反馈保存到数据库
    console.log(`记录用户反馈: ${userId}, 推荐${recommendationId}, 评分${feedback.rating}`);
  }

  /**
   * 实时推荐更新
   */
  public async updateRealtimeRecommendations(
    userId: string,
    userAction: UserInteraction
  ): Promise<void> {
    try {
      // 更新用户行为模式
      await this.behaviorAnalyzer.updateBehaviorPattern(userId, userAction);

      // 清除相关缓存
      await this.realtimeCache.clearUserCache(userId);

      // 触发实时更新事件
      this.eventEmitter.emit('realtime.update', {
        userId,
        action: userAction.action,
        timestamp: userAction.timestamp
      });

    } catch (error) {
      console.error('实时推荐更新失败:', error);
    }
  }

  /**
   * 获取推荐统计信息
   */
  public async getRecommendationStats(userId: string): Promise<RecommendationStats> {
    return {
      totalRecommendations: await this.getTotalRecommendations(userId),
      acceptanceRate: await this.getAcceptanceRate(userId),
      averageRating: await this.getAverageRating(userId),
      topCategories: await this.getTopCategories(userId),
      recentActivity: await this.getRecentActivity(userId)
    };
  }

  /**
   * 事件监听
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听
   */
  public off(event: string, listener?: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 系统初始化
   */
  protected onInitialize(): void {
    if (this.config.debug) {
      console.log('AI推荐引擎系统初始化');
    }
  }

  /**
   * 系统更新
   */
  protected onUpdate(_deltaTime: number): number {
    // 定期清理过期缓存
    if (this.realtimeCache) {
      // 缓存会自动清理，这里可以添加其他定期任务
    }
    return 0; // 返回处理的任务数量
  }

  /**
   * 系统销毁
   */
  protected onDispose(): void {
    // 清理定时器
    if (this.behaviorAnalyzer) {
      this.behaviorAnalyzer.destroy();
    }

    // 清理缓存
    if (this.realtimeCache) {
      this.realtimeCache.clear();
    }

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('AI推荐引擎系统已销毁');
    }
  }

  /**
   * 注册推荐算法
   */
  public registerAlgorithm(type: RecommendationType, algorithm: RecommendationAlgorithm): void {
    this.algorithms.set(type, algorithm);

    this.eventEmitter.emit('algorithm.registered', {
      type,
      algorithmName: algorithm.name,
      version: algorithm.version
    });

    if (this.config.debug) {
      console.log(`注册推荐算法: ${type} -> ${algorithm.name}`);
    }
  }

  /**
   * 移除推荐算法
   */
  public unregisterAlgorithm(type: RecommendationType): void {
    const algorithm = this.algorithms.get(type);
    if (algorithm) {
      this.algorithms.delete(type);

      this.eventEmitter.emit('algorithm.unregistered', {
        type,
        algorithmName: algorithm.name
      });

      if (this.config.debug) {
        console.log(`移除推荐算法: ${type}`);
      }
    }
  }

  /**
   * 获取已注册的算法列表
   */
  public getRegisteredAlgorithms(): Map<RecommendationType, RecommendationAlgorithm> {
    return new Map(this.algorithms);
  }

  /**
   * 批量获取推荐
   */
  public async getBatchRecommendations(
    requests: RecommendationRequest[]
  ): Promise<Map<string, Recommendation[]>> {
    const results = new Map<string, Recommendation[]>();
    const batchPromises = requests.map(async (request) => {
      try {
        const recommendations = await this.getPersonalizedRecommendations(
          request.context.userId,
          request.type,
          request.context,
          request.count
        );
        const key = `${request.context.userId}_${request.type}`;
        results.set(key, recommendations);
      } catch (error) {
        console.error(`批量推荐失败 - 用户: ${request.context.userId}, 类型: ${request.type}`, error);
        results.set(`${request.context.userId}_${request.type}`, []);
      }
    });

    await Promise.all(batchPromises);

    this.eventEmitter.emit('batch.recommendations.completed', {
      requestCount: requests.length,
      successCount: results.size
    });

    return results;
  }

  /**
   * 获取推荐解释
   */
  public async getRecommendationExplanation(
    recommendationId: string,
    userId: string
  ): Promise<RecommendationExplanation> {
    // 这里应该根据推荐ID查找推荐的生成原因
    return {
      recommendationId,
      userId,
      reasons: [
        {
          type: 'user_behavior',
          description: '基于您的使用习惯',
          confidence: 0.8,
          evidence: ['经常使用类似功能', '在相似场景中活跃']
        },
        {
          type: 'content_similarity',
          description: '与您喜欢的内容相似',
          confidence: 0.7,
          evidence: ['风格匹配', '标签相似']
        }
      ],
      algorithmUsed: 'hybrid',
      generatedAt: new Date()
    };
  }

  /**
   * A/B测试支持
   */
  public async getABTestRecommendations(
    userId: string,
    type: RecommendationType,
    context: RecommendationContext,
    testGroup: string,
    count: number = 10
  ): Promise<Recommendation[]> {
    // 根据测试组调整推荐策略
    const modifiedContext = {
      ...context,
      sessionData: {
        ...context.sessionData,
        currentFocus: `${context.sessionData.currentFocus}_${testGroup}`
      }
    };

    const recommendations = await this.getPersonalizedRecommendations(
      userId,
      type,
      modifiedContext,
      count
    );

    // 记录A/B测试数据
    this.eventEmitter.emit('ab.test.served', {
      userId,
      type,
      testGroup,
      recommendationCount: recommendations.length
    });

    return recommendations;
  }

  /**
   * 评估推荐算法性能
   */
  public async evaluateAlgorithm(
    type: RecommendationType,
    testData: TestData
  ): Promise<EvaluationResult> {
    const algorithm = this.algorithms.get(type);
    if (!algorithm || !algorithm.evaluate) {
      throw new Error(`算法 ${type} 不支持评估功能`);
    }

    const result = await algorithm.evaluate(testData);

    this.eventEmitter.emit('algorithm.evaluated', {
      type,
      algorithmName: algorithm.name,
      result
    });

    return result;
  }

  /**
   * 训练推荐算法
   */
  public async trainAlgorithm(
    type: RecommendationType,
    trainingData: TrainingData
  ): Promise<void> {
    const algorithm = this.algorithms.get(type);
    if (!algorithm || !algorithm.train) {
      throw new Error(`算法 ${type} 不支持训练功能`);
    }

    await algorithm.train(trainingData);

    this.eventEmitter.emit('algorithm.trained', {
      type,
      algorithmName: algorithm.name,
      dataSize: trainingData.interactions.length
    });
  }

  /**
   * 获取推荐多样性控制
   */
  public async getDiversifiedRecommendations(
    userId: string,
    type: RecommendationType,
    context: RecommendationContext,
    diversityConfig: DiversityConfig,
    count: number = 10
  ): Promise<Recommendation[]> {
    // 获取基础推荐
    const baseRecommendations = await this.getPersonalizedRecommendations(
      userId,
      type,
      context,
      count * 2 // 获取更多推荐用于多样性筛选
    );

    // 应用多样性控制
    return this.applyDiversityControl(baseRecommendations, diversityConfig, count);
  }

  /**
   * 应用多样性控制
   */
  private applyDiversityControl(
    recommendations: Recommendation[],
    config: DiversityConfig,
    targetCount: number
  ): Recommendation[] {
    const diversified: Recommendation[] = [];
    const categoryCount: Map<string, number> = new Map();
    const tagCount: Map<string, number> = new Map();

    // 按相关性排序
    const sorted = recommendations.sort((a, b) => b.relevanceScore - a.relevanceScore);

    for (const rec of sorted) {
      if (diversified.length >= targetCount) break;

      const category = rec.metadata.category;
      const currentCategoryCount = categoryCount.get(category) || 0;

      // 检查分类多样性
      if (currentCategoryCount >= config.maxPerCategory) {
        continue;
      }

      // 检查标签多样性
      const hasOverlappingTags = rec.metadata.tags.some(tag => {
        const tagCountValue = tagCount.get(tag) || 0;
        return tagCountValue >= config.maxPerTag;
      });

      if (hasOverlappingTags && config.enforceTagDiversity) {
        continue;
      }

      // 添加到结果
      diversified.push(rec);
      categoryCount.set(category, currentCategoryCount + 1);

      // 更新标签计数
      rec.metadata.tags.forEach(tag => {
        tagCount.set(tag, (tagCount.get(tag) || 0) + 1);
      });
    }

    return diversified;
  }

  /**
   * 获取推荐性能指标
   */
  public async getPerformanceMetrics(): Promise<RecommendationPerformanceMetrics> {
    return {
      totalRecommendationsGenerated: await this.getTotalRecommendationsGenerated(),
      averageResponseTime: await this.getAverageResponseTime(),
      cacheHitRate: this.realtimeCache.getStats().hitRate,
      algorithmPerformance: await this.getAlgorithmPerformance(),
      userSatisfactionScore: await this.getUserSatisfactionScore(),
      diversityScore: await this.getDiversityScore(),
      noveltyScore: await this.getNoveltyScore(),
      lastUpdated: new Date()
    };
  }

  /**
   * 清理过期数据
   */
  public async cleanupExpiredData(): Promise<void> {
    try {
      // 清理过期缓存
      await this.realtimeCache.cleanup();

      // 清理过期用户画像
      if (this.behaviorAnalyzer) {
        // 这里应该调用行为分析器的清理方法
      }

      this.eventEmitter.emit('data.cleanup.completed', {
        timestamp: Date.now()
      });

      if (this.config.debug) {
        console.log('过期数据清理完成');
      }
    } catch (error) {
      console.error('数据清理失败:', error);
      this.eventEmitter.emit('data.cleanup.error', { error });
    }
  }

  // 辅助方法的实现
  private async getTotalRecommendations(userId: string): Promise<number> {
    try {
      // 这里应该从数据库查询用户的总推荐数
      // 暂时返回模拟数据
      const userProfile = await this.behaviorAnalyzer.analyzeUser(userId);
      return userProfile ? 100 + Math.floor(Math.random() * 500) : 0;
    } catch (error) {
      console.error(`获取用户 ${userId} 总推荐数失败:`, error);
      return 0;
    }
  }

  private async getAcceptanceRate(userId: string): Promise<number> {
    try {
      // 这里应该计算用户的推荐接受率
      // 接受率 = 被接受的推荐数 / 总推荐数
      const totalRecommendations = await this.getTotalRecommendations(userId);
      if (totalRecommendations === 0) return 0;

      // 模拟接受率计算
      const acceptedCount = Math.floor(totalRecommendations * (0.3 + Math.random() * 0.4));
      return acceptedCount / totalRecommendations;
    } catch (error) {
      console.error(`获取用户 ${userId} 接受率失败:`, error);
      return 0;
    }
  }

  private async getAverageRating(userId: string): Promise<number> {
    try {
      // 这里应该计算用户给出的平均评分
      // 暂时返回模拟数据
      return 3.5 + Math.random() * 1.5; // 3.5-5.0之间的评分
    } catch (error) {
      console.error(`获取用户 ${userId} 平均评分失败:`, error);
      return 0;
    }
  }

  private async getTopCategories(userId: string): Promise<string[]> {
    try {
      // 这里应该分析用户最常使用的推荐分类
      const userProfile = await this.behaviorAnalyzer.analyzeUser(userId);
      if (!userProfile) return [];

      // 基于用户兴趣返回热门分类
      const categories = ['models', 'materials', 'templates', 'components', 'tutorials'];
      return categories
        .sort(() => Math.random() - 0.5)
        .slice(0, 3);
    } catch (error) {
      console.error(`获取用户 ${userId} 热门分类失败:`, error);
      return [];
    }
  }

  private async getRecentActivity(userId: string): Promise<UserInteraction[]> {
    try {
      // 这里应该获取用户最近的交互活动
      // 暂时返回模拟数据
      const activities: UserInteraction[] = [];
      const actions = ['view', 'download', 'like', 'share', 'comment'];

      for (let i = 0; i < 5; i++) {
        activities.push({
          userId,
          itemId: `item_${i}`,
          action: actions[Math.floor(Math.random() * actions.length)],
          timestamp: new Date(Date.now() - i * 3600000), // 每小时一个活动
          context: { source: 'recent_activity' },
          duration: Math.floor(Math.random() * 300) // 0-300秒
        });
      }

      return activities;
    } catch (error) {
      console.error(`获取用户 ${userId} 最近活动失败:`, error);
      return [];
    }
  }

  // 性能指标辅助方法
  private async getTotalRecommendationsGenerated(): Promise<number> {
    // 这里应该从统计数据中获取总推荐生成数
    return 10000 + Math.floor(Math.random() * 50000);
  }

  private async getAverageResponseTime(): Promise<number> {
    // 这里应该计算平均响应时间（毫秒）
    return 150 + Math.random() * 100;
  }

  private async getAlgorithmPerformance(): Promise<Map<RecommendationType, EvaluationResult>> {
    const performance = new Map<RecommendationType, EvaluationResult>();

    for (const [type, _algorithm] of this.algorithms) {
      // 模拟算法性能数据
      performance.set(type, {
        accuracy: 0.8 + Math.random() * 0.15,
        precision: 0.75 + Math.random() * 0.2,
        recall: 0.7 + Math.random() * 0.25,
        f1Score: 0.72 + Math.random() * 0.23,
        coverage: 0.65 + Math.random() * 0.3,
        diversity: 0.6 + Math.random() * 0.35,
        novelty: 0.55 + Math.random() * 0.4
      });
    }

    return performance;
  }

  private async getUserSatisfactionScore(): Promise<number> {
    // 这里应该计算用户满意度评分
    return 4.2 + Math.random() * 0.8; // 4.2-5.0之间
  }

  private async getDiversityScore(): Promise<number> {
    // 这里应该计算推荐多样性评分
    return 0.7 + Math.random() * 0.25;
  }

  private async getNoveltyScore(): Promise<number> {
    // 这里应该计算推荐新颖性评分
    return 0.6 + Math.random() * 0.3;
  }
}

// 推荐统计信息
export interface RecommendationStats {
  totalRecommendations: number;
  acceptanceRate: number;
  averageRating: number;
  topCategories: string[];
  recentActivity: UserInteraction[];
}

// 推荐解释
export interface RecommendationExplanation {
  recommendationId: string;
  userId: string;
  reasons: ExplanationReason[];
  algorithmUsed: string;
  generatedAt: Date;
}

// 解释原因
export interface ExplanationReason {
  type: 'user_behavior' | 'content_similarity' | 'collaborative_filtering' | 'popularity' | 'diversity';
  description: string;
  confidence: number;
  evidence: string[];
}

// 多样性配置
export interface DiversityConfig {
  maxPerCategory: number;
  maxPerTag: number;
  enforceTagDiversity: boolean;
  categoryWeights?: Map<string, number>;
  diversityThreshold?: number;
}

// 推荐性能指标
export interface RecommendationPerformanceMetrics {
  totalRecommendationsGenerated: number;
  averageResponseTime: number;
  cacheHitRate: number;
  algorithmPerformance: Map<RecommendationType, EvaluationResult>;
  userSatisfactionScore: number;
  diversityScore: number;
  noveltyScore: number;
  lastUpdated: Date;
}

// ================================
// 推荐算法实现类
// ================================

/**
 * 资产推荐算法
 */
export class AssetRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'AssetRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    // 基于内容的资产推荐逻辑
    const recommendations: Recommendation[] = [];

    // 模拟推荐结果
    for (let i = 0; i < Math.min(request.count, 5); i++) {
      recommendations.push({
        id: `asset_${i}`,
        type: RecommendationType.ASSET,
        title: `推荐资产 ${i + 1}`,
        description: `基于您的使用习惯推荐的资产`,
        confidence: 0.8 + Math.random() * 0.2,
        relevanceScore: 0.7 + Math.random() * 0.3,
        metadata: {
          category: 'models',
          tags: ['3d', 'character', 'animation'],
          difficulty: SkillLevel.INTERMEDIATE,
          estimatedTime: 30,
          popularity: 0.8,
          lastUpdated: new Date(),
          source: 'content_based'
        },
        actions: [
          {
            type: 'download',
            label: '下载资产',
            data: { assetId: `asset_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练资产推荐模型
    console.log('训练资产推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估资产推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.85,
      precision: 0.82,
      recall: 0.78,
      f1Score: 0.80,
      coverage: 0.75,
      diversity: 0.70,
      novelty: 0.65
    };
  }
}

/**
 * 场景模板推荐算法
 */
export class SceneTemplateRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'SceneTemplateRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // 基于协同过滤的场景模板推荐
    for (let i = 0; i < Math.min(request.count, 5); i++) {
      recommendations.push({
        id: `scene_template_${i}`,
        type: RecommendationType.SCENE_TEMPLATE,
        title: `场景模板 ${i + 1}`,
        description: `适合您项目类型的场景模板`,
        confidence: 0.75 + Math.random() * 0.25,
        relevanceScore: 0.8 + Math.random() * 0.2,
        metadata: {
          category: 'templates',
          tags: ['scene', 'environment', 'lighting'],
          difficulty: SkillLevel.BEGINNER,
          estimatedTime: 45,
          popularity: 0.9,
          lastUpdated: new Date(),
          source: 'collaborative_filtering'
        },
        actions: [
          {
            type: 'apply',
            label: '应用模板',
            data: { templateId: `scene_template_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练场景模板推荐模型
    console.log('训练场景模板推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估场景模板推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.82,
      precision: 0.79,
      recall: 0.76,
      f1Score: 0.77,
      coverage: 0.73,
      diversity: 0.68,
      novelty: 0.62
    };
  }
}

/**
 * 协作者推荐算法
 */
export class CollaboratorRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'CollaboratorRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // 基于社交网络的协作者推荐
    for (let i = 0; i < Math.min(request.count, 3); i++) {
      recommendations.push({
        id: `collaborator_${i}`,
        type: RecommendationType.COLLABORATOR,
        title: `协作者 ${i + 1}`,
        description: `技能互补的潜在协作伙伴`,
        confidence: 0.7 + Math.random() * 0.3,
        relevanceScore: 0.75 + Math.random() * 0.25,
        metadata: {
          category: 'users',
          tags: ['collaboration', 'skills', 'experience'],
          difficulty: SkillLevel.ADVANCED,
          estimatedTime: 0,
          popularity: 0.85,
          lastUpdated: new Date(),
          source: 'social_network'
        },
        actions: [
          {
            type: 'invite',
            label: '邀请协作',
            data: { userId: `collaborator_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练协作者推荐模型
    console.log('训练协作者推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估协作者推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.78,
      precision: 0.75,
      recall: 0.72,
      f1Score: 0.73,
      coverage: 0.70,
      diversity: 0.65,
      novelty: 0.60
    };
  }
}

/**
 * 学习路径推荐算法
 */
export class LearningPathRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'LearningPathRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // 基于学习进度的路径推荐
    for (let i = 0; i < Math.min(request.count, 4); i++) {
      recommendations.push({
        id: `learning_path_${i}`,
        type: RecommendationType.LEARNING_PATH,
        title: `学习路径 ${i + 1}`,
        description: `根据您的技能水平定制的学习路径`,
        confidence: 0.85 + Math.random() * 0.15,
        relevanceScore: 0.8 + Math.random() * 0.2,
        metadata: {
          category: 'education',
          tags: ['learning', 'tutorial', 'progression'],
          difficulty: request.userProfile?.preferences.skillLevel || SkillLevel.BEGINNER,
          estimatedTime: 120,
          popularity: 0.75,
          lastUpdated: new Date(),
          source: 'adaptive_learning'
        },
        actions: [
          {
            type: 'start_learning',
            label: '开始学习',
            data: { pathId: `learning_path_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练学习路径推荐模型
    console.log('训练学习路径推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估学习路径推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.88,
      precision: 0.85,
      recall: 0.82,
      f1Score: 0.83,
      coverage: 0.78,
      diversity: 0.72,
      novelty: 0.68
    };
  }
}

/**
 * 材质推荐算法
 */
export class MaterialRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'MaterialRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // 基于视觉相似性的材质推荐
    for (let i = 0; i < Math.min(request.count, 6); i++) {
      recommendations.push({
        id: `material_${i}`,
        type: RecommendationType.MATERIAL,
        title: `材质 ${i + 1}`,
        description: `与您当前场景风格匹配的材质`,
        confidence: 0.8 + Math.random() * 0.2,
        relevanceScore: 0.75 + Math.random() * 0.25,
        metadata: {
          category: 'materials',
          tags: ['texture', 'shader', 'pbr'],
          difficulty: SkillLevel.INTERMEDIATE,
          estimatedTime: 15,
          popularity: 0.8,
          lastUpdated: new Date(),
          source: 'visual_similarity'
        },
        actions: [
          {
            type: 'apply_material',
            label: '应用材质',
            data: { materialId: `material_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练材质推荐模型
    console.log('训练材质推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估材质推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.83,
      precision: 0.80,
      recall: 0.77,
      f1Score: 0.78,
      coverage: 0.74,
      diversity: 0.69,
      novelty: 0.63
    };
  }
}

/**
 * 组件推荐算法
 */
export class ComponentRecommendationAlgorithm implements RecommendationAlgorithm {
  name = 'ComponentRecommendationAlgorithm';
  version = '1.0.0';

  async recommend(request: RecommendationRequest): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // 基于功能需求的组件推荐
    for (let i = 0; i < Math.min(request.count, 5); i++) {
      recommendations.push({
        id: `component_${i}`,
        type: RecommendationType.COMPONENT,
        title: `组件 ${i + 1}`,
        description: `增强您项目功能的推荐组件`,
        confidence: 0.75 + Math.random() * 0.25,
        relevanceScore: 0.7 + Math.random() * 0.3,
        metadata: {
          category: 'components',
          tags: ['functionality', 'behavior', 'interaction'],
          difficulty: SkillLevel.ADVANCED,
          estimatedTime: 60,
          popularity: 0.7,
          lastUpdated: new Date(),
          source: 'functional_matching'
        },
        actions: [
          {
            type: 'add_component',
            label: '添加组件',
            data: { componentId: `component_${i}` },
            primary: true
          }
        ]
      });
    }

    return recommendations;
  }

  async train(data: TrainingData): Promise<void> {
    // 训练组件推荐模型
    console.log('训练组件推荐算法，数据量:', data.interactions.length);
  }

  async evaluate(testData: TestData): Promise<EvaluationResult> {
    // 评估算法性能
    console.log('评估组件推荐算法，测试用例数:', testData.testCases.length);
    return {
      accuracy: 0.80,
      precision: 0.77,
      recall: 0.74,
      f1Score: 0.75,
      coverage: 0.71,
      diversity: 0.66,
      novelty: 0.61
    };
  }
}

