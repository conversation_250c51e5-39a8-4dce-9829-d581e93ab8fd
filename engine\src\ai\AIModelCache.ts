/**
 * AI模型缓存
 * 用于缓存AI模型的结果，提高性能
 */
import { AIModelType } from './AIModelType';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 缓存驱逐策略
 */
export enum CacheEvictionPolicy {
  /** 最近最少使用 */
  LRU = 'lru',
  /** 最不经常使用 */
  LFU = 'lfu',
  /** 先进先出 */
  FIFO = 'fifo',
  /** 随机驱逐 */
  RANDOM = 'random'
}

/**
 * 缓存存储类型
 */
export enum CacheStorageType {
  /** 内存存储 */
  MEMORY = 'memory',
  /** 本地存储 */
  LOCAL_STORAGE = 'localStorage',
  /** IndexedDB存储 */
  INDEXED_DB = 'indexedDB',
  /** 文件系统存储 */
  FILE_SYSTEM = 'fileSystem'
}

/**
 * 缓存项
 */
interface CacheItem<T> {
  /** 值 */
  value: T;
  /** 过期时间 */
  expireTime: number;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 访问次数 */
  accessCount: number;
  /** 创建时间 */
  createdTime: number;
  /** 数据大小（字节） */
  size: number;
  /** 是否压缩 */
  compressed: boolean;
  /** 优先级 */
  priority: number;
}

/**
 * 缓存配置
 */
export interface AIModelCacheConfig {
  /** 最大缓存大小 */
  maxSize?: number;
  /** 缓存过期时间（毫秒） */
  expireTime?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 清理间隔（毫秒） */
  cleanupInterval?: number;
  /** 缓存命中率阈值 */
  hitRateThreshold?: number;
  /** 缓存命中率窗口大小 */
  hitRateWindowSize?: number;
  /** 驱逐策略 */
  evictionPolicy?: CacheEvictionPolicy;
  /** 存储类型 */
  storageType?: CacheStorageType;
  /** 是否启用压缩 */
  enableCompression?: boolean;
  /** 压缩阈值（字节） */
  compressionThreshold?: number;
  /** 是否启用持久化 */
  enablePersistence?: boolean;
  /** 持久化键前缀 */
  persistenceKeyPrefix?: string;
  /** 是否启用预热 */
  enablePrewarm?: boolean;
  /** 预热数据 */
  prewarmData?: Array<{ key: string; value: any; expireTime?: number }>;
  /** 最大内存使用量（字节） */
  maxMemoryUsage?: number;
  /** 是否启用分层缓存 */
  enableTieredCache?: boolean;
  /** L1缓存大小 */
  l1CacheSize?: number;
  /** L2缓存大小 */
  l2CacheSize?: number;
}

/**
 * 缓存统计
 */
interface CacheStats {
  /** 缓存大小 */
  size: number;
  /** 最大缓存大小 */
  maxSize: number;
  /** 缓存命中次数 */
  hits: number;
  /** 缓存未命中次数 */
  misses: number;
  /** 缓存命中率 */
  hitRate: number;
  /** 缓存过期次数 */
  expirations: number;
  /** 缓存驱逐次数 */
  evictions: number;
}

/**
 * AI模型缓存
 */
export class AIModelCache<T> extends EventEmitter {
  /** 缓存 */
  private cache: Map<string, CacheItem<T>> = new Map();

  /** L1缓存（内存） */
  private l1Cache: Map<string, CacheItem<T>> = new Map();

  /** L2缓存（持久化） */
  private l2Cache: Map<string, CacheItem<T>> = new Map();
  
  /** 配置 */
  private config: Required<AIModelCacheConfig>;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<AIModelCacheConfig> = {
    maxSize: 100,
    expireTime: 3600000, // 1小时
    debug: false,
    cleanupInterval: 300000, // 5分钟
    hitRateThreshold: 0.5,
    hitRateWindowSize: 100,
    evictionPolicy: CacheEvictionPolicy.LRU,
    storageType: CacheStorageType.MEMORY,
    enableCompression: false,
    compressionThreshold: 1024, // 1KB
    enablePersistence: false,
    persistenceKeyPrefix: 'ai_cache_',
    enablePrewarm: false,
    prewarmData: [],
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    enableTieredCache: false,
    l1CacheSize: 50,
    l2CacheSize: 200
  };
  
  /** 缓存命中次数 */
  private hits: number = 0;
  
  /** 缓存未命中次数 */
  private misses: number = 0;
  
  /** 缓存过期次数 */
  private expirations: number = 0;
  
  /** 缓存驱逐次数 */
  private evictions: number = 0;
  
  /** 清理定时器 */
  private cleanupTimer: NodeJS.Timeout | null = null;
  
  /** 最近的访问历史 */
  private recentAccesses: boolean[] = [];

  /** 当前内存使用量 */
  private currentMemoryUsage: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIModelCacheConfig = {}) {
    super();

    this.config = {
      ...AIModelCache.DEFAULT_CONFIG,
      ...config
    };

    // 初始化缓存
    this.initializeCache();

    // 启动清理定时器
    this.startCleanupTimer();
  }
  
  /**
   * 生成缓存键
   * @param modelType 模型类型
   * @param input 输入
   * @param options 选项
   * @returns 缓存键
   */
  public generateKey(modelType: AIModelType, input: string, options?: any): string {
    // 基本键
    let key = `${modelType}:${input}`;
    
    // 添加选项
    if (options) {
      key += `:${JSON.stringify(options)}`;
    }
    
    return key;
  }
  
  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存值
   */
  public get(key: string): T | null {
    // 获取缓存项
    const item = this.cache.get(key);
    
    // 如果缓存项不存在，返回null
    if (!item) {
      this.misses++;
      this.updateRecentAccesses(false);
      return null;
    }
    
    // 检查是否过期
    if (item.expireTime > 0 && Date.now() > item.expireTime) {
      this.cache.delete(key);
      this.expirations++;
      this.misses++;
      this.updateRecentAccesses(false);
      return null;
    }
    
    // 更新访问信息
    item.lastAccessTime = Date.now();
    item.accessCount++;

    // 更新统计
    this.hits++;
    this.updateRecentAccesses(true);

    // 如果数据被压缩，需要解压缩
    const value = item.compressed ? this.decompress(item.value) : item.value;

    // 返回值
    return value;
  }
  
  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param expireTime 过期时间（毫秒）
   * @param priority 优先级
   * @returns 是否成功
   */
  public set(key: string, value: T, expireTime?: number, priority?: number): boolean {
    // 如果缓存已满，清理一些项
    if (this.cache.size >= this.config.maxSize) {
      this.evictItems();
    }
    
    // 计算过期时间
    const expire = expireTime !== undefined
      ? (expireTime > 0 ? Date.now() + expireTime : 0)
      : (this.config.expireTime > 0 ? Date.now() + this.config.expireTime : 0);
    
    // 计算数据大小
    const size = this.calculateSize(value);

    // 检查是否需要压缩
    const shouldCompress = this.config.enableCompression && size > this.config.compressionThreshold;
    const finalValue = shouldCompress ? this.compress(value) : value;

    // 创建缓存项
    const item: CacheItem<T> = {
      value: finalValue,
      expireTime: expire,
      lastAccessTime: Date.now(),
      accessCount: 0,
      createdTime: Date.now(),
      size: shouldCompress ? this.calculateSize(finalValue) : size,
      compressed: shouldCompress,
      priority: priority || 0
    };
    
    // 设置缓存项
    this.cache.set(key, item);

    // 更新内存使用量
    this.currentMemoryUsage += item.size;

    // 检查内存使用量
    this.checkMemoryUsage();

    // 保存到持久化存储
    this.saveToPersistence(key, item);

    // 发出事件
    this.emit('itemSet', { key, value, size: item.size });

    return true;
  }
  
  /**
   * 删除缓存项
   * @param key 缓存键
   * @returns 是否成功
   */
  public delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
    this.expirations = 0;
    this.evictions = 0;
    this.recentAccesses = [];
  }
  
  /**
   * 获取缓存大小
   * @returns 缓存大小
   */
  public size(): number {
    return this.cache.size;
  }
  
  /**
   * 获取缓存统计
   * @returns 缓存统计
   */
  public getStats(): CacheStats {
    const totalAccesses = this.hits + this.misses;
    const hitRate = totalAccesses > 0 ? this.hits / totalAccesses : 0;
    
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hits: this.hits,
      misses: this.misses,
      hitRate,
      expirations: this.expirations,
      evictions: this.evictions
    };
  }
  
  /**
   * 清理过期项
   */
  public cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;
    
    // 遍历缓存项
    for (const [key, item] of this.cache.entries()) {
      // 如果过期时间大于0且已过期，删除
      if (item.expireTime > 0 && now > item.expireTime) {
        this.cache.delete(key);
        expiredCount++;
        this.expirations++;
      }
    }
    
    // 如果启用调试且有过期项，输出日志
    if (this.config.debug && expiredCount > 0) {
      console.log(`[AIModelCache] 清理了 ${expiredCount} 个过期项`);
    }
  }
  
  /**
   * 驱逐项
   */
  private evictItems(): void {
    // 如果缓存为空，直接返回
    if (this.cache.size === 0) return;
    
    // 计算要驱逐的项数
    const evictCount = Math.max(1, Math.floor(this.cache.size * 0.1));
    
    // 按最后访问时间和访问次数排序
    const sortedItems = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => {
        // 首先按访问次数排序
        const countDiff = a.accessCount - b.accessCount;
        if (countDiff !== 0) return countDiff;
        
        // 然后按最后访问时间排序
        return a.lastAccessTime - b.lastAccessTime;
      });
    
    // 驱逐最不常用的项
    for (let i = 0; i < evictCount && i < sortedItems.length; i++) {
      this.cache.delete(sortedItems[i][0]);
      this.evictions++;
    }
    
    // 如果启用调试，输出日志
    if (this.config.debug) {
      console.log(`[AIModelCache] 驱逐了 ${evictCount} 个项`);
    }
  }
  
  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 如果已有定时器，先清除
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // 创建新定时器
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
      
      // 检查缓存命中率
      this.checkHitRate();
    }, this.config.cleanupInterval);
  }
  
  /**
   * 停止清理定时器
   */
  public stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
  
  /**
   * 更新最近访问历史
   * @param hit 是否命中
   */
  private updateRecentAccesses(hit: boolean): void {
    // 添加到历史
    this.recentAccesses.push(hit);
    
    // 如果超过窗口大小，移除最早的
    if (this.recentAccesses.length > this.config.hitRateWindowSize) {
      this.recentAccesses.shift();
    }
  }
  
  /**
   * 检查缓存命中率
   */
  private checkHitRate(): void {
    // 如果历史不足，直接返回
    if (this.recentAccesses.length < this.config.hitRateWindowSize) {
      return;
    }
    
    // 计算最近的命中率
    const recentHits = this.recentAccesses.filter(hit => hit).length;
    const recentHitRate = recentHits / this.recentAccesses.length;
    
    // 如果命中率低于阈值，增加缓存大小
    if (recentHitRate < this.config.hitRateThreshold) {
      const newMaxSize = Math.min(this.config.maxSize * 1.5, 1000);
      
      if (this.config.debug) {
        console.log(`[AIModelCache] 命中率低 (${recentHitRate.toFixed(2)}), 增加缓存大小: ${this.config.maxSize} -> ${newMaxSize}`);
      }
      
      this.config.maxSize = newMaxSize;
    }
  }
  
  /**
   * 计算数据大小
   * @param value 数据值
   * @returns 大小（字节）
   */
  private calculateSize(value: any): number {
    try {
      if (value === null || value === undefined) {
        return 0;
      }

      if (typeof value === 'string') {
        return new Blob([value]).size;
      }

      if (typeof value === 'number' || typeof value === 'boolean') {
        return 8; // 近似值
      }

      if (value instanceof ArrayBuffer) {
        return value.byteLength;
      }

      if (value instanceof Blob) {
        return value.size;
      }

      // 对于对象，使用JSON序列化估算大小
      const jsonString = JSON.stringify(value);
      return new Blob([jsonString]).size;
    } catch (error) {
      // 如果无法计算大小，返回默认值
      return 1024; // 1KB
    }
  }

  /**
   * 压缩数据
   * @param value 原始数据
   * @returns 压缩后的数据
   */
  private compress(value: any): any {
    try {
      // 简单的压缩实现：JSON字符串化后使用LZ压缩算法
      const jsonString = JSON.stringify(value);

      // 这里应该使用真正的压缩算法，如LZ4、gzip等
      // 目前使用简单的字符串压缩模拟
      const compressed = this.simpleCompress(jsonString);

      return {
        __compressed: true,
        data: compressed,
        originalType: typeof value
      };
    } catch (error) {
      console.warn('压缩失败，返回原始数据:', error);
      return value;
    }
  }

  /**
   * 解压缩数据
   * @param compressedValue 压缩的数据
   * @returns 解压缩后的数据
   */
  private decompress(compressedValue: any): any {
    try {
      if (!compressedValue || !compressedValue.__compressed) {
        return compressedValue;
      }

      const decompressed = this.simpleDecompress(compressedValue.data);
      return JSON.parse(decompressed);
    } catch (error) {
      console.warn('解压缩失败，返回原始数据:', error);
      return compressedValue;
    }
  }

  /**
   * 简单压缩算法（模拟）
   * @param str 字符串
   * @returns 压缩后的字符串
   */
  private simpleCompress(str: string): string {
    // 简单的RLE压缩算法
    let compressed = '';
    let count = 1;
    let current = str[0];

    for (let i = 1; i < str.length; i++) {
      if (str[i] === current && count < 9) {
        count++;
      } else {
        compressed += count > 1 ? count + current : current;
        current = str[i];
        count = 1;
      }
    }

    compressed += count > 1 ? count + current : current;
    return compressed;
  }

  /**
   * 简单解压缩算法（模拟）
   * @param str 压缩的字符串
   * @returns 解压缩后的字符串
   */
  private simpleDecompress(str: string): string {
    let decompressed = '';

    for (let i = 0; i < str.length; i++) {
      const char = str[i];
      if (/\d/.test(char) && i + 1 < str.length) {
        const count = parseInt(char);
        const nextChar = str[i + 1];
        decompressed += nextChar.repeat(count);
        i++; // 跳过下一个字符
      } else {
        decompressed += char;
      }
    }

    return decompressed;
  }

  /**
   * 初始化缓存
   */
  private initializeCache(): void {
    if (this.initialized) {
      return;
    }

    // 如果启用预热，加载预热数据
    if (this.config.enablePrewarm && this.config.prewarmData.length > 0) {
      this.prewarmCache();
    }

    // 如果启用持久化，加载持久化数据
    if (this.config.enablePersistence) {
      this.loadFromPersistence();
    }

    this.initialized = true;
    this.emit('initialized');

    if (this.config.debug) {
      console.log('[AIModelCache] 缓存初始化完成');
    }
  }

  /**
   * 预热缓存
   */
  private prewarmCache(): void {
    if (!this.config.prewarmData || this.config.prewarmData.length === 0) {
      return;
    }

    let prewarmCount = 0;
    for (const item of this.config.prewarmData) {
      try {
        this.set(item.key, item.value as T, item.expireTime);
        prewarmCount++;
      } catch (error) {
        console.warn(`预热数据失败: ${item.key}`, error);
      }
    }

    if (this.config.debug) {
      console.log(`[AIModelCache] 预热了 ${prewarmCount} 个缓存项`);
    }

    this.emit('prewarmed', prewarmCount);
  }

  /**
   * 从持久化存储加载数据
   */
  private loadFromPersistence(): void {
    try {
      if (this.config.storageType === CacheStorageType.LOCAL_STORAGE && typeof localStorage !== 'undefined') {
        this.loadFromLocalStorage();
      } else if (this.config.storageType === CacheStorageType.INDEXED_DB) {
        this.loadFromIndexedDB();
      } else if (this.config.storageType === CacheStorageType.FILE_SYSTEM) {
        this.loadFromFileSystem();
      }
    } catch (error) {
      console.warn('从持久化存储加载数据失败:', error);
    }
  }

  /**
   * 从LocalStorage加载数据
   */
  private loadFromLocalStorage(): void {
    const keys = Object.keys(localStorage).filter(key =>
      key.startsWith(this.config.persistenceKeyPrefix)
    );

    let loadedCount = 0;
    for (const key of keys) {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const item = JSON.parse(data);
          const cacheKey = key.replace(this.config.persistenceKeyPrefix, '');

          // 检查是否过期
          if (item.expireTime === 0 || Date.now() < item.expireTime) {
            this.cache.set(cacheKey, item);
            loadedCount++;
          } else {
            localStorage.removeItem(key);
          }
        }
      } catch (error) {
        console.warn(`加载持久化数据失败: ${key}`, error);
        localStorage.removeItem(key);
      }
    }

    if (this.config.debug && loadedCount > 0) {
      console.log(`[AIModelCache] 从LocalStorage加载了 ${loadedCount} 个缓存项`);
    }
  }

  /**
   * 从IndexedDB加载数据（模拟）
   */
  private loadFromIndexedDB(): void {
    // 这里应该实现真正的IndexedDB操作
    // 目前只是模拟
    if (this.config.debug) {
      console.log('[AIModelCache] IndexedDB加载功能尚未实现');
    }
  }

  /**
   * 从文件系统加载数据（模拟）
   */
  private loadFromFileSystem(): void {
    // 这里应该实现真正的文件系统操作
    // 目前只是模拟
    if (this.config.debug) {
      console.log('[AIModelCache] 文件系统加载功能尚未实现');
    }
  }

  /**
   * 保存到持久化存储
   */
  private saveToPersistence(key: string, item: CacheItem<T>): void {
    if (!this.config.enablePersistence) {
      return;
    }

    try {
      if (this.config.storageType === CacheStorageType.LOCAL_STORAGE && typeof localStorage !== 'undefined') {
        const persistenceKey = this.config.persistenceKeyPrefix + key;
        localStorage.setItem(persistenceKey, JSON.stringify(item));
      } else if (this.config.storageType === CacheStorageType.INDEXED_DB) {
        this.saveToIndexedDB(key, item);
      } else if (this.config.storageType === CacheStorageType.FILE_SYSTEM) {
        this.saveToFileSystem(key, item);
      }
    } catch (error) {
      console.warn('保存到持久化存储失败:', error);
    }
  }

  /**
   * 保存到IndexedDB（模拟）
   */
  private saveToIndexedDB(key: string, item: CacheItem<T>): void {
    // 这里应该实现真正的IndexedDB操作
    if (this.config.debug) {
      console.log(`[AIModelCache] IndexedDB保存功能尚未实现: ${key}`);
    }
  }

  /**
   * 保存到文件系统（模拟）
   */
  private saveToFileSystem(key: string, item: CacheItem<T>): void {
    // 这里应该实现真正的文件系统操作
    if (this.config.debug) {
      console.log(`[AIModelCache] 文件系统保存功能尚未实现: ${key}`, item);
    }
  }

  /**
   * 检查内存使用量
   */
  private checkMemoryUsage(): void {
    if (this.currentMemoryUsage > this.config.maxMemoryUsage) {
      // 内存使用量超过限制，强制清理
      const targetSize = this.config.maxMemoryUsage * 0.8; // 清理到80%
      this.evictByMemoryUsage(targetSize);

      if (this.config.debug) {
        console.log(`[AIModelCache] 内存使用量超限，已清理到 ${this.currentMemoryUsage} 字节`);
      }

      this.emit('memoryLimitExceeded', {
        currentUsage: this.currentMemoryUsage,
        maxUsage: this.config.maxMemoryUsage
      });
    }
  }

  /**
   * 按内存使用量驱逐项
   */
  private evictByMemoryUsage(targetSize: number): void {
    const sortedItems = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => {
        // 按优先级、访问次数、最后访问时间排序
        if (a.priority !== b.priority) {
          return a.priority - b.priority; // 低优先级先驱逐
        }
        if (a.accessCount !== b.accessCount) {
          return a.accessCount - b.accessCount; // 访问次数少的先驱逐
        }
        return a.lastAccessTime - b.lastAccessTime; // 最久未访问的先驱逐
      });

    let evictedCount = 0;
    for (const [key, item] of sortedItems) {
      if (this.currentMemoryUsage <= targetSize) {
        break;
      }

      this.cache.delete(key);
      this.currentMemoryUsage -= item.size;
      this.evictions++;
      evictedCount++;

      // 从持久化存储中删除
      this.removeFromPersistence(key);
    }

    if (this.config.debug && evictedCount > 0) {
      console.log(`[AIModelCache] 按内存使用量驱逐了 ${evictedCount} 个项`);
    }
  }

  /**
   * 从持久化存储中删除
   */
  private removeFromPersistence(key: string): void {
    if (!this.config.enablePersistence) {
      return;
    }

    try {
      if (this.config.storageType === CacheStorageType.LOCAL_STORAGE && typeof localStorage !== 'undefined') {
        const persistenceKey = this.config.persistenceKeyPrefix + key;
        localStorage.removeItem(persistenceKey);
      }
      // 其他存储类型的删除操作...
    } catch (error) {
      console.warn('从持久化存储删除失败:', error);
    }
  }

  /**
   * 获取增强的缓存统计
   */
  public getEnhancedStats(): any {
    const basicStats = this.getStats();

    return {
      ...basicStats,
      memoryUsage: this.currentMemoryUsage,
      maxMemoryUsage: this.config.maxMemoryUsage,
      memoryUsagePercentage: (this.currentMemoryUsage / this.config.maxMemoryUsage) * 100,
      averageItemSize: this.cache.size > 0 ? this.currentMemoryUsage / this.cache.size : 0,
      compressionEnabled: this.config.enableCompression,
      persistenceEnabled: this.config.enablePersistence,
      tieredCacheEnabled: this.config.enableTieredCache,
      evictionPolicy: this.config.evictionPolicy,
      storageType: this.config.storageType
    };
  }

  /**
   * 批量设置缓存项
   */
  public setBatch(items: Array<{ key: string; value: T; expireTime?: number; priority?: number }>): number {
    let successCount = 0;

    for (const item of items) {
      try {
        if (this.set(item.key, item.value, item.expireTime, item.priority)) {
          successCount++;
        }
      } catch (error) {
        console.warn(`批量设置缓存项失败: ${item.key}`, error);
      }
    }

    this.emit('batchSet', { total: items.length, success: successCount });

    return successCount;
  }

  /**
   * 批量获取缓存项
   */
  public getBatch(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>();

    for (const key of keys) {
      results.set(key, this.get(key));
    }

    this.emit('batchGet', { keys: keys.length, hits: Array.from(results.values()).filter(v => v !== null).length });

    return results;
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.stopCleanupTimer();
    this.clear();
    this.removeAllListeners();
    this.initialized = false;
  }
}
