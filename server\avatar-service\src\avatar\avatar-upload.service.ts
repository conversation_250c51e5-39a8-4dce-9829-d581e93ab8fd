/**
 * 虚拟化身上传服务
 * 
 * 提供虚拟化身文件上传、数字人创建管理的业务逻辑实现
 */

import { Injectable, Logger } from '@nestjs/common';
import { UploadConfigDto, CreateDigitalHumanDto } from './avatar-upload.controller';

/**
 * 上传文件信息接口
 */
interface UploadFileInfo {
  fileName: string;
  fileSize: number;
  fileType: string;
  fileContent: Buffer;
  uploadTime: Date;
}

/**
 * 上传结果接口
 */
interface UploadResult {
  success: boolean;
  uploadId: string;
  avatarData?: any;
  digitalHumanEntity?: any;
  processingTime: number;
  error?: string;
  warnings?: string[];
  validationResult?: {
    isValid: boolean;
    issues: string[];
    score: number;
  };
}

/**
 * 数字人信息接口
 */
interface DigitalHumanInfo {
  id: string;
  name: string;
  avatarData: any;
  sceneId: string;
  position: { x: number; y: number; z: number };
  config: {
    enableAI: boolean;
    enableVoice: boolean;
    enableInteraction: boolean;
    knowledgeBaseId?: string;
    personality?: string;
  };
  createdAt: Date;
  status: 'active' | 'inactive' | 'error';
}

@Injectable()
export class AvatarUploadService {
  private readonly logger = new Logger(AvatarUploadService.name);

  /** 支持的文件格式 */
  private readonly supportedFormats = ['json', 'bin', 'gltf', 'fbx'];

  /** 最大文件大小（MB） */
  private readonly maxFileSize = 50;

  /** 数字人存储 */
  private readonly digitalHumans: Map<string, DigitalHumanInfo> = new Map();

  /** 上传历史记录 */
  private readonly uploadHistory: Map<string, UploadResult[]> = new Map();

  /** 统计信息 */
  private statistics = {
    totalUploads: 0,
    successfulUploads: 0,
    failedUploads: 0,
    activeDigitalHumans: 0,
    averageProcessingTime: 0
  };

  /**
   * 上传虚拟化身文件
   */
  async uploadAvatarFile(fileInfo: UploadFileInfo, config: UploadConfigDto): Promise<UploadResult> {
    const startTime = Date.now();
    const uploadId = this.generateUploadId(fileInfo.fileName);
    
    this.logger.log(`开始上传虚拟化身文件: ${fileInfo.fileName}`);
    this.statistics.totalUploads++;

    try {
      // 1. 验证文件
      const validationResult = await this.validateFile(fileInfo);
      if (!validationResult.isValid) {
        throw new Error(`文件验证失败: ${validationResult.issues.join(', ')}`);
      }

      // 2. 解析虚拟化身数据
      const avatarData = await this.parseAvatarFile(fileInfo);

      // 3. 验证数据完整性
      const dataValidation = await this.validateAvatarData(avatarData, config.validationOptions);

      // 4. 创建数字人
      const digitalHuman = await this.createDigitalHumanFromData(avatarData, config);

      // 5. 存储数字人
      this.digitalHumans.set(digitalHuman.id, digitalHuman);

      const processingTime = Date.now() - startTime;
      this.statistics.successfulUploads++;
      this.statistics.activeDigitalHumans++;
      this.updateAverageProcessingTime(processingTime);

      const result: UploadResult = {
        success: true,
        uploadId,
        avatarData,
        digitalHumanEntity: { id: digitalHuman.id, name: digitalHuman.name },
        processingTime,
        validationResult: dataValidation,
        warnings: dataValidation.issues.length > 0 ? dataValidation.issues : undefined
      };

      // 记录上传历史
      this.recordUploadHistory(config.targetSceneId, result);

      this.logger.log(`虚拟化身上传完成: ${fileInfo.fileName}, 耗时: ${processingTime}ms`);
      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.statistics.failedUploads++;
      this.updateAverageProcessingTime(processingTime);

      this.logger.error(`虚拟化身上传失败: ${fileInfo.fileName}`, error.stack);
      
      return {
        success: false,
        uploadId,
        processingTime,
        error: error.message
      };
    }
  }

  /**
   * 创建数字人
   */
  async createDigitalHuman(createDto: CreateDigitalHumanDto): Promise<DigitalHumanInfo> {
    this.logger.log(`创建数字人: ${createDto.name || 'Unnamed'}`);

    try {
      const digitalHuman: DigitalHumanInfo = {
        id: `dh_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
        name: createDto.name || `DigitalHuman_${Date.now()}`,
        avatarData: createDto.avatarData,
        sceneId: createDto.sceneId,
        position: createDto.position || { x: 0, y: 0, z: 0 },
        config: {
          enableAI: createDto.config?.enableAI || false,
          enableVoice: createDto.config?.enableVoice || false,
          enableInteraction: createDto.config?.enableInteraction || true,
          knowledgeBaseId: createDto.config?.knowledgeBaseId,
          personality: createDto.config?.personality || 'friendly'
        },
        createdAt: new Date(),
        status: 'active'
      };

      this.digitalHumans.set(digitalHuman.id, digitalHuman);
      this.statistics.activeDigitalHumans++;

      this.logger.log(`数字人创建完成: ${digitalHuman.id}`);
      return digitalHuman;

    } catch (error) {
      this.logger.error(`创建数字人失败`, error.stack);
      throw error;
    }
  }

  /**
   * 获取数字人列表
   */
  async getDigitalHumans(sceneId?: string): Promise<DigitalHumanInfo[]> {
    this.logger.log(`获取数字人列表, 场景: ${sceneId || 'all'}`);

    const digitalHumans = Array.from(this.digitalHumans.values());
    
    if (sceneId) {
      return digitalHumans.filter(dh => dh.sceneId === sceneId);
    }

    return digitalHumans;
  }

  /**
   * 获取数字人详情
   */
  async getDigitalHumanDetail(digitalHumanId: string): Promise<DigitalHumanInfo | null> {
    this.logger.log(`获取数字人详情: ${digitalHumanId}`);

    const digitalHuman = this.digitalHumans.get(digitalHumanId);
    return digitalHuman || null;
  }

  /**
   * 删除数字人
   */
  async removeDigitalHuman(digitalHumanId: string): Promise<{ success: boolean }> {
    this.logger.log(`删除数字人: ${digitalHumanId}`);

    try {
      const digitalHuman = this.digitalHumans.get(digitalHumanId);
      if (!digitalHuman) {
        throw new Error(`数字人不存在: ${digitalHumanId}`);
      }

      this.digitalHumans.delete(digitalHumanId);
      this.statistics.activeDigitalHumans = Math.max(0, this.statistics.activeDigitalHumans - 1);

      this.logger.log(`数字人删除完成: ${digitalHumanId}`);
      return { success: true };

    } catch (error) {
      this.logger.error(`删除数字人失败: ${digitalHumanId}`, error.stack);
      return { success: false };
    }
  }

  /**
   * 更新数字人配置
   */
  async updateDigitalHumanConfig(digitalHumanId: string, config: any): Promise<{ success: boolean }> {
    this.logger.log(`更新数字人配置: ${digitalHumanId}`);

    try {
      const digitalHuman = this.digitalHumans.get(digitalHumanId);
      if (!digitalHuman) {
        throw new Error(`数字人不存在: ${digitalHumanId}`);
      }

      // 更新配置
      Object.assign(digitalHuman.config, config);

      this.logger.log(`数字人配置更新完成: ${digitalHumanId}`);
      return { success: true };

    } catch (error) {
      this.logger.error(`更新数字人配置失败: ${digitalHumanId}`, error.stack);
      return { success: false };
    }
  }

  /**
   * 激活/停用数字人
   */
  async toggleDigitalHuman(digitalHumanId: string): Promise<{ success: boolean; status: string }> {
    this.logger.log(`切换数字人状态: ${digitalHumanId}`);

    try {
      const digitalHuman = this.digitalHumans.get(digitalHumanId);
      if (!digitalHuman) {
        throw new Error(`数字人不存在: ${digitalHumanId}`);
      }

      // 切换状态
      digitalHuman.status = digitalHuman.status === 'active' ? 'inactive' : 'active';

      this.logger.log(`数字人状态切换完成: ${digitalHumanId} -> ${digitalHuman.status}`);
      return { success: true, status: digitalHuman.status };

    } catch (error) {
      this.logger.error(`切换数字人状态失败: ${digitalHumanId}`, error.stack);
      return { success: false, status: 'error' };
    }
  }

  /**
   * 获取上传历史
   */
  async getUploadHistory(sceneId?: string): Promise<UploadResult[]> {
    this.logger.log(`获取上传历史, 场景: ${sceneId || 'all'}`);

    if (sceneId) {
      return this.uploadHistory.get(sceneId) || [];
    }

    // 返回所有场景的历史记录
    const allHistory: UploadResult[] = [];
    for (const history of this.uploadHistory.values()) {
      allHistory.push(...history);
    }

    return allHistory.sort((a, b) => b.processingTime - a.processingTime);
  }

  /**
   * 获取上传统计信息
   */
  async getUploadStatistics(): Promise<{
    totalUploads: number;
    successfulUploads: number;
    failedUploads: number;
    activeDigitalHumans: number;
    averageProcessingTime: number;
    supportedFormats: string[];
  }> {
    return {
      ...this.statistics,
      supportedFormats: this.supportedFormats
    };
  }

  /**
   * 验证虚拟化身文件
   */
  async validateAvatarFile(fileInfo: UploadFileInfo): Promise<{
    isValid: boolean;
    issues: string[];
    score: number;
    fileInfo: {
      name: string;
      size: string;
      type: string;
    };
  }> {
    this.logger.log(`验证虚拟化身文件: ${fileInfo.fileName}`);

    const validationResult = await this.validateFile(fileInfo);
    
    return {
      ...validationResult,
      fileInfo: {
        name: fileInfo.fileName,
        size: `${(fileInfo.fileSize / 1024 / 1024).toFixed(2)} MB`,
        type: fileInfo.fileType
      }
    };
  }

  /**
   * 获取支持的文件格式
   */
  async getSupportedFormats(): Promise<{
    formats: string[];
    maxFileSize: string;
    description: Record<string, string>;
  }> {
    return {
      formats: this.supportedFormats,
      maxFileSize: `${this.maxFileSize} MB`,
      description: {
        json: 'JSON格式的虚拟化身数据',
        bin: '二进制格式的虚拟化身数据',
        gltf: 'GLTF 3D模型格式',
        fbx: 'FBX 3D模型格式'
      }
    };
  }

  /**
   * 验证文件
   */
  private async validateFile(fileInfo: UploadFileInfo): Promise<{
    isValid: boolean;
    issues: string[];
    score: number;
  }> {
    const issues: string[] = [];

    // 检查文件大小
    const fileSizeInMB = fileInfo.fileSize / (1024 * 1024);
    if (fileSizeInMB > this.maxFileSize) {
      issues.push(`文件大小 ${fileSizeInMB.toFixed(2)}MB 超过限制 ${this.maxFileSize}MB`);
    }

    // 检查文件格式
    const fileExtension = fileInfo.fileName.split('.').pop()?.toLowerCase();
    if (!fileExtension || !this.supportedFormats.includes(fileExtension)) {
      issues.push(`不支持的文件格式: ${fileExtension}`);
    }

    // 检查文件内容
    if (!fileInfo.fileContent || fileInfo.fileContent.length === 0) {
      issues.push('文件内容为空');
    }

    const isValid = issues.length === 0;
    const score = isValid ? 1.0 : Math.max(0, 1 - issues.length * 0.2);

    return { isValid, issues, score };
  }

  /**
   * 解析虚拟化身文件
   */
  private async parseAvatarFile(fileInfo: UploadFileInfo): Promise<any> {
    const fileExtension = fileInfo.fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'json':
        return this.parseJsonFile(fileInfo);
      case 'bin':
        return this.parseBinaryFile(fileInfo);
      case 'gltf':
        return this.parseGltfFile(fileInfo);
      case 'fbx':
        return this.parseFbxFile(fileInfo);
      default:
        throw new Error(`不支持的文件格式: ${fileExtension}`);
    }
  }

  /**
   * 解析JSON文件
   */
  private async parseJsonFile(fileInfo: UploadFileInfo): Promise<any> {
    try {
      const jsonContent = fileInfo.fileContent.toString('utf8');
      const data = JSON.parse(jsonContent);

      // 验证必要字段
      if (!data.id) {
        data.id = `uploaded_${Date.now()}`;
      }

      if (!data.createdAt) {
        data.createdAt = new Date();
      }

      if (!data.updatedAt) {
        data.updatedAt = new Date();
      }

      return data;
    } catch (error) {
      throw new Error(`JSON文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析二进制文件
   */
  private async parseBinaryFile(fileInfo: UploadFileInfo): Promise<any> {
    try {
      // 简化的二进制解析，实际应该根据具体格式实现
      const jsonContent = fileInfo.fileContent.toString('utf8');
      return JSON.parse(jsonContent);
    } catch (error) {
      throw new Error(`二进制文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析GLTF文件
   */
  private async parseGltfFile(fileInfo: UploadFileInfo): Promise<any> {
    try {
      // 这里应该实现GLTF格式的解析
      // 暂时返回模拟数据
      return {
        id: `gltf_${Date.now()}`,
        userId: 'uploaded_user',
        createdAt: new Date(),
        updatedAt: new Date(),
        gltfData: fileInfo.fileContent
      };
    } catch (error) {
      throw new Error(`GLTF文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析FBX文件
   */
  private async parseFbxFile(fileInfo: UploadFileInfo): Promise<any> {
    try {
      // 这里应该实现FBX格式的解析
      // 暂时返回模拟数据
      return {
        id: `fbx_${Date.now()}`,
        userId: 'uploaded_user',
        createdAt: new Date(),
        updatedAt: new Date(),
        fbxData: fileInfo.fileContent
      };
    } catch (error) {
      throw new Error(`FBX文件解析失败: ${error.message}`);
    }
  }

  /**
   * 验证虚拟化身数据
   */
  private async validateAvatarData(
    avatarData: any,
    options?: UploadConfigDto['validationOptions']
  ): Promise<{
    isValid: boolean;
    issues: string[];
    score: number;
  }> {
    const issues: string[] = [];
    const strictMode = options?.strictMode ?? true;

    // 验证必要字段
    if (!avatarData.id) {
      issues.push('缺少虚拟化身ID');
    }

    if (!avatarData.createdAt) {
      issues.push('缺少创建时间');
    }

    if (!avatarData.updatedAt) {
      issues.push('缺少更新时间');
    }

    // 严格模式下的额外验证
    if (strictMode) {
      if (!avatarData.userId) {
        issues.push('缺少用户ID');
      }

      if (avatarData.id && avatarData.id.length < 3) {
        issues.push('虚拟化身ID过短');
      }
    }

    const isValid = issues.length === 0 || (options?.allowPartialData && issues.length <= 2);
    const score = Math.max(0, 1 - issues.length * 0.1);

    return { isValid, issues, score };
  }

  /**
   * 从数据创建数字人
   */
  private async createDigitalHumanFromData(
    avatarData: any,
    config: UploadConfigDto
  ): Promise<DigitalHumanInfo> {
    const digitalHuman: DigitalHumanInfo = {
      id: `dh_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      name: config.digitalHumanName || avatarData.name || `DigitalHuman_${Date.now()}`,
      avatarData,
      sceneId: config.targetSceneId,
      position: config.spawnPosition || { x: 0, y: 0, z: 0 },
      config: {
        enableAI: config.digitalHumanConfig?.enableAI || false,
        enableVoice: config.digitalHumanConfig?.enableVoice || false,
        enableInteraction: config.digitalHumanConfig?.enableInteraction || true,
        knowledgeBaseId: config.digitalHumanConfig?.knowledgeBaseId,
        personality: config.digitalHumanConfig?.personality || 'friendly'
      },
      createdAt: new Date(),
      status: 'active'
    };

    return digitalHuman;
  }

  /**
   * 生成上传ID
   */
  private generateUploadId(fileName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9]/g, '_');
    return `upload_${cleanFileName}_${timestamp}_${random}`;
  }

  /**
   * 记录上传历史
   */
  private recordUploadHistory(sceneId: string, result: UploadResult): void {
    if (!this.uploadHistory.has(sceneId)) {
      this.uploadHistory.set(sceneId, []);
    }

    const history = this.uploadHistory.get(sceneId)!;
    history.push(result);

    // 限制历史记录数量
    if (history.length > 20) {
      history.shift();
    }
  }

  /**
   * 更新平均处理时间
   */
  private updateAverageProcessingTime(processingTime: number): void {
    const totalProcessed = this.statistics.successfulUploads + this.statistics.failedUploads;
    this.statistics.averageProcessingTime =
      (this.statistics.averageProcessingTime * (totalProcessed - 1) + processingTime) / totalProcessed;
  }
}
