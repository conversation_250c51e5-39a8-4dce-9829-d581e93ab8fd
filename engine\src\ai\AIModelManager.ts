/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { AIModelFactory } from './AIModelFactory';
import { AIModelLoadOptions } from './AIModelLoadOptions';
import { IAIModel } from './models/IAIModel';
import { GPTModel } from './models/GPTModel';
import { StableDiffusionModel } from './models/StableDiffusionModel';
import { BERTModel } from './models/BERTModel';
import { RoBERTaModel } from './models/RoBERTaModel';
import { DistilBERTModel } from './models/DistilBERTModel';
import { ALBERTModel } from './models/ALBERTModel';
import { XLNetModel } from './models/XLNetModel';
import { BARTModel } from './models/BARTModel';
import { T5Model } from './models/T5Model';

/**
 * 模型状态
 */
export enum ModelStatus {
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 加载中 */
  LOADING = 'loading',
  /** 已加载 */
  LOADED = 'loaded',
  /** 预热中 */
  WARMING_UP = 'warming_up',
  /** 就绪 */
  READY = 'ready',
  /** 运行中 */
  RUNNING = 'running',
  /** 错误 */
  ERROR = 'error',
  /** 已卸载 */
  UNLOADED_COMPLETE = 'unloaded_complete'
}

/**
 * 模型实例信息
 */
export interface ModelInstanceInfo {
  /** 模型ID */
  id: string;
  /** 模型类型 */
  type: AIModelType;
  /** 模型状态 */
  status: ModelStatus;
  /** 模型实例 */
  model: IAIModel;
  /** 配置 */
  config: AIModelConfig;
  /** 加载选项 */
  loadOptions: AIModelLoadOptions;
  /** 创建时间 */
  createdAt: number;
  /** 最后使用时间 */
  lastUsedAt: number;
  /** 使用次数 */
  usageCount: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
  /** 是否预热 */
  isWarmedUp: boolean;
  /** 版本信息 */
  version: string;
  /** 性能指标 */
  performanceMetrics: PerformanceMetrics;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 平均响应时间（毫秒） */
  averageResponseTime: number;
  /** 最大响应时间（毫秒） */
  maxResponseTime: number;
  /** 最小响应时间（毫秒） */
  minResponseTime: number;
  /** 吞吐量（请求/秒） */
  throughput: number;
  /** 错误率 */
  errorRate: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 内存使用率 */
  memoryUsage: number;
  /** 最后更新时间 */
  lastUpdated: number;
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failureCount: number;
  /** 总数量 */
  totalCount: number;
  /** 成功的模型ID列表 */
  successIds: string[];
  /** 失败的模型ID列表 */
  failureIds: string[];
  /** 错误信息 */
  errors: Array<{ modelId: string; error: Error }>;
}

/**
 * AI模型管理器配置
 */
export interface AIModelManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 模型缓存大小 */
  cacheSize?: number;
  /** 是否使用本地模型 */
  useLocalModels?: boolean;
  /** 模型API密钥 */
  apiKeys?: Record<string, string>;
  /** 模型基础URL */
  baseUrls?: Record<string, string>;
  /** 模型版本 */
  modelVersions?: Record<string, string>;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 性能监控间隔（毫秒） */
  performanceMonitoringInterval?: number;
  /** 是否启用模型预热 */
  enableModelWarmup?: boolean;
  /** 预热超时时间（毫秒） */
  warmupTimeout?: number;
  /** 是否启用热重载 */
  enableHotReload?: boolean;
  /** 热重载检查间隔（毫秒） */
  hotReloadInterval?: number;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 模型健康检查间隔（毫秒） */
  healthCheckInterval?: number;
  /** 是否启用智能模型选择 */
  enableSmartModelSelection?: boolean;
}

/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
export class AIModelManager extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 50;

  /** 配置 */
  private config: Required<AIModelManagerConfig>;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<AIModelManagerConfig> = {
    debug: false,
    cacheSize: 5,
    useLocalModels: false,
    apiKeys: {},
    baseUrls: {},
    modelVersions: {},
    enablePerformanceMonitoring: false,
    performanceMonitoringInterval: 5000,
    enableModelWarmup: false,
    warmupTimeout: 30000,
    enableHotReload: false,
    hotReloadInterval: 60000,
    maxConcurrentLoads: 3,
    healthCheckInterval: 30000,
    enableSmartModelSelection: false
  };

  /** 模型实例信息 */
  private modelInstances: Map<string, ModelInstanceInfo> = new Map();

  /** 模型加载进度 */
  private modelLoadProgress: Map<string, number> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 模型工厂 */
  private modelFactory!: AIModelFactory;

  /** 模型加载队列 */
  private loadQueue: Array<{
    modelType: AIModelType;
    config: AIModelConfig;
    options?: AIModelLoadOptions;
    resolve: (model: IAIModel | null) => void;
    reject: (error: Error) => void;
  }> = [];

  /** 是否正在加载模型 */
  private isLoading: boolean = false;

  /** 当前并发加载数 */
  private currentConcurrentLoads: number = 0;

  /** 性能监控定时器 */
  private performanceMonitorTimer?: NodeJS.Timeout;

  /** 热重载定时器 */
  private hotReloadTimer?: NodeJS.Timeout;

  /** 健康检查定时器 */
  private healthCheckTimer?: NodeJS.Timeout;

  /** 模型版本映射 */
  private modelVersions: Map<string, string> = new Map();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: AIModelManagerConfig = {}) {
    // 使用系统优先级作为参数
    super(AIModelManager.PRIORITY);

    // 设置世界引用
    this.setWorld(world);

    // 合并配置
    this.config = {
      ...AIModelManager.DEFAULT_CONFIG,
      ...config
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('初始化AI模型管理器');
      console.log(`世界实例: ${this.world ? '已设置' : '未设置'}`);
    }

    // 创建模型工厂
    this.modelFactory = new AIModelFactory(this.config);

    // 初始化事件发射器
    this.eventEmitter = new EventEmitter();

    // 启动监控功能
    this.startPerformanceMonitoring();
    this.startHotReloadMonitoring();
    this.startHealthCheck();

    if (this.config.debug) {
      console.log('AI模型管理器初始化完成');
    }
  }

  /**
   * 加载模型
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 加载选项
   * @returns 模型实例
   */
  public async loadModel(
    modelType: AIModelType,
    config: AIModelConfig = {},
    options: AIModelLoadOptions = {}
  ): Promise<IAIModel | null> {
    // 生成模型ID
    const modelId = this.generateModelId(modelType, config);

    // 检查模型是否已加载
    const existingInstance = this.modelInstances.get(modelId);
    if (existingInstance && existingInstance.status !== ModelStatus.ERROR) {
      if (this.config.debug) {
        console.log(`模型已加载: ${modelId}`);
      }
      // 更新使用统计
      existingInstance.lastUsedAt = Date.now();
      existingInstance.usageCount++;
      return existingInstance.model;
    }

    // 检查并发加载限制
    if (this.currentConcurrentLoads >= this.config.maxConcurrentLoads) {
      if (this.config.debug) {
        console.log(`达到最大并发加载数限制，加入队列: ${modelId}`);
      }
    }

    // 创建加载承诺
    return new Promise<IAIModel | null>((resolve, reject) => {
      // 添加到加载队列
      this.loadQueue.push({
        modelType,
        config,
        options,
        resolve,
        reject
      });

      // 如果没有正在加载的模型，开始加载
      if (!this.isLoading) {
        this.processLoadQueue();
      }
    });
  }

  /**
   * 异步批量加载模型
   * @param requests 加载请求列表
   * @returns 批量操作结果
   */
  public async loadModels(requests: Array<{
    modelType: AIModelType;
    config?: AIModelConfig;
    options?: AIModelLoadOptions;
  }>): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      successCount: 0,
      failureCount: 0,
      totalCount: requests.length,
      successIds: [],
      failureIds: [],
      errors: []
    };

    const loadPromises = requests.map(async (request) => {
      try {
        const model = await this.loadModel(
          request.modelType,
          request.config || {},
          request.options || {}
        );

        if (model) {
          const modelId = this.generateModelId(request.modelType, request.config || {});
          result.successCount++;
          result.successIds.push(modelId);
          return { success: true, modelId, model };
        } else {
          const modelId = this.generateModelId(request.modelType, request.config || {});
          result.failureCount++;
          result.failureIds.push(modelId);
          result.errors.push({
            modelId,
            error: new Error(`模型加载失败: ${modelId}`)
          });
          return { success: false, modelId, error: new Error(`模型加载失败: ${modelId}`) };
        }
      } catch (error) {
        const modelId = this.generateModelId(request.modelType, request.config || {});
        result.failureCount++;
        result.failureIds.push(modelId);
        result.errors.push({ modelId, error: error as Error });
        return { success: false, modelId, error: error as Error };
      }
    });

    await Promise.allSettled(loadPromises);
    return result;
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue(): Promise<void> {
    // 如果队列为空或达到并发限制，返回
    if (this.loadQueue.length === 0 || this.currentConcurrentLoads >= this.config.maxConcurrentLoads) {
      this.isLoading = false;
      return;
    }

    // 设置加载状态
    this.isLoading = true;
    this.currentConcurrentLoads++;

    // 获取队列中的第一个任务
    const task = this.loadQueue.shift();
    if (!task) {
      this.isLoading = false;
      this.currentConcurrentLoads--;
      return;
    }

    try {
      // 生成模型ID
      const modelId = this.generateModelId(task.modelType, task.config);

      // 创建模型实例信息
      const instanceInfo: ModelInstanceInfo = {
        id: modelId,
        type: task.modelType,
        status: ModelStatus.LOADING,
        model: null as any, // 临时设置，稍后更新
        config: task.config,
        loadOptions: task.options || {},
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        usageCount: 0,
        memoryUsage: 0,
        isWarmedUp: false,
        version: task.config.version || this.config.modelVersions[task.modelType] || '1.0.0',
        performanceMetrics: {
          averageResponseTime: 0,
          maxResponseTime: 0,
          minResponseTime: 0,
          throughput: 0,
          errorRate: 0,
          cpuUsage: 0,
          memoryUsage: 0,
          lastUpdated: Date.now()
        }
      };

      // 添加到模型实例映射
      this.modelInstances.set(modelId, instanceInfo);

      // 设置加载进度
      this.modelLoadProgress.set(modelId, 0);
      this.eventEmitter.emit('modelLoadProgress', {
        modelId,
        progress: 0
      });

      // 创建模型实例
      const model = await this.createModelInstance(task.modelType, task.config, task.options);

      // 如果模型创建成功，更新实例信息
      if (model) {
        instanceInfo.model = model;
        instanceInfo.status = ModelStatus.LOADED;
        instanceInfo.memoryUsage = this.estimateMemoryUsage(model);

        // 检查缓存大小限制
        if (this.modelInstances.size > this.config.cacheSize) {
          this.evictLeastRecentlyUsedModel();
        }

        // 设置加载进度为100%
        this.modelLoadProgress.set(modelId, 1);
        this.eventEmitter.emit('modelLoadProgress', {
          modelId,
          progress: 1
        });

        // 如果启用预热，进行预热
        if (this.config.enableModelWarmup) {
          await this.warmupModel(modelId);
        }

        // 触发模型加载完成事件
        this.eventEmitter.emit('modelLoaded', {
          modelId,
          model,
          instanceInfo
        });

        // 解析承诺
        task.resolve(model);
      } else {
        // 如果模型创建失败，更新状态并移除
        instanceInfo.status = ModelStatus.ERROR;
        this.modelInstances.delete(modelId);

        // 触发错误事件
        this.eventEmitter.emit('modelLoadError', {
          modelId,
          error: new Error(`无法创建模型: ${modelId}`)
        });

        // 拒绝承诺
        task.reject(new Error(`无法创建模型: ${modelId}`));
      }
    } catch (error) {
      const modelId = this.generateModelId(task.modelType, task.config);

      // 更新实例状态
      const instanceInfo = this.modelInstances.get(modelId);
      if (instanceInfo) {
        instanceInfo.status = ModelStatus.ERROR;
      }

      // 触发错误事件
      this.eventEmitter.emit('modelLoadError', {
        modelId,
        error
      });

      // 拒绝承诺
      task.reject(error as Error);
    } finally {
      // 减少并发计数
      this.currentConcurrentLoads--;

      // 继续处理队列
      if (this.loadQueue.length > 0) {
        setTimeout(() => this.processLoadQueue(), 0);
      } else {
        this.isLoading = false;
      }
    }
  }

  /**
   * 估算模型内存使用量
   */
  private estimateMemoryUsage(model: IAIModel): number {
    // 简单的内存估算，实际应该根据模型类型和配置计算
    const baseMemory = 100 * 1024 * 1024; // 100MB基础内存
    const modelType = model.getType();

    switch (modelType) {
      case AIModelType.GPT:
        return baseMemory * 10; // 1GB
      case AIModelType.STABLE_DIFFUSION:
        return baseMemory * 20; // 2GB
      case AIModelType.BERT:
        return baseMemory * 5; // 500MB
      case AIModelType.ROBERTA:
        return baseMemory * 6; // 600MB
      case AIModelType.DISTILBERT:
        return baseMemory * 3; // 300MB
      case AIModelType.ALBERT:
        return baseMemory * 4; // 400MB
      case AIModelType.XLNET:
        return baseMemory * 7; // 700MB
      case AIModelType.BART:
        return baseMemory * 8; // 800MB
      case AIModelType.T5:
        return baseMemory * 9; // 900MB
      default:
        return baseMemory;
    }
  }

  /**
   * 驱逐最近最少使用的模型
   */
  private evictLeastRecentlyUsedModel(): void {
    let oldestTime = Date.now();
    let oldestId = '';

    for (const [id, instance] of this.modelInstances) {
      if (instance.lastUsedAt < oldestTime) {
        oldestTime = instance.lastUsedAt;
        oldestId = id;
      }
    }

    if (oldestId) {
      this.unloadModel(oldestId);

      if (this.config.debug) {
        console.log(`驱逐最近最少使用的模型: ${oldestId}`);
      }
    }
  }

  /**
   * 预热模型
   */
  private async warmupModel(modelId: string): Promise<void> {
    const instanceInfo = this.modelInstances.get(modelId);
    if (!instanceInfo) {
      return;
    }

    try {
      instanceInfo.status = ModelStatus.WARMING_UP;

      // 执行预热操作
      await instanceInfo.model.initialize();

      // 执行简单的推理来预热模型
      if (typeof instanceInfo.model.generateText === 'function') {
        await instanceInfo.model.generateText('warmup', { maxTokens: 1 });
      }

      instanceInfo.isWarmedUp = true;
      instanceInfo.status = ModelStatus.READY;

      this.eventEmitter.emit('modelWarmedUp', { modelId, instanceInfo });

      if (this.config.debug) {
        console.log(`模型预热完成: ${modelId}`);
      }
    } catch (error) {
      console.warn(`模型预热失败: ${modelId}`, error);
      instanceInfo.status = ModelStatus.LOADED; // 回退到已加载状态
    }
  }

  /**
   * 批量预热模型
   */
  public async warmupModels(modelIds: string[]): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      successCount: 0,
      failureCount: 0,
      totalCount: modelIds.length,
      successIds: [],
      failureIds: [],
      errors: []
    };

    const warmupPromises = modelIds.map(async (modelId) => {
      try {
        await this.warmupModel(modelId);
        result.successCount++;
        result.successIds.push(modelId);
        return { success: true, modelId };
      } catch (error) {
        result.failureCount++;
        result.failureIds.push(modelId);
        result.errors.push({ modelId, error: error as Error });
        return { success: false, modelId, error: error as Error };
      }
    });

    await Promise.allSettled(warmupPromises);
    return result;
  }

  /**
   * 创建模型实例
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 加载选项
   * @returns 模型实例
   */
  private async createModelInstance(
    modelType: AIModelType,
    config: AIModelConfig,
    options: AIModelLoadOptions = {}
  ): Promise<IAIModel | null> {
    try {
      // 使用加载选项（如果需要）
      if (options.useCache !== undefined) {
        // 这里可以根据需要使用options参数
      }

      // 使用模型工厂创建模型实例
      if (this.modelFactory && options.useFactory !== false) {
        try {
          const model = this.modelFactory.createModel(modelType, config);
          if (model) {
            return model;
          }
        } catch (error) {
          if (this.config.debug) {
            console.warn(`模型工厂创建模型失败，回退到直接创建: ${modelType}`, error);
          }
        }
      }

      // 根据模型类型创建不同的模型实例
      switch (modelType) {
        case AIModelType.GPT:
          return new GPTModel(config as any, this.config);
        case AIModelType.STABLE_DIFFUSION:
          return new StableDiffusionModel(config as any, this.config);
        case AIModelType.BERT:
          return new BERTModel(config as any, this.config);
        case AIModelType.ROBERTA:
          // 转换为RoBERTa特定配置
          return new RoBERTaModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'distilled' | undefined
          } as any, this.config);
        case AIModelType.DISTILBERT:
          // 转换为DistilBERT特定配置
          return new DistilBERTModel({
            ...config,
            variant: config.variant as 'base' | 'multilingual' | undefined
          } as any, this.config);
        case AIModelType.ALBERT:
          // 转换为ALBERT特定配置
          return new ALBERTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'xlarge' | 'xxlarge' | undefined
          } as any, this.config);
        case AIModelType.XLNET:
          // 转换为XLNet特定配置
          return new XLNetModel({
            ...config,
            variant: config.variant as 'base' | 'large' | undefined
          } as any, this.config);
        case AIModelType.BART:
          // 转换为BART特定配置
          return new BARTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'cnn' | undefined
          } as any, this.config);
        case AIModelType.T5:
          // 转换为T5特定配置
          return new T5Model({
            ...config,
            variant: config.variant as 'small' | 'base' | 'large' | 'xl' | 'xxl' | undefined
          } as any, this.config);
        default:
          console.error(`不支持的模型类型: ${modelType}`);
          return null;
      }
    } catch (error) {
      console.error(`创建模型实例失败: ${error}`);
      return null;
    }
  }

  /**
   * 生成模型ID
   * @param modelType 模型类型
   * @param config 模型配置
   * @returns 模型ID
   */
  private generateModelId(modelType: AIModelType, config: AIModelConfig): string {
    // 基本ID
    let id = `${modelType}`;

    // 添加版本信息
    if (config.version) {
      id += `-${config.version}`;
    } else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
      id += `-${this.config.modelVersions[modelType]}`;
    }

    // 添加其他配置信息
    if (config.variant) {
      id += `-${config.variant}`;
    }

    return id;
  }

  /**
   * 获取已加载的模型
   * @param modelId 模型ID
   * @returns 模型实例
   */
  public getModel(modelId: string): IAIModel | null {
    const instanceInfo = this.modelInstances.get(modelId);
    if (instanceInfo && instanceInfo.status !== ModelStatus.ERROR) {
      instanceInfo.lastUsedAt = Date.now();
      instanceInfo.usageCount++;
      return instanceInfo.model;
    }
    return null;
  }

  /**
   * 获取模型实例信息
   * @param modelId 模型ID
   * @returns 模型实例信息
   */
  public getModelInstance(modelId: string): ModelInstanceInfo | null {
    return this.modelInstances.get(modelId) || null;
  }

  /**
   * 获取所有已加载的模型
   * @returns 模型实例映射
   */
  public getAllModels(): Map<string, IAIModel> {
    const result = new Map<string, IAIModel>();
    for (const [id, instanceInfo] of this.modelInstances) {
      if (instanceInfo.status !== ModelStatus.ERROR) {
        result.set(id, instanceInfo.model);
      }
    }
    return result;
  }

  /**
   * 获取所有模型实例信息
   * @returns 模型实例信息映射
   */
  public getAllModelInstances(): Map<string, ModelInstanceInfo> {
    return new Map(this.modelInstances);
  }

  /**
   * 卸载模型
   * @param modelId 模型ID
   * @returns 是否成功
   */
  public unloadModel(modelId: string): boolean {
    // 检查模型是否已加载
    const instanceInfo = this.modelInstances.get(modelId);
    if (!instanceInfo) {
      return false;
    }

    try {
      // 卸载模型
      if (instanceInfo.model && typeof instanceInfo.model.dispose === 'function') {
        instanceInfo.model.dispose();
      }

      // 更新状态
      instanceInfo.status = ModelStatus.UNLOADED_COMPLETE;

      // 从实例映射中移除
      this.modelInstances.delete(modelId);

      // 清理进度信息
      this.modelLoadProgress.delete(modelId);

      // 触发模型卸载事件
      this.eventEmitter.emit('modelUnloaded', {
        modelId,
        instanceInfo
      });

      if (this.config.debug) {
        console.log(`模型已卸载: ${modelId}`);
      }

      return true;
    } catch (error) {
      console.error(`卸载模型失败: ${modelId}`, error);
      return false;
    }
  }

  /**
   * 批量卸载模型
   * @param modelIds 模型ID列表
   * @returns 批量操作结果
   */
  public unloadModels(modelIds: string[]): BatchOperationResult {
    const result: BatchOperationResult = {
      successCount: 0,
      failureCount: 0,
      totalCount: modelIds.length,
      successIds: [],
      failureIds: [],
      errors: []
    };

    for (const modelId of modelIds) {
      try {
        const success = this.unloadModel(modelId);
        if (success) {
          result.successCount++;
          result.successIds.push(modelId);
        } else {
          result.failureCount++;
          result.failureIds.push(modelId);
          result.errors.push({
            modelId,
            error: new Error(`模型未加载或卸载失败: ${modelId}`)
          });
        }
      } catch (error) {
        result.failureCount++;
        result.failureIds.push(modelId);
        result.errors.push({ modelId, error: error as Error });
      }
    }

    return result;
  }

  /**
   * 卸载所有模型
   */
  public unloadAllModels(): void {
    const modelIds = Array.from(this.modelInstances.keys());

    for (const modelId of modelIds) {
      this.unloadModel(modelId);
    }

    // 清理所有相关数据
    this.modelInstances.clear();
    this.modelLoadProgress.clear();

    if (this.config.debug) {
      console.log('所有模型已卸载');
    }

    this.eventEmitter.emit('allModelsUnloaded');
  }

  /**
   * 获取模型加载进度
   * @param modelId 模型ID
   * @returns 加载进度 (0-1)
   */
  public getModelLoadProgress(modelId: string): number {
    return this.modelLoadProgress.get(modelId) || 0;
  }

  /**
   * 检查模型健康状态
   * @param modelId 模型ID
   * @returns 健康状态信息
   */
  public async checkModelHealth(modelId: string): Promise<{
    healthy: boolean;
    status: ModelStatus;
    responseTime?: number;
    memoryUsage: number;
    lastUsed: number;
    usageCount: number;
    errors?: string[];
  } | null> {
    const instanceInfo = this.modelInstances.get(modelId);
    if (!instanceInfo) {
      return null;
    }

    const errors: string[] = [];
    let responseTime: number | undefined;
    let healthy = true;

    try {
      // 执行简单的健康检查
      const startTime = Date.now();

      if (typeof instanceInfo.model.generateText === 'function') {
        await Promise.race([
          instanceInfo.model.generateText('health check', { maxTokens: 1 }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('健康检查超时')), 5000)
          )
        ]);
      }

      responseTime = Date.now() - startTime;

      // 检查响应时间
      if (responseTime > 3000) {
        errors.push('响应时间过长');
        healthy = false;
      }

    } catch (error) {
      errors.push(`健康检查失败: ${error}`);
      healthy = false;
    }

    return {
      healthy,
      status: instanceInfo.status,
      responseTime,
      memoryUsage: instanceInfo.memoryUsage,
      lastUsed: instanceInfo.lastUsedAt,
      usageCount: instanceInfo.usageCount,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  /**
   * 批量健康检查
   * @param modelIds 模型ID列表，如果为空则检查所有模型
   * @returns 健康检查结果
   */
  public async batchHealthCheck(modelIds?: string[]): Promise<Map<string, any>> {
    const idsToCheck = modelIds || Array.from(this.modelInstances.keys());
    const results = new Map<string, any>();

    const checkPromises = idsToCheck.map(async (modelId) => {
      try {
        const health = await this.checkModelHealth(modelId);
        results.set(modelId, health);
      } catch (error) {
        results.set(modelId, {
          healthy: false,
          error: error
        });
      }
    });

    await Promise.allSettled(checkPromises);
    return results;
  }

  /**
   * 获取AI模型性能统计
   */
  public getAIModelStats(): {
    totalModels: number;
    loadedModels: number;
    readyModels: number;
    errorModels: number;
    totalMemoryUsage: number;
    averageResponseTime: number;
    totalUsageCount: number;
    modelsByType: Map<AIModelType, number>;
    modelsByStatus: Map<ModelStatus, number>;
  } {
    const stats = {
      totalModels: this.modelInstances.size,
      loadedModels: 0,
      readyModels: 0,
      errorModels: 0,
      totalMemoryUsage: 0,
      averageResponseTime: 0,
      totalUsageCount: 0,
      modelsByType: new Map<AIModelType, number>(),
      modelsByStatus: new Map<ModelStatus, number>()
    };

    let totalResponseTime = 0;
    let responseTimeCount = 0;

    for (const instanceInfo of this.modelInstances.values()) {
      // 按状态统计
      stats.modelsByStatus.set(
        instanceInfo.status,
        (stats.modelsByStatus.get(instanceInfo.status) || 0) + 1
      );

      // 按类型统计
      stats.modelsByType.set(
        instanceInfo.type,
        (stats.modelsByType.get(instanceInfo.type) || 0) + 1
      );

      // 状态统计
      switch (instanceInfo.status) {
        case ModelStatus.LOADED:
        case ModelStatus.READY:
        case ModelStatus.RUNNING:
          stats.loadedModels++;
          break;
        case ModelStatus.READY:
          stats.readyModels++;
          break;
        case ModelStatus.ERROR:
          stats.errorModels++;
          break;
      }

      // 内存和性能统计
      stats.totalMemoryUsage += instanceInfo.memoryUsage;
      stats.totalUsageCount += instanceInfo.usageCount;

      if (instanceInfo.performanceMetrics.averageResponseTime > 0) {
        totalResponseTime += instanceInfo.performanceMetrics.averageResponseTime;
        responseTimeCount++;
      }
    }

    if (responseTimeCount > 0) {
      stats.averageResponseTime = totalResponseTime / responseTimeCount;
    }

    return stats;
  }

  /**
   * 获取系统性能统计（重写基类方法）
   * @returns 系统性能统计
   */
  public getPerformanceStats(): import('../core/System').SystemPerformanceStats {
    // 获取基类的性能统计
    const baseStats = super.getPerformanceStats();

    // 添加AI模型管理器特定的统计信息
    const aiStats = this.getAIModelStats();

    // 将AI模型数量作为处理的实体数量
    baseStats.processedEntities = aiStats.totalModels;

    return baseStats;
  }

  /**
   * 智能模型选择
   * 根据任务类型和性能要求选择最优模型
   */
  public selectOptimalModel(
    taskType: string,
    requirements?: {
      maxResponseTime?: number;
      maxMemoryUsage?: number;
      minAccuracy?: number;
      preferredTypes?: AIModelType[];
    }
  ): IAIModel | null {
    if (!this.config.enableSmartModelSelection) {
      return null;
    }

    const candidates: Array<{ model: IAIModel; score: number; instanceInfo: ModelInstanceInfo }> = [];

    for (const instanceInfo of this.modelInstances.values()) {
      if (instanceInfo.status !== ModelStatus.READY && instanceInfo.status !== ModelStatus.LOADED) {
        continue;
      }

      // 检查类型偏好
      if (requirements?.preferredTypes && !requirements.preferredTypes.includes(instanceInfo.type)) {
        continue;
      }

      // 检查性能要求
      if (requirements?.maxResponseTime &&
          instanceInfo.performanceMetrics.averageResponseTime > requirements.maxResponseTime) {
        continue;
      }

      if (requirements?.maxMemoryUsage && instanceInfo.memoryUsage > requirements.maxMemoryUsage) {
        continue;
      }

      // 计算评分
      let score = 0;

      // 响应时间评分（越低越好）
      if (instanceInfo.performanceMetrics.averageResponseTime > 0) {
        score += Math.max(0, 100 - instanceInfo.performanceMetrics.averageResponseTime / 10);
      } else {
        score += 50; // 默认分数
      }

      // 内存使用评分（越低越好）
      score += Math.max(0, 100 - instanceInfo.memoryUsage / (1024 * 1024 * 1024)); // GB

      // 使用频率评分
      score += Math.min(50, instanceInfo.usageCount);

      // 预热状态加分
      if (instanceInfo.isWarmedUp) {
        score += 20;
      }

      candidates.push({ model: instanceInfo.model, score, instanceInfo });
    }

    // 按评分排序
    candidates.sort((a, b) => b.score - a.score);

    if (candidates.length > 0) {
      const selected = candidates[0];

      if (this.config.debug) {
        console.log(`智能选择模型: ${selected.instanceInfo.id}, 评分: ${selected.score}`);
      }

      return selected.model;
    }

    return null;
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }

    this.performanceMonitorTimer = setInterval(() => {
      this.updatePerformanceMetrics();
    }, this.config.performanceMonitoringInterval);

    if (this.config.debug) {
      console.log('性能监控已启动');
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    for (const instanceInfo of this.modelInstances.values()) {
      // 这里可以实现实际的性能指标收集
      // 目前只是模拟数据更新
      instanceInfo.performanceMetrics.lastUpdated = Date.now();

      // 可以添加实际的CPU、内存监控逻辑
      // instanceInfo.performanceMetrics.cpuUsage = getCurrentCpuUsage();
      // instanceInfo.performanceMetrics.memoryUsage = getCurrentMemoryUsage();
    }
  }

  /**
   * 启动热重载监控
   */
  private startHotReloadMonitoring(): void {
    if (!this.config.enableHotReload) {
      return;
    }

    this.hotReloadTimer = setInterval(() => {
      this.checkForModelUpdates();
    }, this.config.hotReloadInterval);

    if (this.config.debug) {
      console.log('热重载监控已启动');
    }
  }

  /**
   * 检查模型更新
   */
  private async checkForModelUpdates(): Promise<void> {
    for (const [modelId, instanceInfo] of this.modelInstances) {
      try {
        // 检查模型版本是否有更新
        const currentVersion = instanceInfo.version;
        const latestVersion = await this.getLatestModelVersion(instanceInfo.type);

        if (latestVersion && latestVersion !== currentVersion) {
          if (this.config.debug) {
            console.log(`检测到模型更新: ${modelId}, ${currentVersion} -> ${latestVersion}`);
          }

          await this.hotReloadModel(modelId, latestVersion);
        }
      } catch (error) {
        console.warn(`检查模型更新失败: ${modelId}`, error);
      }
    }
  }

  /**
   * 获取最新模型版本
   */
  private async getLatestModelVersion(modelType: AIModelType): Promise<string | null> {
    // 这里应该实现实际的版本检查逻辑
    // 可以从远程服务器、配置文件或其他来源获取最新版本信息
    return null;
  }

  /**
   * 热重载模型
   */
  private async hotReloadModel(modelId: string, newVersion: string): Promise<void> {
    const instanceInfo = this.modelInstances.get(modelId);
    if (!instanceInfo) {
      return;
    }

    try {
      // 保存当前配置
      const config = { ...instanceInfo.config, version: newVersion };
      const loadOptions = instanceInfo.loadOptions;
      const modelType = instanceInfo.type;

      // 卸载旧模型
      this.unloadModel(modelId);

      // 加载新模型
      await this.loadModel(modelType, config, loadOptions);

      this.eventEmitter.emit('modelHotReloaded', {
        modelId,
        oldVersion: instanceInfo.version,
        newVersion
      });

      if (this.config.debug) {
        console.log(`模型热重载完成: ${modelId}`);
      }
    } catch (error) {
      console.error(`模型热重载失败: ${modelId}`, error);

      this.eventEmitter.emit('modelHotReloadError', {
        modelId,
        error
      });
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, this.config.healthCheckInterval);

    if (this.config.debug) {
      console.log('健康检查已启动');
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const healthResults = await this.batchHealthCheck();

    for (const [modelId, health] of healthResults) {
      if (!health.healthy) {
        this.eventEmitter.emit('modelHealthIssue', {
          modelId,
          health
        });

        if (this.config.debug) {
          console.warn(`模型健康问题: ${modelId}`, health.errors);
        }
      }
    }
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   * @returns this 实例，用于链式调用
   */
  public addListener(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   * @returns this 实例，用于链式调用
   */
  public removeListener(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 监听事件（兼容System类）
   * @param event 事件名称
   * @param callback 回调函数
   * @returns this 实例，用于链式调用
   */
  public on(event: string, callback: (...args: any[]) => void): this {
    return this.addListener(event, callback);
  }

  /**
   * 取消监听事件（兼容System类）
   * @param event 事件名称
   * @param callback 回调函数
   * @returns this 实例，用于链式调用
   */
  public off(event: string, callback?: (...args: any[]) => void): this {
    if (callback) {
      return this.removeListener(event, callback);
    } else {
      // 移除指定事件的所有监听器
      // 注意：EventEmitter 的 removeAllListeners 方法可能不接受参数
      // 这里做一个兼容处理
      try {
        // @ts-ignore - 忽略类型检查
        this.eventEmitter.removeAllListeners(event);
      } catch (error) {
        console.warn(`无法移除事件 ${event} 的所有监听器:`, error);
      }
      return this;
    }
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 停止所有定时器
    if (this.performanceMonitorTimer) {
      clearInterval(this.performanceMonitorTimer);
      this.performanceMonitorTimer = undefined;
    }

    if (this.hotReloadTimer) {
      clearInterval(this.hotReloadTimer);
      this.hotReloadTimer = undefined;
    }

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }

    // 卸载所有模型
    this.unloadAllModels();

    // 清空队列
    this.loadQueue.length = 0;

    // 重置状态
    this.isLoading = false;
    this.currentConcurrentLoads = 0;

    // 清空版本映射
    this.modelVersions.clear();

    // 销毁模型工厂
    if (this.modelFactory) {
      this.modelFactory.dispose();
    }

    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('AI模型管理器已销毁');
    }
  }

  /**
   * 获取管理器状态
   */
  public getManagerStatus(): {
    isInitialized: boolean;
    totalModels: number;
    loadingModels: number;
    readyModels: number;
    errorModels: number;
    queueLength: number;
    concurrentLoads: number;
    monitoringEnabled: {
      performance: boolean;
      hotReload: boolean;
      healthCheck: boolean;
    };
  } {
    const stats = this.getAIModelStats();

    return {
      isInitialized: !!this.modelFactory,
      totalModels: stats.totalModels,
      loadingModels: stats.modelsByStatus.get(ModelStatus.LOADING) || 0,
      readyModels: stats.readyModels,
      errorModels: stats.errorModels,
      queueLength: this.loadQueue.length,
      concurrentLoads: this.currentConcurrentLoads,
      monitoringEnabled: {
        performance: !!this.performanceMonitorTimer,
        hotReload: !!this.hotReloadTimer,
        healthCheck: !!this.healthCheckTimer
      }
    };
  }

  /**
   * 重新加载模型
   * @param modelId 模型ID
   * @returns 是否成功
   */
  public async reloadModel(modelId: string): Promise<boolean> {
    const instanceInfo = this.modelInstances.get(modelId);
    if (!instanceInfo) {
      return false;
    }

    try {
      const config = instanceInfo.config;
      const loadOptions = instanceInfo.loadOptions;
      const modelType = instanceInfo.type;

      // 卸载旧模型
      this.unloadModel(modelId);

      // 重新加载模型
      const model = await this.loadModel(modelType, config, loadOptions);

      this.eventEmitter.emit('modelReloaded', {
        modelId,
        model
      });

      return !!model;
    } catch (error) {
      console.error(`重新加载模型失败: ${modelId}`, error);
      return false;
    }
  }

  /**
   * 清理未使用的模型
   * @param maxIdleTime 最大空闲时间（毫秒）
   * @returns 清理的模型数量
   */
  public cleanupUnusedModels(maxIdleTime: number = 3600000): number { // 默认1小时
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [modelId, instanceInfo] of this.modelInstances) {
      if (now - instanceInfo.lastUsedAt > maxIdleTime && instanceInfo.usageCount === 0) {
        toRemove.push(modelId);
      }
    }

    for (const modelId of toRemove) {
      this.unloadModel(modelId);
    }

    if (this.config.debug && toRemove.length > 0) {
      console.log(`清理了 ${toRemove.length} 个未使用的模型`);
    }

    this.eventEmitter.emit('modelsCleanedUp', {
      cleanedCount: toRemove.length,
      cleanedIds: toRemove
    });

    return toRemove.length;
  }
}
