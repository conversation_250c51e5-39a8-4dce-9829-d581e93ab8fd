/**
 * AI情感分析系统功能测试
 * 验证修复后的功能是否正常工作
 */
import { 
  AIEmotionAnalysisSystem, 
  EmotionType, 
  AnalysisMethod,
  EmotionAnalysisOptions 
} from '../AIEmotionAnalysisSystem';

/**
 * AI情感分析系统测试类
 */
export class AIEmotionAnalysisSystemTest {
  private emotionSystem: AIEmotionAnalysisSystem;

  constructor() {
    // 创建情感分析系统实例，启用所有功能
    this.emotionSystem = new AIEmotionAnalysisSystem({
      debug: true,
      analysisMethod: AnalysisMethod.HYBRID,
      enableHistory: true,
      maxHistoryLength: 50,
      enableRealtime: true,
      language: 'zh',
      enableEmoticonAnalysis: true,
      enableToneAnalysis: true,
      useLocalModel: false
    });
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI情感分析系统功能测试开始 ===\n');

    try {
      await this.testBasicAnalysis();
      await this.testDeepLearningAnalysis();
      await this.testToneAnalysis();
      await this.testEmotionChanges();
      await this.testRealtimeAnalysis();
      await this.testBatchAnalysis();
      await this.testCustomDictionary();
      await this.testSystemStatus();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    } finally {
      this.emotionSystem.dispose();
    }
  }

  /**
   * 测试基本情感分析功能
   */
  private async testBasicAnalysis(): Promise<void> {
    console.log('1. 测试基本情感分析功能');
    
    const testTexts = [
      '我今天非常开心！',
      '感觉很难过，想哭😢',
      '这让我很愤怒！！！',
      '哇，太惊讶了',
      '有点担心和焦虑...',
      '这真的很恶心'
    ];

    for (const text of testTexts) {
      const result = await this.emotionSystem.analyzeEmotion(text, {
        detailed: true,
        includeSecondary: true
      });

      if (result) {
        console.log(`文本: "${text}"`);
        console.log(`主要情感: ${result.primaryEmotion} (强度: ${result.primaryIntensity.toFixed(2)})`);
        if (result.secondaryEmotion) {
          console.log(`次要情感: ${result.secondaryEmotion} (强度: ${result.secondaryIntensity?.toFixed(2)})`);
        }
        console.log(`置信度: ${result.confidence?.toFixed(2)}`);
        console.log('---');
      }
    }
    console.log();
  }

  /**
   * 测试深度学习分析
   */
  private async testDeepLearningAnalysis(): Promise<void> {
    console.log('2. 测试深度学习分析功能');
    
    // 临时切换到深度学习模式
    this.emotionSystem.updateConfig({ analysisMethod: AnalysisMethod.DEEP_LEARNING });
    
    const result = await this.emotionSystem.analyzeEmotion('我对这个项目既兴奋又紧张', {
      detailed: true
    });

    if (result) {
      console.log('深度学习分析结果:');
      console.log(`主要情感: ${result.primaryEmotion} (强度: ${result.primaryIntensity.toFixed(2)})`);
      console.log(`置信度: ${result.confidence?.toFixed(2)}`);
      if (result.detailedEmotions) {
        console.log('详细信息:', result.detailedEmotions);
      }
    }

    // 恢复混合模式
    this.emotionSystem.updateConfig({ analysisMethod: AnalysisMethod.HYBRID });
    console.log();
  }

  /**
   * 测试语调分析功能
   */
  private async testToneAnalysis(): Promise<void> {
    console.log('3. 测试语调分析功能');
    
    const toneTexts = [
      '什么？？？这怎么可能！！！',
      '哈哈哈哈哈，太好笑了',
      '唉...又是这样...',
      '呜呜呜，好难过',
      '哇！！！太棒了！！！'
    ];

    for (const text of toneTexts) {
      const result = await this.emotionSystem.analyzeEmotion(text);
      if (result) {
        console.log(`文本: "${text}" -> ${result.primaryEmotion} (${result.primaryIntensity.toFixed(2)})`);
      }
    }
    console.log();
  }

  /**
   * 测试情感变化分析
   */
  private async testEmotionChanges(): Promise<void> {
    console.log('4. 测试情感变化分析功能');
    
    const changeTexts = [
      '开始我很开心',
      '但是后来发生了一些事情',
      '突然我变得很愤怒',
      '最终我平静下来了'
    ];

    for (const text of changeTexts) {
      const result = await this.emotionSystem.analyzeEmotion(text, {
        includeChanges: true
      });

      if (result) {
        console.log(`文本: "${text}" -> ${result.primaryEmotion}`);
        if (result.emotionChanges && result.emotionChanges.length > 0) {
          console.log('情感变化:', result.emotionChanges);
        }
      }
    }
    console.log();
  }

  /**
   * 测试实时分析功能
   */
  private async testRealtimeAnalysis(): Promise<void> {
    console.log('5. 测试实时分析功能');
    
    return new Promise((resolve) => {
      let processedCount = 0;
      const totalTexts = 3;
      
      const realtimeTexts = [
        '实时分析测试1：我很高兴',
        '实时分析测试2：有点担心',
        '实时分析测试3：非常兴奋！'
      ];

      // 监听实时分析事件
      this.emotionSystem.addEventListener('realtimeEmotionAnalyzed', (data) => {
        console.log(`实时分析结果: "${data.text}" -> ${data.result?.primaryEmotion}`);
        processedCount++;
        
        if (processedCount >= totalTexts) {
          console.log();
          resolve();
        }
      });

      // 添加到实时分析队列
      realtimeTexts.forEach(text => {
        this.emotionSystem.addToRealtimeQueue(text);
      });
    });
  }

  /**
   * 测试批量分析功能
   */
  private async testBatchAnalysis(): Promise<void> {
    console.log('6. 测试批量分析功能');
    
    const batchTexts = [
      '批量测试1：开心',
      '批量测试2：悲伤',
      '批量测试3：愤怒'
    ];

    const results = await this.emotionSystem.batchAnalyzeEmotions(batchTexts);
    
    console.log(`批量分析完成，处理了 ${results.length} 个文本`);
    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.primaryEmotion} (${result.primaryIntensity.toFixed(2)})`);
    });
    console.log();
  }

  /**
   * 测试自定义词典功能
   */
  private async testCustomDictionary(): Promise<void> {
    console.log('7. 测试自定义词典功能');
    
    // 添加自定义情感词
    this.emotionSystem.addEmotionWord({
      word: '超赞',
      emotion: EmotionType.EXCITED,
      intensity: 0.9,
      weight: 1.0
    });

    const result = await this.emotionSystem.analyzeEmotion('这个功能超赞的！');
    if (result) {
      console.log(`自定义词典测试: ${result.primaryEmotion} (${result.primaryIntensity.toFixed(2)})`);
    }

    // 获取词典信息
    const dictionary = this.emotionSystem.getEmotionDictionary();
    console.log(`当前词典包含 ${dictionary.length} 个情感词`);
    console.log();
  }

  /**
   * 测试系统状态功能
   */
  private async testSystemStatus(): Promise<void> {
    console.log('8. 测试系统状态功能');
    
    const status = this.emotionSystem.getSystemStatus();
    console.log('系统状态:', status);

    const statistics = this.emotionSystem.getEmotionStatistics();
    console.log('情感统计:', statistics);

    const history = this.emotionSystem.getEmotionHistory(5);
    console.log(`最近 ${history.length} 条分析记录`);
    console.log();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIEmotionAnalysisSystemTest();
  test.runAllTests().catch(console.error);
}
