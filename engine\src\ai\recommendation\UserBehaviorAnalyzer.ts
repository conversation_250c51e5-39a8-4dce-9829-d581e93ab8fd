/**
 * 用户行为分析器
 * 分析用户行为模式，构建用户画像
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { 
  UserProfile, 
  UserInteraction, 
  UserFeedback, 
  BehaviorPattern,
  SkillAssessment,
  SkillLevel,
  UserPreferences,
  CollaborationStyle
} from '../AIRecommendationEngine';

export interface UserBehaviorAnalyzerConfig {
  debug?: boolean;
  analysisWindow?: number;     // 分析窗口大小(天)
  patternThreshold?: number;   // 模式识别阈值
  updateInterval?: number;     // 更新间隔(分钟)
  maxInteractionHistory?: number; // 最大交互历史记录数
  enableEmotionAnalysis?: boolean; // 启用情感分析
  enableAnomalyDetection?: boolean; // 启用异常检测
  enableCollaborationAnalysis?: boolean; // 启用协作分析
  enableLearningPathAnalysis?: boolean; // 启用学习路径分析
  persistenceEnabled?: boolean; // 启用数据持久化
  cacheTimeout?: number;       // 缓存超时时间(分钟)
}

// 分析统计
export interface AnalysisStats {
  totalUsers: number;
  totalInteractions: number;
  averageSessionDuration: number;
  mostActiveHours: number[];
  topActivities: string[];
  skillDistribution: Map<SkillLevel, number>;
  collaborationRate: number;
  learningEfficiency: number;
  lastUpdated: Date;
}

// 情感状态
export interface EmotionalState {
  emotion: string;
  intensity: number;          // 强度 (0-1)
  confidence: number;         // 置信度 (0-1)
  timestamp: Date;
  context: string[];
}

// 异常行为
export interface AnomalyDetection {
  type: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  timestamp: Date;
  affectedMetrics: string[];
  suggestedActions: string[];
}

// 学习路径分析
export interface LearningPathAnalysis {
  currentPath: string[];
  completionRate: number;
  estimatedTimeToComplete: number;
  recommendedNextSteps: string[];
  difficultyProgression: number[];
  stuckPoints: string[];
}

/**
 * 用户行为分析器
 */
export class UserBehaviorAnalyzer {
  private config: UserBehaviorAnalyzerConfig;
  private eventEmitter: EventEmitter = new EventEmitter();
  
  // 用户画像缓存
  private userProfiles: Map<string, UserProfile> = new Map();
  
  // 用户交互历史
  private userInteractions: Map<string, UserInteraction[]> = new Map();
  
  // 行为模式缓存
  private behaviorPatterns: Map<string, BehaviorPattern[]> = new Map();

  // 情感状态缓存
  private emotionalStates: Map<string, EmotionalState[]> = new Map();

  // 异常检测缓存
  private anomalies: Map<string, AnomalyDetection[]> = new Map();

  // 学习路径分析缓存
  private learningPaths: Map<string, LearningPathAnalysis> = new Map();

  // 分析统计
  private analysisStats: AnalysisStats;

  // 更新定时器
  private updateTimer?: NodeJS.Timeout;

  constructor(config: UserBehaviorAnalyzerConfig = {}) {
    this.config = {
      debug: false,
      analysisWindow: 30,
      patternThreshold: 0.7,
      updateInterval: 60,
      maxInteractionHistory: 5000,
      enableEmotionAnalysis: true,
      enableAnomalyDetection: true,
      enableCollaborationAnalysis: true,
      enableLearningPathAnalysis: true,
      persistenceEnabled: false,
      cacheTimeout: 120,
      ...config
    };

    this.initializeStats();
    this.startUpdateTimer();

    if (this.config.debug) {
      console.log('用户行为分析器初始化完成');
    }
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): void {
    this.analysisStats = {
      totalUsers: 0,
      totalInteractions: 0,
      averageSessionDuration: 0,
      mostActiveHours: [],
      topActivities: [],
      skillDistribution: new Map(),
      collaborationRate: 0,
      learningEfficiency: 0,
      lastUpdated: new Date()
    };
  }

  /**
   * 启动更新定时器
   */
  private startUpdateTimer(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }

    this.updateTimer = setInterval(() => {
      this.performPeriodicUpdate();
    }, this.config.updateInterval! * 60 * 1000);
  }

  /**
   * 分析用户画像
   */
  public async analyzeUser(userId: string): Promise<UserProfile> {
    // 检查缓存
    if (this.userProfiles.has(userId)) {
      const profile = this.userProfiles.get(userId)!;
      // 检查是否需要更新
      if (this.shouldUpdateProfile(profile)) {
        return await this.updateUserProfile(userId);
      }
      return profile;
    }

    // 创建新的用户画像
    return await this.createUserProfile(userId);
  }

  /**
   * 创建用户画像
   */
  private async createUserProfile(userId: string): Promise<UserProfile> {
    const interactions = await this.getUserInteractions(userId);
    const behaviorPatterns = await this.analyzeBehaviorPatterns(userId, interactions);
    const skillAssessment = await this.assessUserSkills(userId, interactions);
    const preferences = await this.inferUserPreferences(userId, interactions);

    const profile: UserProfile = {
      userId,
      demographics: await this.getUserDemographics(userId),
      behaviorPatterns,
      skillAssessment,
      preferences,
      collaborationHistory: await this.getCollaborationHistory(userId),
      learningProgress: await this.getLearningProgress(userId)
    };

    // 缓存用户画像
    this.userProfiles.set(userId, profile);

    if (this.config.debug) {
      console.log(`创建用户画像: ${userId}`);
    }

    return profile;
  }

  /**
   * 更新用户画像
   */
  private async updateUserProfile(userId: string): Promise<UserProfile> {
    const existingProfile = this.userProfiles.get(userId);
    if (!existingProfile) {
      return await this.createUserProfile(userId);
    }

    // 获取最新交互数据
    const recentInteractions = await this.getRecentInteractions(userId);
    
    // 更新行为模式
    const updatedPatterns = await this.updateBehaviorPatterns(
      userId, 
      existingProfile.behaviorPatterns, 
      recentInteractions
    );

    // 更新技能评估
    const updatedSkills = await this.updateSkillAssessment(
      existingProfile.skillAssessment,
      recentInteractions
    );

    // 更新偏好
    const updatedPreferences = await this.updateUserPreferences(
      existingProfile.preferences,
      recentInteractions
    );

    const updatedProfile: UserProfile = {
      ...existingProfile,
      behaviorPatterns: updatedPatterns,
      skillAssessment: updatedSkills,
      preferences: updatedPreferences,
      learningProgress: await this.updateLearningProgress(userId, existingProfile.learningProgress)
    };

    this.userProfiles.set(userId, updatedProfile);

    if (this.config.debug) {
      console.log(`更新用户画像: ${userId}`);
    }

    return updatedProfile;
  }

  /**
   * 分析行为模式
   */
  private async analyzeBehaviorPatterns(
    userId: string, 
    interactions: UserInteraction[]
  ): Promise<BehaviorPattern[]> {
    const patterns: BehaviorPattern[] = [];

    // 分析时间模式
    const timePattern = this.analyzeTimePattern(interactions);
    if (timePattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'time_preference',
        frequency: timePattern.frequency,
        context: ['working_hours'],
        timePattern: timePattern.hours
      });
    }

    // 分析活动模式
    const activityPattern = this.analyzeActivityPattern(interactions);
    if (activityPattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'activity_preference',
        frequency: activityPattern.frequency,
        context: activityPattern.activities,
        timePattern: []
      });
    }

    // 分析工具使用模式
    const toolPattern = this.analyzeToolUsagePattern(interactions);
    if (toolPattern.confidence > this.config.patternThreshold!) {
      patterns.push({
        pattern: 'tool_preference',
        frequency: toolPattern.frequency,
        context: toolPattern.tools,
        timePattern: []
      });
    }

    return patterns;
  }

  /**
   * 分析时间模式
   */
  private analyzeTimePattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    hours: number[];
  } {
    const hourCounts = new Array(24).fill(0);
    
    interactions.forEach(interaction => {
      const hour = interaction.timestamp.getHours();
      hourCounts[hour]++;
    });

    const totalInteractions = interactions.length;
    const maxCount = Math.max(...hourCounts);
    const activeHours = hourCounts.filter(count => count > 0).length;

    return {
      confidence: activeHours > 0 ? maxCount / totalInteractions : 0,
      frequency: totalInteractions / this.config.analysisWindow!,
      hours: hourCounts.map((count, hour) => count > totalInteractions * 0.1 ? hour : -1)
               .filter(hour => hour !== -1)
    };
  }

  /**
   * 分析活动模式
   */
  private analyzeActivityPattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    activities: string[];
  } {
    const activityCounts: Map<string, number> = new Map();
    
    interactions.forEach(interaction => {
      const count = activityCounts.get(interaction.action) || 0;
      activityCounts.set(interaction.action, count + 1);
    });

    const totalInteractions = interactions.length;
    const topActivities = Array.from(activityCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([activity]) => activity);

    const topActivityCount = Math.max(...Array.from(activityCounts.values()));

    return {
      confidence: totalInteractions > 0 ? topActivityCount / totalInteractions : 0,
      frequency: totalInteractions / this.config.analysisWindow!,
      activities: topActivities
    };
  }

  /**
   * 分析工具使用模式
   */
  private analyzeToolUsagePattern(interactions: UserInteraction[]): {
    confidence: number;
    frequency: number;
    tools: string[];
  } {
    const toolCounts: Map<string, number> = new Map();
    
    interactions.forEach(interaction => {
      if (interaction.context && interaction.context.tool) {
        const tool = interaction.context.tool;
        const count = toolCounts.get(tool) || 0;
        toolCounts.set(tool, count + 1);
      }
    });

    const totalToolUsage = Array.from(toolCounts.values()).reduce((sum, count) => sum + count, 0);
    const topTools = Array.from(toolCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([tool]) => tool);

    const topToolCount = Math.max(...Array.from(toolCounts.values()));

    return {
      confidence: totalToolUsage > 0 ? topToolCount / totalToolUsage : 0,
      frequency: totalToolUsage / this.config.analysisWindow!,
      tools: topTools
    };
  }

  /**
   * 评估用户技能
   */
  private async assessUserSkills(
    userId: string, 
    interactions: UserInteraction[]
  ): Promise<SkillAssessment> {
    const specificSkills = new Map<string, number>();

    // 基于交互分析技能水平
    const skillMetrics = this.calculateSkillMetrics(interactions);
    
    // 3D建模技能
    if (skillMetrics.modeling.interactions > 10) {
      specificSkills.set('3d_modeling', skillMetrics.modeling.proficiency);
    }

    // 动画技能
    if (skillMetrics.animation.interactions > 5) {
      specificSkills.set('animation', skillMetrics.animation.proficiency);
    }

    // 脚本编程技能
    if (skillMetrics.scripting.interactions > 3) {
      specificSkills.set('scripting', skillMetrics.scripting.proficiency);
    }

    // 材质设计技能
    if (skillMetrics.materials.interactions > 8) {
      specificSkills.set('materials', skillMetrics.materials.proficiency);
    }

    // 计算整体技能水平
    const overallLevel = this.calculateOverallSkillLevel(specificSkills);

    return {
      overallLevel,
      specificSkills,
      learningSpeed: skillMetrics.learningSpeed,
      adaptability: skillMetrics.adaptability
    };
  }

  /**
   * 计算技能指标
   */
  private calculateSkillMetrics(interactions: UserInteraction[]): any {
    const metrics = {
      modeling: { interactions: 0, proficiency: 0 },
      animation: { interactions: 0, proficiency: 0 },
      scripting: { interactions: 0, proficiency: 0 },
      materials: { interactions: 0, proficiency: 0 },
      learningSpeed: 0.5,
      adaptability: 0.5
    };

    // 分析各类技能的交互次数和熟练度
    interactions.forEach(interaction => {
      const action = interaction.action.toLowerCase();
      
      if (action.includes('model') || action.includes('mesh')) {
        metrics.modeling.interactions++;
        metrics.modeling.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('anim')) {
        metrics.animation.interactions++;
        metrics.animation.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('script') || action.includes('code')) {
        metrics.scripting.interactions++;
        metrics.scripting.proficiency += this.calculateActionProficiency(interaction);
      } else if (action.includes('material') || action.includes('texture')) {
        metrics.materials.interactions++;
        metrics.materials.proficiency += this.calculateActionProficiency(interaction);
      }
    });

    // 标准化熟练度分数
    Object.keys(metrics).forEach(key => {
      if (key !== 'learningSpeed' && key !== 'adaptability') {
        const skill = metrics[key as keyof typeof metrics] as any;
        if (skill.interactions > 0) {
          skill.proficiency = Math.min(1.0, skill.proficiency / skill.interactions);
        }
      }
    });

    return metrics;
  }

  /**
   * 计算操作熟练度
   */
  private calculateActionProficiency(interaction: UserInteraction): number {
    let proficiency = 0.5; // 基础分数

    // 基于操作时长评估
    if (interaction.duration) {
      if (interaction.duration < 30000) { // 30秒内完成，熟练度高
        proficiency += 0.3;
      } else if (interaction.duration > 300000) { // 5分钟以上，熟练度低
        proficiency -= 0.2;
      }
    }

    // 基于上下文信息评估
    if (interaction.context) {
      if (interaction.context.success) {
        proficiency += 0.2;
      }
      if (interaction.context.errors && interaction.context.errors > 0) {
        proficiency -= 0.1 * interaction.context.errors;
      }
    }

    return Math.max(0, Math.min(1, proficiency));
  }

  /**
   * 计算整体技能水平
   */
  private calculateOverallSkillLevel(specificSkills: Map<string, number>): SkillLevel {
    if (specificSkills.size === 0) {
      return SkillLevel.BEGINNER;
    }

    const averageSkill = Array.from(specificSkills.values())
      .reduce((sum, skill) => sum + skill, 0) / specificSkills.size;

    if (averageSkill >= 0.8) {
      return SkillLevel.EXPERT;
    } else if (averageSkill >= 0.6) {
      return SkillLevel.ADVANCED;
    } else if (averageSkill >= 0.4) {
      return SkillLevel.INTERMEDIATE;
    } else {
      return SkillLevel.BEGINNER;
    }
  }

  // 其他辅助方法的实现...
  private shouldUpdateProfile(profile: UserProfile): boolean {
    const now = Date.now();
    const lastUpdate = profile.learningProgress.lastActivity.getTime();
    const timeSinceUpdate = now - lastUpdate;

    // 如果超过缓存超时时间，需要更新
    const cacheTimeoutMs = (this.config.cacheTimeout || 120) * 60 * 1000;
    if (timeSinceUpdate > cacheTimeoutMs) {
      return true;
    }

    // 如果用户有新的交互记录，需要更新
    const userInteractions = this.userInteractions.get(profile.userId) || [];
    const recentInteractions = userInteractions.filter(
      interaction => interaction.timestamp.getTime() > lastUpdate
    );

    return recentInteractions.length > 0;
  }

  /**
   * 定期更新处理
   */
  private async performPeriodicUpdate(): Promise<void> {
    try {
      // 更新统计数据
      this.updateAnalysisStats();

      // 清理过期数据
      this.cleanupExpiredData();

      // 触发定期更新事件
      this.eventEmitter.emit('periodic.update', {
        timestamp: new Date(),
        stats: this.analysisStats
      });

      if (this.config.debug) {
        console.log('定期更新完成');
      }
    } catch (error) {
      console.error('定期更新失败:', error);
      this.eventEmitter.emit('update.error', { error });
    }
  }

  /**
   * 更新分析统计
   */
  private updateAnalysisStats(): void {
    this.analysisStats.totalUsers = this.userProfiles.size;
    this.analysisStats.totalInteractions = Array.from(this.userInteractions.values())
      .reduce((total, interactions) => total + interactions.length, 0);

    // 计算平均会话时长
    const allInteractions = Array.from(this.userInteractions.values()).flat();
    if (allInteractions.length > 0) {
      const totalDuration = allInteractions
        .filter(i => i.duration)
        .reduce((sum, i) => sum + (i.duration || 0), 0);
      this.analysisStats.averageSessionDuration = totalDuration / allInteractions.length;
    }

    // 分析最活跃时间
    this.analysisStats.mostActiveHours = this.calculateMostActiveHours(allInteractions);

    // 分析热门活动
    this.analysisStats.topActivities = this.calculateTopActivities(allInteractions);

    // 技能分布统计
    this.analysisStats.skillDistribution = this.calculateSkillDistribution();

    this.analysisStats.lastUpdated = new Date();
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const cutoffTime = Date.now() - this.config.analysisWindow! * 24 * 60 * 60 * 1000;

    // 清理过期交互记录
    this.userInteractions.forEach((interactions, userId) => {
      const validInteractions = interactions.filter(
        interaction => interaction.timestamp.getTime() > cutoffTime
      );

      if (validInteractions.length !== interactions.length) {
        this.userInteractions.set(userId, validInteractions);
      }
    });

    // 清理过期情感状态
    this.emotionalStates.forEach((states, userId) => {
      const validStates = states.filter(
        state => state.timestamp.getTime() > cutoffTime
      );

      if (validStates.length !== states.length) {
        this.emotionalStates.set(userId, validStates);
      }
    });
  }

  /**
   * 计算最活跃时间
   */
  private calculateMostActiveHours(interactions: UserInteraction[]): number[] {
    const hourCounts = new Array(24).fill(0);

    interactions.forEach(interaction => {
      const hour = interaction.timestamp.getHours();
      hourCounts[hour]++;
    });

    // 找出活跃度最高的前5个小时
    return hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .map(item => item.hour);
  }

  /**
   * 计算热门活动
   */
  private calculateTopActivities(interactions: UserInteraction[]): string[] {
    const activityCounts = new Map<string, number>();

    interactions.forEach(interaction => {
      const count = activityCounts.get(interaction.action) || 0;
      activityCounts.set(interaction.action, count + 1);
    });

    return Array.from(activityCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([activity]) => activity);
  }

  /**
   * 计算技能分布
   */
  private calculateSkillDistribution(): Map<SkillLevel, number> {
    const distribution = new Map<SkillLevel, number>();

    // 初始化计数
    Object.values(SkillLevel).forEach(level => {
      distribution.set(level, 0);
    });

    // 统计各技能水平的用户数量
    this.userProfiles.forEach(profile => {
      const currentCount = distribution.get(profile.skillAssessment.overallLevel) || 0;
      distribution.set(profile.skillAssessment.overallLevel, currentCount + 1);
    });

    return distribution;
  }

  private async getUserInteractions(userId: string): Promise<UserInteraction[]> {
    return this.userInteractions.get(userId) || [];
  }

  private async getRecentInteractions(userId: string): Promise<UserInteraction[]> {
    const interactions = this.userInteractions.get(userId) || [];
    const cutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天前
    return interactions.filter(interaction => interaction.timestamp > cutoff);
  }

  private async getUserDemographics(userId: string): Promise<any> {
    // 基于用户交互推断人口统计信息
    const interactions = this.userInteractions.get(userId) || [];

    if (interactions.length === 0) {
      return {
        ageGroup: 'unknown',
        location: 'unknown',
        profession: 'unknown',
        experience: 0
      };
    }

    // 基于使用模式推断经验水平
    const totalInteractions = interactions.length;
    const uniqueActions = new Set(interactions.map(i => i.action)).size;
    const averageDuration = interactions
      .filter(i => i.duration)
      .reduce((sum, i) => sum + (i.duration || 0), 0) / interactions.length;

    let experience = 0;
    if (totalInteractions > 1000 && uniqueActions > 20) {
      experience = 3; // 高级用户
    } else if (totalInteractions > 100 && uniqueActions > 10) {
      experience = 2; // 中级用户
    } else if (totalInteractions > 10) {
      experience = 1; // 初级用户
    }

    // 基于活动时间推断地理位置（时区）
    const activeHours = this.calculateMostActiveHours(interactions);
    let location = 'unknown';
    if (activeHours.some(h => h >= 9 && h <= 17)) {
      location = 'office_timezone';
    } else if (activeHours.some(h => h >= 18 && h <= 23)) {
      location = 'evening_timezone';
    }

    return {
      ageGroup: experience > 2 ? 'adult' : 'young_adult',
      location,
      profession: this.inferProfession(interactions),
      experience
    };
  }

  private async getCollaborationHistory(userId: string): Promise<any[]> {
    // 基于交互记录分析协作历史
    const interactions = this.userInteractions.get(userId) || [];
    const collaborationEvents = interactions.filter(
      i => i.action.includes('collaborate') ||
           i.action.includes('share') ||
           i.action.includes('invite')
    );

    const collaborationHistory = [];
    const projectCollaborations = new Map<string, any>();

    collaborationEvents.forEach(interaction => {
      const projectId = interaction.context?.projectId || 'unknown';

      if (!projectCollaborations.has(projectId)) {
        projectCollaborations.set(projectId, {
          projectId,
          collaborators: new Set(),
          role: this.inferCollaborationRole(interaction),
          duration: 0,
          satisfaction: 0.7, // 默认满意度
          effectiveness: 0.6, // 默认效率
          startTime: interaction.timestamp,
          endTime: interaction.timestamp
        });
      }

      const collab = projectCollaborations.get(projectId)!;
      if (interaction.context?.collaboratorId) {
        collab.collaborators.add(interaction.context.collaboratorId);
      }
      collab.endTime = interaction.timestamp;
      collab.duration = collab.endTime.getTime() - collab.startTime.getTime();
    });

    projectCollaborations.forEach(collab => {
      collaborationHistory.push({
        ...collab,
        collaborators: Array.from(collab.collaborators)
      });
    });

    return collaborationHistory;
  }

  private async getLearningProgress(userId: string): Promise<any> {
    const interactions = this.userInteractions.get(userId) || [];
    const skillAssessment = await this.assessUserSkills(userId, interactions);

    // 分析学习活动
    const learningActivities = interactions.filter(
      i => i.action.includes('learn') ||
           i.action.includes('tutorial') ||
           i.action.includes('help')
    );

    // 识别完成的课程
    const completedCourses = this.identifyCompletedCourses(interactions);

    // 分析当前目标
    const currentGoals = this.inferCurrentGoals(interactions);

    // 识别薄弱和强项领域
    const { weakAreas, strongAreas } = this.analyzeSkillAreas(skillAssessment);

    return {
      completedCourses,
      currentGoals,
      weakAreas,
      strongAreas,
      lastActivity: interactions.length > 0 ?
        interactions[interactions.length - 1].timestamp : new Date()
    };
  }

  /**
   * 推断用户职业
   */
  private inferProfession(interactions: UserInteraction[]): string {
    const actionCounts = new Map<string, number>();

    interactions.forEach(interaction => {
      const action = interaction.action.toLowerCase();
      actionCounts.set(action, (actionCounts.get(action) || 0) + 1);
    });

    // 基于主要活动推断职业
    const topActions = Array.from(actionCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([action]) => action);

    if (topActions.some(action => action.includes('model') || action.includes('design'))) {
      return '3d_artist';
    } else if (topActions.some(action => action.includes('script') || action.includes('code'))) {
      return 'developer';
    } else if (topActions.some(action => action.includes('anim'))) {
      return 'animator';
    } else if (topActions.some(action => action.includes('teach') || action.includes('help'))) {
      return 'educator';
    } else {
      return 'general_user';
    }
  }

  /**
   * 推断协作角色
   */
  private inferCollaborationRole(interaction: UserInteraction): string {
    const action = interaction.action.toLowerCase();

    if (action.includes('lead') || action.includes('manage')) {
      return 'leader';
    } else if (action.includes('help') || action.includes('mentor')) {
      return 'mentor';
    } else if (action.includes('follow') || action.includes('learn')) {
      return 'follower';
    } else {
      return 'collaborator';
    }
  }

  /**
   * 识别完成的课程
   */
  private identifyCompletedCourses(interactions: UserInteraction[]): string[] {
    const courseCompletions = new Set<string>();

    interactions.forEach(interaction => {
      if (interaction.action.includes('complete') &&
          interaction.context?.courseId) {
        courseCompletions.add(interaction.context.courseId);
      }
    });

    return Array.from(courseCompletions);
  }

  /**
   * 推断当前目标
   */
  private inferCurrentGoals(interactions: UserInteraction[]): string[] {
    const recentInteractions = interactions
      .filter(i => Date.now() - i.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000) // 最近7天
      .slice(-50); // 最近50个交互

    const goalPatterns = new Map<string, number>();

    recentInteractions.forEach(interaction => {
      const action = interaction.action.toLowerCase();

      if (action.includes('learn')) {
        goalPatterns.set('learning', (goalPatterns.get('learning') || 0) + 1);
      } else if (action.includes('create') || action.includes('build')) {
        goalPatterns.set('creation', (goalPatterns.get('creation') || 0) + 1);
      } else if (action.includes('improve') || action.includes('optimize')) {
        goalPatterns.set('improvement', (goalPatterns.get('improvement') || 0) + 1);
      } else if (action.includes('collaborate') || action.includes('share')) {
        goalPatterns.set('collaboration', (goalPatterns.get('collaboration') || 0) + 1);
      }
    });

    return Array.from(goalPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([goal]) => goal);
  }

  /**
   * 分析技能领域
   */
  private analyzeSkillAreas(skillAssessment: SkillAssessment): {
    weakAreas: string[];
    strongAreas: string[];
  } {
    const skillEntries = Array.from(skillAssessment.specificSkills.entries());

    const weakAreas = skillEntries
      .filter(([, proficiency]) => proficiency < 0.4)
      .sort((a, b) => a[1] - b[1])
      .slice(0, 3)
      .map(([skill]) => skill);

    const strongAreas = skillEntries
      .filter(([, proficiency]) => proficiency > 0.7)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([skill]) => skill);

    return { weakAreas, strongAreas };
  }

  private async inferUserPreferences(userId: string, interactions: UserInteraction[]): Promise<UserPreferences> {
    // 分析偏好风格
    const stylePreferences = this.analyzeStylePreferences(interactions);

    // 分析兴趣领域
    const interests = this.analyzeInterests(interactions);

    // 分析工作时间偏好
    const workingHours = this.analyzeWorkingHours(interactions);

    // 分析协作风格
    const collaborationStyle = this.analyzeCollaborationStyle(interactions);

    // 评估技能水平
    const skillAssessment = await this.assessUserSkills(userId, interactions);

    return {
      preferredStyles: stylePreferences,
      skillLevel: skillAssessment.overallLevel,
      interests,
      workingHours,
      collaborationStyle
    };
  }

  private async updateBehaviorPatterns(userId: string, patterns: BehaviorPattern[], interactions: UserInteraction[]): Promise<BehaviorPattern[]> {
    // 重新分析行为模式
    const newPatterns = await this.analyzeBehaviorPatterns(userId, interactions);

    // 合并新旧模式，保留有效的历史模式
    const mergedPatterns = [...patterns];

    newPatterns.forEach(newPattern => {
      const existingIndex = mergedPatterns.findIndex(p => p.pattern === newPattern.pattern);
      if (existingIndex >= 0) {
        // 更新现有模式
        mergedPatterns[existingIndex] = {
          ...mergedPatterns[existingIndex],
          frequency: (mergedPatterns[existingIndex].frequency + newPattern.frequency) / 2,
          context: [...new Set([...mergedPatterns[existingIndex].context, ...newPattern.context])],
          timePattern: newPattern.timePattern
        };
      } else {
        // 添加新模式
        mergedPatterns.push(newPattern);
      }
    });

    // 缓存更新的模式
    this.behaviorPatterns.set(userId, mergedPatterns);

    return mergedPatterns;
  }

  private async updateSkillAssessment(assessment: SkillAssessment, interactions: UserInteraction[]): Promise<SkillAssessment> {
    // 计算新的技能指标
    const newMetrics = this.calculateSkillMetrics(interactions);

    // 更新特定技能
    const updatedSpecificSkills = new Map(assessment.specificSkills);

    // 3D建模技能更新
    if (newMetrics.modeling.interactions > 0) {
      const currentSkill = updatedSpecificSkills.get('3d_modeling') || 0;
      const newSkill = (currentSkill + newMetrics.modeling.proficiency) / 2;
      updatedSpecificSkills.set('3d_modeling', newSkill);
    }

    // 动画技能更新
    if (newMetrics.animation.interactions > 0) {
      const currentSkill = updatedSpecificSkills.get('animation') || 0;
      const newSkill = (currentSkill + newMetrics.animation.proficiency) / 2;
      updatedSpecificSkills.set('animation', newSkill);
    }

    // 脚本技能更新
    if (newMetrics.scripting.interactions > 0) {
      const currentSkill = updatedSpecificSkills.get('scripting') || 0;
      const newSkill = (currentSkill + newMetrics.scripting.proficiency) / 2;
      updatedSpecificSkills.set('scripting', newSkill);
    }

    // 材质技能更新
    if (newMetrics.materials.interactions > 0) {
      const currentSkill = updatedSpecificSkills.get('materials') || 0;
      const newSkill = (currentSkill + newMetrics.materials.proficiency) / 2;
      updatedSpecificSkills.set('materials', newSkill);
    }

    return {
      overallLevel: this.calculateOverallSkillLevel(updatedSpecificSkills),
      specificSkills: updatedSpecificSkills,
      learningSpeed: (assessment.learningSpeed + newMetrics.learningSpeed) / 2,
      adaptability: (assessment.adaptability + newMetrics.adaptability) / 2
    };
  }

  private async updateUserPreferences(preferences: UserPreferences, interactions: UserInteraction[]): Promise<UserPreferences> {
    // 分析新的偏好数据
    const newStylePreferences = this.analyzeStylePreferences(interactions);
    const newInterests = this.analyzeInterests(interactions);
    const newWorkingHours = this.analyzeWorkingHours(interactions);
    const newCollaborationStyle = this.analyzeCollaborationStyle(interactions);

    // 合并偏好（保留历史偏好，添加新发现的偏好）
    const mergedStyles = [...new Set([...preferences.preferredStyles, ...newStylePreferences])];
    const mergedInterests = [...new Set([...preferences.interests, ...newInterests])];

    return {
      preferredStyles: mergedStyles.slice(0, 10), // 限制数量
      skillLevel: preferences.skillLevel, // 技能水平通过技能评估更新
      interests: mergedInterests.slice(0, 15), // 限制数量
      workingHours: newWorkingHours.start !== 9 || newWorkingHours.end !== 17 ? newWorkingHours : preferences.workingHours,
      collaborationStyle: newCollaborationStyle !== CollaborationStyle.INDEPENDENT ? newCollaborationStyle : preferences.collaborationStyle
    };
  }

  private async updateLearningProgress(userId: string, progress: any): Promise<any> {
    // 获取最新的学习进度数据
    const newProgress = await this.getLearningProgress(userId);

    // 合并学习进度
    const mergedCompletedCourses = [...new Set([
      ...(progress.completedCourses || []),
      ...(newProgress.completedCourses || [])
    ])];

    const mergedCurrentGoals = [...new Set([
      ...(progress.currentGoals || []),
      ...(newProgress.currentGoals || [])
    ])];

    return {
      completedCourses: mergedCompletedCourses,
      currentGoals: mergedCurrentGoals.slice(0, 5), // 限制目标数量
      weakAreas: newProgress.weakAreas || progress.weakAreas || [],
      strongAreas: newProgress.strongAreas || progress.strongAreas || [],
      lastActivity: newProgress.lastActivity || new Date()
    };
  }

  /**
   * 分析风格偏好
   */
  private analyzeStylePreferences(interactions: UserInteraction[]): string[] {
    const styleKeywords = new Map<string, number>();

    interactions.forEach(interaction => {
      const context = interaction.context;
      if (context) {
        // 从上下文中提取风格关键词
        if (context.style) {
          styleKeywords.set(context.style, (styleKeywords.get(context.style) || 0) + 1);
        }
        if (context.theme) {
          styleKeywords.set(context.theme, (styleKeywords.get(context.theme) || 0) + 1);
        }
        if (context.category) {
          styleKeywords.set(context.category, (styleKeywords.get(context.category) || 0) + 1);
        }
      }

      // 从操作中推断风格偏好
      const action = interaction.action.toLowerCase();
      if (action.includes('realistic')) {
        styleKeywords.set('realistic', (styleKeywords.get('realistic') || 0) + 1);
      } else if (action.includes('cartoon') || action.includes('stylized')) {
        styleKeywords.set('stylized', (styleKeywords.get('stylized') || 0) + 1);
      } else if (action.includes('minimal')) {
        styleKeywords.set('minimalist', (styleKeywords.get('minimalist') || 0) + 1);
      }
    });

    return Array.from(styleKeywords.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([style]) => style);
  }

  /**
   * 分析兴趣领域
   */
  private analyzeInterests(interactions: UserInteraction[]): string[] {
    const interestCounts = new Map<string, number>();

    interactions.forEach(interaction => {
      const action = interaction.action.toLowerCase();

      // 基于操作类型推断兴趣
      if (action.includes('game') || action.includes('gaming')) {
        interestCounts.set('gaming', (interestCounts.get('gaming') || 0) + 1);
      } else if (action.includes('architecture') || action.includes('building')) {
        interestCounts.set('architecture', (interestCounts.get('architecture') || 0) + 1);
      } else if (action.includes('character') || action.includes('avatar')) {
        interestCounts.set('character_design', (interestCounts.get('character_design') || 0) + 1);
      } else if (action.includes('environment') || action.includes('landscape')) {
        interestCounts.set('environment_design', (interestCounts.get('environment_design') || 0) + 1);
      } else if (action.includes('vehicle') || action.includes('car')) {
        interestCounts.set('vehicle_design', (interestCounts.get('vehicle_design') || 0) + 1);
      } else if (action.includes('education') || action.includes('learn')) {
        interestCounts.set('education', (interestCounts.get('education') || 0) + 1);
      } else if (action.includes('art') || action.includes('creative')) {
        interestCounts.set('digital_art', (interestCounts.get('digital_art') || 0) + 1);
      }

      // 从上下文中提取兴趣
      if (interaction.context?.category) {
        const category = interaction.context.category;
        interestCounts.set(category, (interestCounts.get(category) || 0) + 1);
      }
    });

    return Array.from(interestCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 8)
      .map(([interest]) => interest);
  }

  /**
   * 分析工作时间偏好
   */
  private analyzeWorkingHours(interactions: UserInteraction[]): { start: number; end: number } {
    const hourCounts = new Array(24).fill(0);

    interactions.forEach(interaction => {
      const hour = interaction.timestamp.getHours();
      hourCounts[hour]++;
    });

    // 找到活跃时间段
    let maxCount = 0;
    let startHour = 9;
    let endHour = 17;

    // 寻找连续的活跃时间段
    for (let start = 0; start < 24; start++) {
      for (let duration = 4; duration <= 12; duration++) {
        let totalCount = 0;
        for (let i = 0; i < duration; i++) {
          totalCount += hourCounts[(start + i) % 24];
        }

        if (totalCount > maxCount) {
          maxCount = totalCount;
          startHour = start;
          endHour = (start + duration) % 24;
        }
      }
    }

    return { start: startHour, end: endHour };
  }

  /**
   * 分析协作风格
   */
  private analyzeCollaborationStyle(interactions: UserInteraction[]): CollaborationStyle {
    const collaborationActions = interactions.filter(i =>
      i.action.includes('collaborate') ||
      i.action.includes('share') ||
      i.action.includes('invite') ||
      i.action.includes('help') ||
      i.action.includes('lead')
    );

    if (collaborationActions.length === 0) {
      return CollaborationStyle.INDEPENDENT;
    }

    const styleScores = {
      [CollaborationStyle.LEADING]: 0,
      [CollaborationStyle.MENTORING]: 0,
      [CollaborationStyle.COLLABORATIVE]: 0,
      [CollaborationStyle.INDEPENDENT]: 0
    };

    collaborationActions.forEach(interaction => {
      const action = interaction.action.toLowerCase();

      if (action.includes('lead') || action.includes('manage')) {
        styleScores[CollaborationStyle.LEADING]++;
      } else if (action.includes('help') || action.includes('mentor') || action.includes('teach')) {
        styleScores[CollaborationStyle.MENTORING]++;
      } else if (action.includes('collaborate') || action.includes('share')) {
        styleScores[CollaborationStyle.COLLABORATIVE]++;
      }
    });

    // 如果协作活动较少，倾向于独立工作
    const totalCollaboration = Object.values(styleScores).reduce((sum, score) => sum + score, 0);
    if (totalCollaboration < interactions.length * 0.1) {
      return CollaborationStyle.INDEPENDENT;
    }

    // 返回得分最高的协作风格
    return Object.entries(styleScores).reduce((max, [style, score]) =>
      score > max.score ? { style: style as CollaborationStyle, score } : max,
      { style: CollaborationStyle.INDEPENDENT, score: 0 }
    ).style;
  }

  /**
   * 更新用户行为模式
   */
  public async updateBehaviorPattern(userId: string, interaction: UserInteraction): Promise<void> {
    // 添加新的交互记录
    if (!this.userInteractions.has(userId)) {
      this.userInteractions.set(userId, []);
    }
    
    const interactions = this.userInteractions.get(userId)!;
    interactions.push(interaction);

    // 保持最近1000条记录
    if (interactions.length > 1000) {
      interactions.shift();
    }

    // 触发画像更新
    if (this.userProfiles.has(userId)) {
      await this.updateUserProfile(userId);
    }
  }

  /**
   * 从反馈更新用户画像
   */
  public async updateUserProfileFromFeedback(userId: string, feedback: UserFeedback): Promise<void> {
    try {
      const profile = this.userProfiles.get(userId);
      if (!profile) {
        console.warn(`用户画像不存在: ${userId}`);
        return;
      }

      // 基于反馈调整用户偏好
      if (feedback.rating >= 4) {
        // 正面反馈，增强相关偏好
        await this.reinforcePreferences(userId, feedback);
      } else if (feedback.rating <= 2) {
        // 负面反馈，调整偏好
        await this.adjustPreferences(userId, feedback);
      }

      // 更新学习速度和适应性
      const updatedSkills = { ...profile.skillAssessment };
      if (feedback.rating >= 4) {
        updatedSkills.adaptability = Math.min(1.0, updatedSkills.adaptability + 0.05);
      } else if (feedback.rating <= 2) {
        updatedSkills.adaptability = Math.max(0.0, updatedSkills.adaptability - 0.02);
      }

      // 更新用户画像
      const updatedProfile = {
        ...profile,
        skillAssessment: updatedSkills
      };
      this.userProfiles.set(userId, updatedProfile);

      // 触发反馈处理事件
      this.eventEmitter.emit('feedback.processed', {
        userId,
        rating: feedback.rating,
        adjustmentMade: true
      });

      if (this.config.debug) {
        console.log(`基于反馈更新用户画像: ${userId}, 评分: ${feedback.rating}`);
      }
    } catch (error) {
      console.error('反馈处理失败:', error);
      this.eventEmitter.emit('feedback.error', { userId, error });
    }
  }

  /**
   * 情感分析
   */
  public async analyzeEmotionalState(userId: string, interactions: UserInteraction[]): Promise<EmotionalState[]> {
    if (!this.config.enableEmotionAnalysis) {
      return [];
    }

    const emotionalStates: EmotionalState[] = [];

    interactions.forEach(interaction => {
      const emotion = this.detectEmotion(interaction);
      if (emotion) {
        emotionalStates.push(emotion);
      }
    });

    // 缓存情感状态
    this.emotionalStates.set(userId, emotionalStates);

    return emotionalStates;
  }

  /**
   * 异常行为检测
   */
  public async detectAnomalies(userId: string): Promise<AnomalyDetection[]> {
    if (!this.config.enableAnomalyDetection) {
      return [];
    }

    const interactions = this.userInteractions.get(userId) || [];
    const anomalies: AnomalyDetection[] = [];

    // 检测异常活动模式
    const activityAnomaly = this.detectActivityAnomaly(interactions);
    if (activityAnomaly) {
      anomalies.push(activityAnomaly);
    }

    // 检测异常时间模式
    const timeAnomaly = this.detectTimeAnomaly(interactions);
    if (timeAnomaly) {
      anomalies.push(timeAnomaly);
    }

    // 检测性能异常
    const performanceAnomaly = this.detectPerformanceAnomaly(interactions);
    if (performanceAnomaly) {
      anomalies.push(performanceAnomaly);
    }

    // 缓存异常检测结果
    this.anomalies.set(userId, anomalies);

    return anomalies;
  }

  /**
   * 学习路径分析
   */
  public async analyzeLearningPath(userId: string): Promise<LearningPathAnalysis> {
    if (!this.config.enableLearningPathAnalysis) {
      return {
        currentPath: [],
        completionRate: 0,
        estimatedTimeToComplete: 0,
        recommendedNextSteps: [],
        difficultyProgression: [],
        stuckPoints: []
      };
    }

    const interactions = this.userInteractions.get(userId) || [];
    const profile = this.userProfiles.get(userId);

    if (!profile) {
      throw new Error(`用户画像不存在: ${userId}`);
    }

    const learningActivities = interactions.filter(i =>
      i.action.includes('learn') ||
      i.action.includes('tutorial') ||
      i.action.includes('course')
    );

    const currentPath = this.extractLearningPath(learningActivities);
    const completionRate = this.calculateCompletionRate(learningActivities);
    const stuckPoints = this.identifyStuckPoints(learningActivities);
    const recommendedNextSteps = this.recommendNextSteps(profile, currentPath);

    const analysis: LearningPathAnalysis = {
      currentPath,
      completionRate,
      estimatedTimeToComplete: this.estimateTimeToComplete(profile, currentPath),
      recommendedNextSteps,
      difficultyProgression: this.analyzeDifficultyProgression(learningActivities),
      stuckPoints
    };

    // 缓存学习路径分析
    this.learningPaths.set(userId, analysis);

    return analysis;
  }

  /**
   * 获取分析统计
   */
  public getAnalysisStats(): AnalysisStats {
    return { ...this.analysisStats };
  }

  /**
   * 销毁分析器
   */
  public destroy(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = undefined;
    }

    // 清理缓存
    this.userProfiles.clear();
    this.userInteractions.clear();
    this.behaviorPatterns.clear();
    this.emotionalStates.clear();
    this.anomalies.clear();
    this.learningPaths.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('用户行为分析器已销毁');
    }
  }

  // 私有辅助方法
  private async reinforcePreferences(userId: string, feedback: UserFeedback): Promise<void> {
    // 实现偏好强化逻辑
    if (this.config.debug) {
      console.log(`强化用户偏好: ${userId}`);
    }
  }

  private async adjustPreferences(userId: string, feedback: UserFeedback): Promise<void> {
    // 实现偏好调整逻辑
    if (this.config.debug) {
      console.log(`调整用户偏好: ${userId}`);
    }
  }

  private detectEmotion(interaction: UserInteraction): EmotionalState | null {
    // 简化的情感检测逻辑
    const action = interaction.action.toLowerCase();
    let emotion = '';
    let intensity = 0.5;

    if (action.includes('error') || action.includes('fail')) {
      emotion = 'frustration';
      intensity = 0.7;
    } else if (action.includes('complete') || action.includes('success')) {
      emotion = 'satisfaction';
      intensity = 0.8;
    } else if (action.includes('help') || action.includes('stuck')) {
      emotion = 'confusion';
      intensity = 0.6;
    } else if (action.includes('explore') || action.includes('discover')) {
      emotion = 'curiosity';
      intensity = 0.7;
    }

    if (emotion) {
      return {
        emotion,
        intensity,
        confidence: 0.6,
        timestamp: interaction.timestamp,
        context: [interaction.action]
      };
    }

    return null;
  }

  private detectActivityAnomaly(interactions: UserInteraction[]): AnomalyDetection | null {
    // 检测活动频率异常
    const recentInteractions = interactions.filter(
      i => Date.now() - i.timestamp.getTime() < 24 * 60 * 60 * 1000
    );

    const averageDaily = interactions.length / this.config.analysisWindow!;
    const todayCount = recentInteractions.length;

    if (todayCount > averageDaily * 3) {
      return {
        type: 'high_activity',
        severity: 'medium',
        description: '用户活动频率异常高',
        timestamp: new Date(),
        affectedMetrics: ['activity_frequency'],
        suggestedActions: ['监控用户状态', '提供休息建议']
      };
    } else if (todayCount < averageDaily * 0.3 && averageDaily > 5) {
      return {
        type: 'low_activity',
        severity: 'low',
        description: '用户活动频率异常低',
        timestamp: new Date(),
        affectedMetrics: ['activity_frequency'],
        suggestedActions: ['发送激励消息', '推荐感兴趣的内容']
      };
    }

    return null;
  }

  private detectTimeAnomaly(interactions: UserInteraction[]): AnomalyDetection | null {
    // 检测时间模式异常
    const recentInteractions = interactions.filter(
      i => Date.now() - i.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000
    );

    const nightTimeInteractions = recentInteractions.filter(
      i => i.timestamp.getHours() >= 23 || i.timestamp.getHours() <= 5
    );

    if (nightTimeInteractions.length > recentInteractions.length * 0.3) {
      return {
        type: 'unusual_time_pattern',
        severity: 'low',
        description: '用户在非常规时间活跃',
        timestamp: new Date(),
        affectedMetrics: ['time_pattern'],
        suggestedActions: ['关注用户健康', '提供时间管理建议']
      };
    }

    return null;
  }

  private detectPerformanceAnomaly(interactions: UserInteraction[]): AnomalyDetection | null {
    // 检测性能异常
    const errorInteractions = interactions.filter(
      i => i.context?.errors && i.context.errors > 0
    );

    const errorRate = errorInteractions.length / interactions.length;

    if (errorRate > 0.3) {
      return {
        type: 'high_error_rate',
        severity: 'high',
        description: '用户错误率异常高',
        timestamp: new Date(),
        affectedMetrics: ['error_rate', 'performance'],
        suggestedActions: ['提供额外帮助', '简化操作流程', '推荐基础教程']
      };
    }

    return null;
  }

  private extractLearningPath(learningActivities: UserInteraction[]): string[] {
    // 提取学习路径
    return learningActivities
      .map(activity => activity.context?.courseId || activity.action)
      .filter((value, index, self) => self.indexOf(value) === index)
      .slice(0, 10);
  }

  private calculateCompletionRate(learningActivities: UserInteraction[]): number {
    const completedActivities = learningActivities.filter(
      activity => activity.action.includes('complete')
    );

    return learningActivities.length > 0 ?
      completedActivities.length / learningActivities.length : 0;
  }

  private identifyStuckPoints(learningActivities: UserInteraction[]): string[] {
    const stuckPoints: string[] = [];
    const activityCounts = new Map<string, number>();

    learningActivities.forEach(activity => {
      const key = activity.context?.courseId || activity.action;
      activityCounts.set(key, (activityCounts.get(key) || 0) + 1);
    });

    // 识别重复次数过多的活动作为卡点
    activityCounts.forEach((count, activity) => {
      if (count > 5 && !activity.includes('complete')) {
        stuckPoints.push(activity);
      }
    });

    return stuckPoints;
  }

  private recommendNextSteps(profile: UserProfile, currentPath: string[]): string[] {
    // 基于用户画像和当前路径推荐下一步
    const recommendations: string[] = [];

    // 基于技能水平推荐
    if (profile.skillAssessment.overallLevel === SkillLevel.BEGINNER) {
      recommendations.push('基础建模教程', '界面操作指南');
    } else if (profile.skillAssessment.overallLevel === SkillLevel.INTERMEDIATE) {
      recommendations.push('高级建模技巧', '材质制作教程');
    } else {
      recommendations.push('专业工作流程', '插件开发教程');
    }

    // 基于兴趣推荐
    profile.preferences.interests.forEach(interest => {
      if (interest === 'gaming') {
        recommendations.push('游戏资产制作');
      } else if (interest === 'architecture') {
        recommendations.push('建筑可视化');
      }
    });

    return recommendations.slice(0, 5);
  }

  private estimateTimeToComplete(profile: UserProfile, currentPath: string[]): number {
    // 估算完成时间（小时）
    const baseTime = currentPath.length * 2; // 每个步骤2小时
    const skillMultiplier = profile.skillAssessment.learningSpeed;

    return Math.ceil(baseTime / skillMultiplier);
  }

  private analyzeDifficultyProgression(learningActivities: UserInteraction[]): number[] {
    // 分析难度进展
    return learningActivities.map((activity, index) => {
      // 简化的难度计算
      const basedifficulty = 0.3;
      const progressionFactor = index / learningActivities.length;
      return Math.min(1.0, basedifficulty + progressionFactor * 0.7);
    });
  }
}
