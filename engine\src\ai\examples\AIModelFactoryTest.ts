/**
 * AI模型工厂功能测试
 * 验证修复后的AIModelFactory功能是否正常工作
 */
import { 
  AIModelFactory,
  AIModelFactoryConfig,
  ModelStatus,
  ModelCreationOptions,
  ModelRegistration
} from '../AIModelFactory';
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';

/**
 * AI模型工厂测试类
 */
export class AIModelFactoryTest {
  private factory: AIModelFactory;

  constructor() {
    // 创建工厂实例，启用所有功能
    const config: AIModelFactoryConfig = {
      debug: true,
      useLocalModels: false,
      maxCacheSize: 5,
      poolSize: 3,
      enablePerformanceMonitoring: true,
      enableAutoWarmup: true,
      warmupTimeout: 10000,
      creationTimeout: 30000,
      apiKeys: {
        'gpt': 'test-gpt-key',
        'stable-diffusion': 'test-sd-key'
      },
      baseUrls: {
        'gpt': 'https://api.openai.com',
        'stable-diffusion': 'https://api.stability.ai'
      }
    };

    this.factory = new AIModelFactory(config);
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI模型工厂功能测试开始 ===\n');

    try {
      this.testBasicModelCreation();
      await this.testAsyncModelCreation();
      this.testModelCaching();
      this.testModelRegistry();
      await this.testModelWarmup();
      this.testPerformanceMonitoring();
      this.testModelLifecycle();
      this.testErrorHandling();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    } finally {
      this.factory.dispose();
    }
  }

  /**
   * 测试基本模型创建
   */
  private testBasicModelCreation(): void {
    console.log('1. 测试基本模型创建');

    // 创建不同类型的模型
    const gptModel = this.factory.createModel(AIModelType.GPT, {
      variant: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2048
    });

    const bertModel = this.factory.createModel(AIModelType.BERT, {
      variant: 'base',
      batchSize: 32
    });

    console.log(`GPT模型创建: ${gptModel ? '成功' : '失败'}`);
    console.log(`BERT模型创建: ${bertModel ? '成功' : '失败'}`);

    if (gptModel) {
      console.log(`GPT模型ID: ${gptModel.getId()}`);
      console.log(`GPT模型类型: ${gptModel.getType()}`);
    }

    console.log('---\n');
  }

  /**
   * 测试异步模型创建
   */
  private async testAsyncModelCreation(): Promise<void> {
    console.log('2. 测试异步模型创建');

    const options: ModelCreationOptions = {
      async: true,
      timeout: 15000,
      retryCount: 2,
      retryDelay: 1000,
      warmup: true,
      priority: 5
    };

    try {
      const model = await this.factory.createModelAsync(
        AIModelType.STABLE_DIFFUSION,
        {
          variant: 'v1.5',
          useGPU: true
        },
        options
      );

      console.log(`异步模型创建: ${model ? '成功' : '失败'}`);
      if (model) {
        console.log(`模型ID: ${model.getId()}`);
      }
    } catch (error) {
      console.log(`异步模型创建失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试模型缓存
   */
  private testModelCaching(): void {
    console.log('3. 测试模型缓存');

    const config: AIModelConfig = {
      variant: 'base',
      temperature: 0.5
    };

    // 创建相同配置的模型，应该从缓存返回
    const model1 = this.factory.createModel(AIModelType.BERT, config);
    const model2 = this.factory.createModel(AIModelType.BERT, config);

    console.log(`模型缓存测试: ${model1 === model2 ? '成功（相同实例）' : '失败（不同实例）'}`);

    // 检查缓存使用情况
    const cacheUsage = this.factory.getCacheUsage();
    console.log(`缓存使用情况: ${cacheUsage.used}/${cacheUsage.total} (${cacheUsage.percentage.toFixed(1)}%)`);

    // 获取所有模型
    const allModels = this.factory.getAllModels();
    console.log(`缓存中的模型数量: ${allModels.size}`);

    console.log('---\n');
  }

  /**
   * 测试模型注册
   */
  private testModelRegistry(): void {
    console.log('4. 测试模型注册');

    // 获取支持的模型类型
    const supportedTypes = this.factory.getSupportedModelTypes();
    console.log(`支持的模型类型: ${supportedTypes.join(', ')}`);

    // 获取特定模型的注册信息
    const gptRegistration = this.factory.getModelRegistration(AIModelType.GPT);
    if (gptRegistration) {
      console.log(`GPT模型支持的变体: ${gptRegistration.supportedVariants?.join(', ')}`);
      console.log(`GPT模型需要预热: ${gptRegistration.requiresWarmup}`);
    }

    // 检查功能支持
    const supportsTextGeneration = this.factory.isFeatureSupported(AIModelType.GPT, 'textGeneration');
    console.log(`GPT支持文本生成: ${supportsTextGeneration}`);

    console.log('---\n');
  }

  /**
   * 测试模型预热
   */
  private async testModelWarmup(): Promise<void> {
    console.log('5. 测试模型预热');

    try {
      // 批量预热模型
      await this.factory.warmupModels([
        AIModelType.BERT,
        AIModelType.DISTILBERT
      ]);

      console.log('批量预热完成');

      // 检查预热状态
      const allInstances = this.factory.getAllModelInstances();
      for (const [id, instance] of allInstances) {
        console.log(`模型 ${id}: 预热状态 ${instance.isWarmedUp ? '已预热' : '未预热'}`);
      }
    } catch (error) {
      console.log(`模型预热失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试性能监控
   */
  private testPerformanceMonitoring(): void {
    console.log('6. 测试性能监控');

    // 获取性能统计
    const stats = this.factory.getPerformanceStats();
    console.log('性能统计:');
    console.log(`  总模型数: ${stats.totalModels}`);
    console.log(`  总内存使用: ${(stats.totalMemoryUsage / 1024 / 1024).toFixed(2)} MB`);
    console.log(`  平均使用次数: ${stats.averageUsageCount.toFixed(2)}`);
    console.log(`  预热模型数: ${stats.warmedUpModels}`);

    console.log('  各类型模型数量:');
    for (const [type, count] of stats.modelsByType) {
      console.log(`    ${type}: ${count}`);
    }

    console.log('---\n');
  }

  /**
   * 测试模型生命周期
   */
  private testModelLifecycle(): void {
    console.log('7. 测试模型生命周期');

    // 创建模型
    const model = this.factory.createModel(AIModelType.ALBERT, {
      variant: 'base'
    });

    if (model) {
      const modelId = model.getId();
      
      // 获取模型实例信息
      const instance = this.factory.getModelInstance(modelId);
      if (instance) {
        console.log(`模型状态: ${instance.status}`);
        console.log(`创建时间: ${new Date(instance.createdAt).toLocaleString()}`);
        console.log(`使用次数: ${instance.usageCount}`);
      }

      // 释放模型
      const released = this.factory.releaseModel(modelId);
      console.log(`模型释放: ${released ? '成功' : '失败'}`);

      // 检查模型是否还在缓存中
      const stillCached = this.factory.isModelCached(AIModelType.ALBERT, { variant: 'base' });
      console.log(`模型仍在缓存: ${stillCached ? '是' : '否'}`);
    }

    console.log('---\n');
  }

  /**
   * 测试错误处理
   */
  private testErrorHandling(): void {
    console.log('8. 测试错误处理');

    // 监听错误事件
    this.factory.addEventListener('modelCreationError', (data) => {
      console.log(`模型创建错误事件: ${data.modelType}`);
    });

    // 尝试创建不支持的模型类型
    try {
      const invalidModel = this.factory.createModel('INVALID_TYPE' as any);
      console.log(`无效模型创建: ${invalidModel ? '意外成功' : '正确失败'}`);
    } catch (error) {
      console.log(`无效模型创建正确抛出错误: ${error}`);
    }

    // 尝试获取不存在的模型
    const nonExistentModel = this.factory.getModel('non-existent-id');
    console.log(`获取不存在模型: ${nonExistentModel ? '意外成功' : '正确返回null'}`);

    // 清理未使用的模型
    const cleanedCount = this.factory.cleanupUnusedModels(0); // 立即清理
    console.log(`清理的模型数量: ${cleanedCount}`);

    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIModelFactoryTest();
  test.runAllTests().catch(console.error);
}
