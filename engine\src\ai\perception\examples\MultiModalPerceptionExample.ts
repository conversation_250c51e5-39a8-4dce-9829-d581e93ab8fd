/**
 * 多模态感知系统使用示例
 * 
 * 展示如何使用完善后的多模态感知系统
 */

import * as THREE from 'three';
import { 
  MultiModalPerceptionSystem, 
  PerceptionModality,
  PerceptionConfig 
} from '../MultiModalPerceptionSystem';

/**
 * 多模态感知系统示例
 */
export class MultiModalPerceptionExample {
  private perceptionSystem: MultiModalPerceptionSystem;
  private scene: THREE.Scene;
  private camera: THREE.Camera;

  constructor() {
    // 配置感知系统
    const config: Partial<PerceptionConfig> = {
      maxHistorySize: 500,
      updateFrequency: 50,
      enablePersistence: true,
      enablePerformanceMonitoring: true,
      confidenceThreshold: 0.4,
      fusionAlgorithm: 'weighted_average'
    };

    this.perceptionSystem = new MultiModalPerceptionSystem(config);
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.perceptionSystem.on('perceptionProcessed', (fusedData) => {
      console.log('感知数据已处理:', {
        timestamp: fusedData.timestamp,
        confidence: fusedData.confidence,
        entitiesCount: fusedData.worldModel.entities.size,
        attentionFocusCount: fusedData.attentionFocus.length,
        predictionsCount: fusedData.predictions.length,
        anomaliesCount: fusedData.anomalies.length
      });
    });
  }

  /**
   * 运行感知示例
   */
  public runExample(): void {
    console.log('=== 多模态感知系统示例 ===');
    
    // 创建模拟环境数据
    const rawData = this.createMockEnvironmentData();
    
    // 处理感知数据
    const fusedData = this.perceptionSystem.processPerception(rawData);
    
    // 显示结果
    this.displayResults(fusedData);
    
    // 显示性能指标
    this.displayPerformanceMetrics();
    
    // 显示系统状态
    this.displaySystemStatus();
    
    // 测试配置管理
    this.testConfigurationManagement();
    
    // 测试数据导出/导入
    this.testDataExportImport();
  }

  /**
   * 创建模拟环境数据
   */
  private createMockEnvironmentData(): any {
    return {
      observerPosition: new THREE.Vector3(0, 1.8, 0),
      observerDirection: new THREE.Vector3(0, 0, -1),
      viewDirection: new THREE.Vector3(0, 0, -1),
      
      // 视觉数据
      entities: [
        {
          id: 'person1',
          type: 'human',
          position: new THREE.Vector3(5, 0, -3),
          size: new THREE.Vector3(0.6, 1.8, 0.3),
          velocity: new THREE.Vector3(0.5, 0, 0),
          attributes: { age: 25, gender: 'male' }
        },
        {
          id: 'car1',
          type: 'vehicle',
          position: new THREE.Vector3(-10, 0, -5),
          size: new THREE.Vector3(4, 1.5, 2),
          velocity: new THREE.Vector3(2, 0, 0),
          attributes: { color: 'red', model: 'sedan' }
        }
      ],
      lightIntensity: 0.8,
      lightDirection: new THREE.Vector3(0.3, -0.7, -0.6),
      lightColor: new THREE.Color(1, 0.95, 0.8),
      shadows: true,
      
      // 听觉数据
      audioSources: [
        {
          id: 'engine1',
          type: 'engine',
          position: new THREE.Vector3(-10, 0, -5),
          volume: 0.6,
          frequency: 120,
          duration: 5000
        }
      ],
      speechEvents: [
        {
          speaker: 'person1',
          text: '你好，今天天气不错',
          emotion: 'happy',
          intent: 'greeting',
          confidence: 0.9
        }
      ],
      ambientNoise: 0.2,
      
      // 社交数据
      socialEntities: [
        {
          id: 'person1',
          type: 'human',
          position: new THREE.Vector3(5, 0, -3),
          relationship: 0.7,
          trustLevel: 0.8,
          emotionalState: 'happy',
          currentActivity: 'walking',
          attentionFocus: 'observer'
        }
      ],
      interactions: [
        {
          participants: ['observer', 'person1'],
          type: 'conversation',
          intensity: 0.8,
          duration: 30000,
          outcome: 'positive'
        }
      ],
      communicationEvents: [
        {
          sender: 'person1',
          receiver: 'observer',
          type: 'verbal',
          content: '你好，今天天气不错',
          timestamp: Date.now()
        }
      ],
      
      // 环境数据
      weatherType: 'sunny',
      temperature: 22,
      humidity: 0.6,
      windSpeed: 2,
      windDirection: new THREE.Vector3(1, 0, 0.3),
      precipitation: 0,
      visibility: 1000,
      terrainType: 'urban',
      elevation: 10,
      slope: 0.1,
      roughness: 0.3,
      terrainMaterial: 'concrete',
      airQuality: 0.9,
      noiseLevel: 0.3,
      hazards: [],
      resources: [
        {
          id: 'bench1',
          type: 'furniture',
          position: new THREE.Vector3(3, 0, -2),
          quantity: 1,
          quality: 0.8
        }
      ],
      
      // 触觉数据
      collisions: [],
      pressure: 0,
      temperature: 22,
      texture: 'smooth',
      vibrationFrequency: 0,
      vibrationAmplitude: 0,
      vibrationPattern: 'none',
      vibrationDuration: 0,
      
      // 时间数据
      timeFlow: 1.0,
      scheduleEvents: [
        {
          id: 'meeting1',
          name: '团队会议',
          startTime: Date.now() + 3600000,
          endTime: Date.now() + 5400000,
          priority: 0.8,
          participants: ['observer', 'person1']
        }
      ],
      temporalPatterns: [
        {
          type: 'daily_routine',
          frequency: 86400000,
          lastOccurrence: Date.now() - 86400000,
          predictedNext: Date.now() + 86400000,
          confidence: 0.9
        }
      ],
      
      // 空间数据
      spaceType: 'urban_street',
      spaceDimensions: new THREE.Vector3(50, 10, 100),
      spaceCenter: new THREE.Vector3(0, 0, 0),
      spaceOrientation: new THREE.Quaternion(),
      accessibility: 0.9,
      navigationPaths: [
        {
          id: 'sidewalk1',
          waypoints: [
            new THREE.Vector3(-20, 0, -2),
            new THREE.Vector3(0, 0, -2),
            new THREE.Vector3(20, 0, -2)
          ],
          difficulty: 0.2,
          estimatedTime: 120000
        }
      ],
      obstacles: [
        {
          id: 'pole1',
          position: new THREE.Vector3(2, 0, -1),
          size: new THREE.Vector3(0.3, 3, 0.3),
          type: 'static',
          movable: false
        }
      ],
      boundaries: [
        {
          id: 'building1',
          type: 'wall',
          points: [
            new THREE.Vector3(-25, 0, 0),
            new THREE.Vector3(25, 0, 0),
            new THREE.Vector3(25, 0, -50),
            new THREE.Vector3(-25, 0, -50)
          ],
          height: 20,
          permeability: 0
        }
      ],
      landmarks: [
        {
          id: 'fountain1',
          name: '中央喷泉',
          position: new THREE.Vector3(0, 0, -25),
          type: 'landmark',
          significance: 0.9
        }
      ]
    };
  }

  /**
   * 显示处理结果
   */
  private displayResults(fusedData: any): void {
    console.log('\n=== 感知处理结果 ===');
    console.log('时间戳:', new Date(fusedData.timestamp).toLocaleString());
    console.log('总体置信度:', fusedData.confidence.toFixed(3));
    console.log('实体数量:', fusedData.worldModel.entities.size);
    console.log('注意力焦点:', fusedData.attentionFocus.length);
    console.log('预测数量:', fusedData.predictions.length);
    console.log('异常数量:', fusedData.anomalies.length);
    
    if (fusedData.attentionFocus.length > 0) {
      console.log('\n注意力焦点详情:');
      fusedData.attentionFocus.forEach((focus: any, index: number) => {
        console.log(`  ${index + 1}. ${focus.target} (${focus.type}) - 优先级: ${focus.priority.toFixed(2)}`);
      });
    }
    
    if (fusedData.predictions.length > 0) {
      console.log('\n预测详情:');
      fusedData.predictions.forEach((prediction: any, index: number) => {
        console.log(`  ${index + 1}. ${prediction.type} - 目标: ${prediction.target} - 置信度: ${prediction.confidence.toFixed(2)}`);
      });
    }
  }

  /**
   * 显示性能指标
   */
  private displayPerformanceMetrics(): void {
    const metrics = this.perceptionSystem.getPerformanceMetrics();
    
    console.log('\n=== 性能指标 ===');
    console.log('总处理时间:', metrics.totalProcessingTime.toFixed(2), 'ms');
    console.log('平均处理时间:', metrics.averageProcessingTime.toFixed(2), 'ms');
    console.log('处理次数:', metrics.processedCount);
    console.log('错误次数:', metrics.errorCount);
    
    console.log('\n各模态性能:');
    for (const [modality, modalityMetrics] of metrics.modalityPerformance) {
      console.log(`  ${modality}:`);
      console.log(`    成功次数: ${modalityMetrics.successCount}`);
      console.log(`    错误次数: ${modalityMetrics.errorCount}`);
      console.log(`    平均置信度: ${modalityMetrics.averageConfidence.toFixed(3)}`);
      console.log(`    最后处理时间: ${modalityMetrics.lastProcessTime.toFixed(2)}ms`);
    }
  }

  /**
   * 显示系统状态
   */
  private displaySystemStatus(): void {
    const summary = this.perceptionSystem.getSystemSummary();
    
    console.log('\n=== 系统状态摘要 ===');
    console.log('处理器数量:', summary.processorsCount);
    console.log('启用的处理器:', summary.enabledProcessors.join(', '));
    console.log('历史记录大小:', summary.historySize);
    console.log('实体数量:', summary.entitiesCount);
    console.log('错误率:', (summary.errorRate * 100).toFixed(2) + '%');
    console.log('融合启用:', summary.fusionEnabled);
  }

  /**
   * 测试配置管理
   */
  private testConfigurationManagement(): void {
    console.log('\n=== 配置管理测试 ===');
    
    const originalConfig = this.perceptionSystem.getConfig();
    console.log('原始配置:', originalConfig);
    
    // 更新配置
    this.perceptionSystem.updateConfig({
      confidenceThreshold: 0.6,
      fusionAlgorithm: 'bayesian_fusion'
    });
    
    const updatedConfig = this.perceptionSystem.getConfig();
    console.log('更新后配置:', updatedConfig);
    
    // 测试处理器控制
    console.log('\n处理器状态控制:');
    const processorStatus = this.perceptionSystem.getProcessorStatus();
    console.log('当前处理器状态:', Array.from(processorStatus.entries()));
    
    // 禁用触觉处理器
    this.perceptionSystem.setProcessorEnabled(PerceptionModality.TACTILE, false);
    console.log('禁用触觉处理器后:', this.perceptionSystem.getProcessorStatus());
    
    // 重新启用
    this.perceptionSystem.setProcessorEnabled(PerceptionModality.TACTILE, true);
    console.log('重新启用后:', this.perceptionSystem.getProcessorStatus());
  }

  /**
   * 测试数据导出/导入
   */
  private testDataExportImport(): void {
    console.log('\n=== 数据导出/导入测试 ===');
    
    // 导出JSON格式
    const jsonData = this.perceptionSystem.exportPerceptionData('json');
    console.log('JSON导出数据大小:', jsonData.length, '字符');
    
    // 导出CSV格式
    const csvData = this.perceptionSystem.exportPerceptionData('csv');
    console.log('CSV导出数据大小:', csvData.length, '字符');
    console.log('CSV数据预览:', csvData.split('\n').slice(0, 3).join('\n'));
    
    // 测试导入
    const importSuccess = this.perceptionSystem.importPerceptionData(jsonData);
    console.log('数据导入成功:', importSuccess);
  }
}

// 运行示例
if (require.main === module) {
  const example = new MultiModalPerceptionExample();
  example.runExample();
}
