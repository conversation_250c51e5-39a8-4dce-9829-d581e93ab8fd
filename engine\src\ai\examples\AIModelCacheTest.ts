/**
 * AI模型缓存功能测试
 * 验证修复后的AIModelCache功能是否正常工作
 */
import { 
  AIModelCache, 
  AIModelCacheConfig,
  CacheEvictionPolicy,
  CacheStorageType 
} from '../AIModelCache';
import { AIModelType } from '../AIModelType';

/**
 * AI模型缓存测试类
 */
export class AIModelCacheTest {
  private cache: AIModelCache<any>;

  constructor() {
    // 创建缓存实例，启用所有功能
    const config: AIModelCacheConfig = {
      maxSize: 10,
      expireTime: 5000, // 5秒
      debug: true,
      enableCompression: true,
      compressionThreshold: 100,
      enablePersistence: true,
      storageType: CacheStorageType.LOCAL_STORAGE,
      persistenceKeyPrefix: 'test_cache_',
      enablePrewarm: true,
      prewarmData: [
        { key: 'prewarmed_key', value: 'prewarmed_value' }
      ],
      maxMemoryUsage: 1024 * 1024, // 1MB
      evictionPolicy: CacheEvictionPolicy.LRU
    };

    this.cache = new AIModelCache(config);
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI模型缓存功能测试开始 ===\n');

    try {
      this.testBasicOperations();
      this.testCompression();
      this.testPersistence();
      this.testMemoryManagement();
      this.testBatchOperations();
      this.testEvictionPolicies();
      this.testEvents();
      this.testStatistics();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    } finally {
      this.cache.dispose();
    }
  }

  /**
   * 测试基本操作
   */
  private testBasicOperations(): void {
    console.log('1. 测试基本操作');

    // 测试键生成
    const key = this.cache.generateKey(AIModelType.TEXT_GENERATION, 'test input', { temperature: 0.7 });
    console.log(`生成的键: ${key}`);

    // 测试设置和获取
    this.cache.set('test_key', 'test_value');
    const value = this.cache.get('test_key');
    console.log(`设置和获取: ${value}`);

    // 测试过期时间
    this.cache.set('expire_key', 'expire_value', 1000); // 1秒后过期
    console.log(`设置过期项: ${this.cache.get('expire_key')}`);

    // 测试优先级
    this.cache.set('high_priority', 'important_data', undefined, 10);
    this.cache.set('low_priority', 'less_important', undefined, 1);

    console.log(`缓存大小: ${this.cache.size()}`);
    console.log('---\n');
  }

  /**
   * 测试压缩功能
   */
  private testCompression(): void {
    console.log('2. 测试压缩功能');

    // 创建大数据对象
    const largeData = {
      text: 'A'.repeat(200), // 超过压缩阈值
      numbers: Array.from({ length: 50 }, (_, i) => i),
      nested: {
        data: 'B'.repeat(100)
      }
    };

    this.cache.set('large_data', largeData);
    const retrieved = this.cache.get('large_data');
    
    console.log(`压缩测试 - 原始数据长度: ${JSON.stringify(largeData).length}`);
    console.log(`压缩测试 - 检索成功: ${JSON.stringify(retrieved) === JSON.stringify(largeData)}`);
    console.log('---\n');
  }

  /**
   * 测试持久化功能
   */
  private testPersistence(): void {
    console.log('3. 测试持久化功能');

    // 设置持久化数据
    this.cache.set('persistent_key', 'persistent_value');
    
    // 检查LocalStorage
    if (typeof localStorage !== 'undefined') {
      const persistedData = localStorage.getItem('test_cache_persistent_key');
      console.log(`持久化数据存在: ${persistedData !== null}`);
      
      if (persistedData) {
        const parsed = JSON.parse(persistedData);
        console.log(`持久化数据值: ${parsed.value}`);
      }
    }
    
    console.log('---\n');
  }

  /**
   * 测试内存管理
   */
  private testMemoryManagement(): void {
    console.log('4. 测试内存管理');

    const stats = this.cache.getEnhancedStats();
    console.log(`当前内存使用: ${stats.memoryUsage} 字节`);
    console.log(`内存使用百分比: ${stats.memoryUsagePercentage.toFixed(2)}%`);
    console.log(`平均项大小: ${stats.averageItemSize.toFixed(2)} 字节`);

    // 添加大量数据测试内存限制
    for (let i = 0; i < 20; i++) {
      const data = 'X'.repeat(1000); // 1KB数据
      this.cache.set(`memory_test_${i}`, data);
    }

    const newStats = this.cache.getEnhancedStats();
    console.log(`添加数据后内存使用: ${newStats.memoryUsage} 字节`);
    console.log(`缓存项数量: ${newStats.size}`);
    
    console.log('---\n');
  }

  /**
   * 测试批量操作
   */
  private testBatchOperations(): void {
    console.log('5. 测试批量操作');

    // 批量设置
    const batchItems = [
      { key: 'batch_1', value: 'value_1' },
      { key: 'batch_2', value: 'value_2', priority: 5 },
      { key: 'batch_3', value: 'value_3', expireTime: 10000 }
    ];

    const setCount = this.cache.setBatch(batchItems);
    console.log(`批量设置成功: ${setCount}/${batchItems.length}`);

    // 批量获取
    const keys = ['batch_1', 'batch_2', 'batch_3', 'nonexistent'];
    const results = this.cache.getBatch(keys);
    
    console.log('批量获取结果:');
    for (const [key, value] of results) {
      console.log(`  ${key}: ${value}`);
    }
    
    console.log('---\n');
  }

  /**
   * 测试驱逐策略
   */
  private testEvictionPolicies(): void {
    console.log('6. 测试驱逐策略');

    // 填满缓存
    for (let i = 0; i < 15; i++) {
      this.cache.set(`evict_test_${i}`, `value_${i}`, undefined, i % 3); // 不同优先级
    }

    const stats = this.cache.getStats();
    console.log(`驱逐后缓存大小: ${stats.size}`);
    console.log(`总驱逐次数: ${stats.evictions}`);

    // 检查哪些项被保留
    console.log('保留的项:');
    for (let i = 0; i < 15; i++) {
      const value = this.cache.get(`evict_test_${i}`);
      if (value) {
        console.log(`  evict_test_${i}: ${value}`);
      }
    }
    
    console.log('---\n');
  }

  /**
   * 测试事件系统
   */
  private testEvents(): void {
    console.log('7. 测试事件系统');

    // 监听事件
    this.cache.addEventListener('itemSet', (data) => {
      console.log(`事件 - 项已设置: ${data.key}, 大小: ${data.size}`);
    });

    this.cache.addEventListener('memoryLimitExceeded', (data) => {
      console.log(`事件 - 内存限制超出: ${data.currentUsage}/${data.maxUsage}`);
    });

    this.cache.addEventListener('batchSet', (data) => {
      console.log(`事件 - 批量设置: ${data.success}/${data.total}`);
    });

    // 触发事件
    this.cache.set('event_test', 'event_value');
    
    console.log('---\n');
  }

  /**
   * 测试统计信息
   */
  private testStatistics(): void {
    console.log('8. 测试统计信息');

    // 执行一些操作来生成统计数据
    this.cache.get('existing_key');
    this.cache.get('nonexistent_key');
    this.cache.get('another_nonexistent');

    const stats = this.cache.getStats();
    console.log('基本统计:');
    console.log(`  缓存大小: ${stats.size}/${stats.maxSize}`);
    console.log(`  命中次数: ${stats.hits}`);
    console.log(`  未命中次数: ${stats.misses}`);
    console.log(`  命中率: ${(stats.hitRate * 100).toFixed(2)}%`);
    console.log(`  过期次数: ${stats.expirations}`);
    console.log(`  驱逐次数: ${stats.evictions}`);

    const enhancedStats = this.cache.getEnhancedStats();
    console.log('\n增强统计:');
    console.log(`  内存使用: ${enhancedStats.memoryUsage} 字节`);
    console.log(`  内存使用率: ${enhancedStats.memoryUsagePercentage.toFixed(2)}%`);
    console.log(`  平均项大小: ${enhancedStats.averageItemSize.toFixed(2)} 字节`);
    console.log(`  压缩启用: ${enhancedStats.compressionEnabled}`);
    console.log(`  持久化启用: ${enhancedStats.persistenceEnabled}`);
    console.log(`  驱逐策略: ${enhancedStats.evictionPolicy}`);
    console.log(`  存储类型: ${enhancedStats.storageType}`);
    
    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIModelCacheTest();
  test.runAllTests().catch(console.error);
}
