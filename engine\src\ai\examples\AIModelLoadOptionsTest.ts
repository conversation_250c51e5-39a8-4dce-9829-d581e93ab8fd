/**
 * AI模型加载选项功能测试
 * 验证修复后的AIModelLoadOptions功能是否正常工作
 */
import { 
  AIModelLoadOptions,
  LoadStrategy,
  DeviceType,
  ModelFormat,
  QuantizationType,
  LoadEnvironment,
  LoadOptionsValidator,
  LoadOptionsBuilder,
  LoadOptionsPresets,
  ResourceConstraints,
  PerformanceOptions,
  SecurityOptions,
  MonitoringOptions,
  FallbackOptions
} from '../AIModelLoadOptions';

/**
 * AI模型加载选项测试类
 */
export class AIModelLoadOptionsTest {
  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI模型加载选项功能测试开始 ===\n');

    try {
      this.testBasicOptions();
      this.testEnumValues();
      this.testResourceConstraints();
      this.testPerformanceOptions();
      this.testSecurityOptions();
      this.testMonitoringOptions();
      this.testOptionsValidator();
      this.testOptionsBuilder();
      this.testPresetOptions();
      this.testCallbacks();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    }
  }

  /**
   * 测试基本选项
   */
  private testBasicOptions(): void {
    console.log('1. 测试基本选项');

    const basicOptions: AIModelLoadOptions = {
      forceReload: true,
      timeout: 30000,
      priority: 8,
      loadStrategy: LoadStrategy.EAGER,
      deviceType: DeviceType.GPU,
      modelFormat: ModelFormat.ONNX,
      environment: LoadEnvironment.PRODUCTION,
      useCache: true,
      cacheKey: 'test-model-v1',
      cacheExpiry: 3600000,
      modelVersion: '1.0.0',
      modelVariant: 'optimized'
    };

    console.log('基本选项配置:');
    console.log(`  强制重载: ${basicOptions.forceReload}`);
    console.log(`  超时时间: ${basicOptions.timeout}ms`);
    console.log(`  优先级: ${basicOptions.priority}`);
    console.log(`  加载策略: ${basicOptions.loadStrategy}`);
    console.log(`  设备类型: ${basicOptions.deviceType}`);
    console.log(`  模型格式: ${basicOptions.modelFormat}`);
    console.log(`  环境: ${basicOptions.environment}`);

    console.log('---\n');
  }

  /**
   * 测试枚举值
   */
  private testEnumValues(): void {
    console.log('2. 测试枚举值');

    console.log('加载策略:');
    Object.values(LoadStrategy).forEach(strategy => {
      console.log(`  - ${strategy}`);
    });

    console.log('设备类型:');
    Object.values(DeviceType).forEach(device => {
      console.log(`  - ${device}`);
    });

    console.log('模型格式:');
    Object.values(ModelFormat).forEach(format => {
      console.log(`  - ${format}`);
    });

    console.log('量化类型:');
    Object.values(QuantizationType).forEach(type => {
      console.log(`  - ${type}`);
    });

    console.log('---\n');
  }

  /**
   * 测试资源约束
   */
  private testResourceConstraints(): void {
    console.log('3. 测试资源约束');

    const constraints: ResourceConstraints = {
      maxMemory: 2 * 1024 * 1024 * 1024, // 2GB
      maxGpuMemory: 1 * 1024 * 1024 * 1024, // 1GB
      maxCpuUsage: 0.8,
      maxLoadTime: 60000,
      maxDiskUsage: 500 * 1024 * 1024 // 500MB
    };

    console.log('资源约束配置:');
    console.log(`  最大内存: ${(constraints.maxMemory! / 1024 / 1024 / 1024).toFixed(1)}GB`);
    console.log(`  最大GPU内存: ${(constraints.maxGpuMemory! / 1024 / 1024 / 1024).toFixed(1)}GB`);
    console.log(`  最大CPU使用率: ${(constraints.maxCpuUsage! * 100).toFixed(0)}%`);
    console.log(`  最大加载时间: ${constraints.maxLoadTime}ms`);
    console.log(`  最大磁盘使用: ${(constraints.maxDiskUsage! / 1024 / 1024).toFixed(0)}MB`);

    console.log('---\n');
  }

  /**
   * 测试性能选项
   */
  private testPerformanceOptions(): void {
    console.log('4. 测试性能选项');

    const perfOptions: PerformanceOptions = {
      batchSize: 32,
      enableParallelLoading: true,
      parallelThreads: 4,
      enableMemoryMapping: true,
      enablePrefetch: true,
      prefetchBufferSize: 1024 * 1024, // 1MB
      quantizationType: QuantizationType.DYNAMIC,
      quantizationBits: 16
    };

    console.log('性能选项配置:');
    console.log(`  批处理大小: ${perfOptions.batchSize}`);
    console.log(`  并行加载: ${perfOptions.enableParallelLoading}`);
    console.log(`  并行线程数: ${perfOptions.parallelThreads}`);
    console.log(`  内存映射: ${perfOptions.enableMemoryMapping}`);
    console.log(`  预取: ${perfOptions.enablePrefetch}`);
    console.log(`  量化类型: ${perfOptions.quantizationType}`);
    console.log(`  量化位数: ${perfOptions.quantizationBits}`);

    console.log('---\n');
  }

  /**
   * 测试安全选项
   */
  private testSecurityOptions(): void {
    console.log('5. 测试安全选项');

    const securityOptions: SecurityOptions = {
      verifySignature: true,
      modelSignature: 'sha256:abc123...',
      checkIntegrity: true,
      modelHash: 'md5:def456...',
      enableSandbox: true,
      allowedOperations: ['inference', 'preprocessing']
    };

    console.log('安全选项配置:');
    console.log(`  验证签名: ${securityOptions.verifySignature}`);
    console.log(`  检查完整性: ${securityOptions.checkIntegrity}`);
    console.log(`  沙箱模式: ${securityOptions.enableSandbox}`);
    console.log(`  允许操作: ${securityOptions.allowedOperations?.join(', ')}`);

    console.log('---\n');
  }

  /**
   * 测试监控选项
   */
  private testMonitoringOptions(): void {
    console.log('6. 测试监控选项');

    const monitoringOptions: MonitoringOptions = {
      enablePerformanceMonitoring: true,
      enableMemoryMonitoring: true,
      enableErrorMonitoring: true,
      monitoringInterval: 5000,
      enableDetailedLogging: true,
      logLevel: 'info'
    };

    console.log('监控选项配置:');
    console.log(`  性能监控: ${monitoringOptions.enablePerformanceMonitoring}`);
    console.log(`  内存监控: ${monitoringOptions.enableMemoryMonitoring}`);
    console.log(`  错误监控: ${monitoringOptions.enableErrorMonitoring}`);
    console.log(`  监控间隔: ${monitoringOptions.monitoringInterval}ms`);
    console.log(`  详细日志: ${monitoringOptions.enableDetailedLogging}`);
    console.log(`  日志级别: ${monitoringOptions.logLevel}`);

    console.log('---\n');
  }

  /**
   * 测试选项验证器
   */
  private testOptionsValidator(): void {
    console.log('7. 测试选项验证器');

    // 有效选项
    const validOptions: AIModelLoadOptions = {
      timeout: 30000,
      priority: 5,
      resourceConstraints: {
        maxMemory: 1024 * 1024 * 1024,
        maxCpuUsage: 0.8
      },
      performanceOptions: {
        batchSize: 16,
        parallelThreads: 2
      }
    };

    const validResult = LoadOptionsValidator.validate(validOptions);
    console.log(`有效选项验证: ${validResult.valid ? '通过' : '失败'}`);

    // 无效选项
    const invalidOptions: AIModelLoadOptions = {
      timeout: -1000, // 无效
      priority: 15, // 超出范围
      resourceConstraints: {
        maxMemory: -100, // 无效
        maxCpuUsage: 1.5 // 超出范围
      },
      performanceOptions: {
        batchSize: 0, // 无效
        parallelThreads: -1 // 无效
      }
    };

    const invalidResult = LoadOptionsValidator.validate(invalidOptions);
    console.log(`无效选项验证: ${invalidResult.valid ? '意外通过' : '正确失败'}`);
    if (!invalidResult.valid) {
      console.log('验证错误:');
      invalidResult.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('---\n');
  }

  /**
   * 测试选项构建器
   */
  private testOptionsBuilder(): void {
    console.log('8. 测试选项构建器');

    try {
      const options = new LoadOptionsBuilder()
        .basic({
          timeout: 45000,
          priority: 7,
          useCache: true
        })
        .performance({
          deviceType: DeviceType.GPU,
          loadStrategy: LoadStrategy.PRELOAD,
          performanceOptions: {
            batchSize: 64,
            enableParallelLoading: true
          }
        })
        .resources({
          maxMemory: 4 * 1024 * 1024 * 1024,
          maxCpuUsage: 0.9
        })
        .security({
          verifySignature: true,
          checkIntegrity: true
        })
        .monitoring({
          enablePerformanceMonitoring: true,
          logLevel: 'warn'
        })
        .build();

      console.log('构建器创建的选项:');
      console.log(`  超时时间: ${options.timeout}ms`);
      console.log(`  优先级: ${options.priority}`);
      console.log(`  设备类型: ${options.deviceType}`);
      console.log(`  加载策略: ${options.loadStrategy}`);
      console.log(`  验证签名: ${options.securityOptions?.verifySignature}`);
      console.log(`  性能监控: ${options.monitoringOptions?.enablePerformanceMonitoring}`);
    } catch (error) {
      console.log(`构建器测试失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试预设选项
   */
  private testPresetOptions(): void {
    console.log('9. 测试预设选项');

    // 开发环境预设
    const devOptions = LoadOptionsPresets.development();
    console.log('开发环境预设:');
    console.log(`  超时时间: ${devOptions.timeout}ms`);
    console.log(`  设备类型: ${devOptions.deviceType}`);
    console.log(`  加载策略: ${devOptions.loadStrategy}`);
    console.log(`  日志级别: ${devOptions.monitoringOptions?.logLevel}`);

    // 生产环境预设
    const prodOptions = LoadOptionsPresets.production();
    console.log('\n生产环境预设:');
    console.log(`  超时时间: ${prodOptions.timeout}ms`);
    console.log(`  设备类型: ${prodOptions.deviceType}`);
    console.log(`  加载策略: ${prodOptions.loadStrategy}`);
    console.log(`  验证签名: ${prodOptions.securityOptions?.verifySignature}`);
    console.log(`  回退启用: ${prodOptions.fallbackOptions?.enabled}`);

    // 边缘计算预设
    const edgeOptions = LoadOptionsPresets.edge();
    console.log('\n边缘计算预设:');
    console.log(`  超时时间: ${edgeOptions.timeout}ms`);
    console.log(`  设备类型: ${edgeOptions.deviceType}`);
    console.log(`  低精度模式: ${edgeOptions.useLowPrecision}`);
    console.log(`  量化类型: ${edgeOptions.performanceOptions?.quantizationType}`);

    // 高性能预设
    const highPerfOptions = LoadOptionsPresets.highPerformance();
    console.log('\n高性能预设:');
    console.log(`  设备类型: ${highPerfOptions.deviceType}`);
    console.log(`  并行加载: ${highPerfOptions.performanceOptions?.enableParallelLoading}`);
    console.log(`  并行线程: ${highPerfOptions.performanceOptions?.parallelThreads}`);
    console.log(`  批处理大小: ${highPerfOptions.performanceOptions?.batchSize}`);

    console.log('---\n');
  }

  /**
   * 测试回调函数
   */
  private testCallbacks(): void {
    console.log('10. 测试回调函数');

    const options: AIModelLoadOptions = {
      onProgress: (progress: number, stage?: string, details?: any) => {
        console.log(`  进度回调: ${(progress * 100).toFixed(1)}% - ${stage || '未知阶段'}`);
      },
      onComplete: (success: boolean, model?: any, stats?: any) => {
        console.log(`  完成回调: ${success ? '成功' : '失败'}`);
      },
      onError: (error: Error, context?: any) => {
        console.log(`  错误回调: ${error.message}`);
      },
      onStageChange: (stage: string, progress: number) => {
        console.log(`  阶段变更: ${stage} (${(progress * 100).toFixed(1)}%)`);
      },
      onResourceUsage: (usage) => {
        console.log(`  资源使用: CPU ${(usage.cpuUsage * 100).toFixed(1)}%, 内存 ${(usage.memoryUsage / 1024 / 1024).toFixed(1)}MB`);
      }
    };

    console.log('回调函数配置完成:');
    console.log(`  进度回调: ${typeof options.onProgress === 'function' ? '已设置' : '未设置'}`);
    console.log(`  完成回调: ${typeof options.onComplete === 'function' ? '已设置' : '未设置'}`);
    console.log(`  错误回调: ${typeof options.onError === 'function' ? '已设置' : '未设置'}`);
    console.log(`  阶段变更回调: ${typeof options.onStageChange === 'function' ? '已设置' : '未设置'}`);
    console.log(`  资源使用回调: ${typeof options.onResourceUsage === 'function' ? '已设置' : '未设置'}`);

    // 模拟回调调用
    console.log('\n模拟回调调用:');
    options.onProgress?.(0.5, '模型加载中', { step: 'downloading' });
    options.onStageChange?.('验证模型', 0.8);
    options.onResourceUsage?.({
      cpuUsage: 0.6,
      memoryUsage: 512 * 1024 * 1024,
      timestamp: Date.now()
    });
    options.onComplete?.(true, { id: 'test-model' }, { loadTime: 5000 });

    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIModelLoadOptionsTest();
  test.runAllTests().catch(console.error);
}
