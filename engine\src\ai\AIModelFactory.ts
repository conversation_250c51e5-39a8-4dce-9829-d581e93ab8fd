/**
 * AI模型工厂
 * 用于创建和管理AI模型实例
 */
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { IAIModel } from './models/IAIModel';
import { GPTModel } from './models/GPTModel';
import { StableDiffusionModel } from './models/StableDiffusionModel';
import { BERTModel } from './models/BERTModel';
import { RoBERTaModel } from './models/RoBERTaModel';
import { DistilBERTModel } from './models/DistilBERTModel';
import { ALBERTModel } from './models/ALBERTModel';
import { XLNetModel } from './models/XLNetModel';
import { BARTModel } from './models/BARTModel';
import { T5Model } from './models/T5Model';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 模型状态
 */
export enum ModelStatus {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 就绪 */
  READY = 'ready',
  /** 加载中 */
  LOADING = 'loading',
  /** 运行中 */
  RUNNING = 'running',
  /** 错误 */
  ERROR = 'error',
  /** 已释放 */
  DISPOSED = 'disposed'
}

/**
 * 模型创建选项
 */
export interface ModelCreationOptions {
  /** 是否异步创建 */
  async?: boolean;
  /** 创建超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否预热模型 */
  warmup?: boolean;
  /** 优先级 */
  priority?: number;
}

/**
 * 模型实例信息
 */
export interface ModelInstance {
  /** 模型实例 */
  model: IAIModel;
  /** 模型状态 */
  status: ModelStatus;
  /** 创建时间 */
  createdAt: number;
  /** 最后使用时间 */
  lastUsedAt: number;
  /** 使用次数 */
  usageCount: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
  /** 是否预热 */
  isWarmedUp: boolean;
}

/**
 * 模型构造函数类型
 */
export type ModelConstructor = new (config: any, factoryConfig: AIModelFactoryConfig) => IAIModel;

/**
 * 模型注册信息
 */
export interface ModelRegistration {
  /** 模型类型 */
  type: AIModelType;
  /** 模型构造函数 */
  constructor: ModelConstructor;
  /** 支持的变体 */
  supportedVariants?: string[];
  /** 默认配置 */
  defaultConfig?: Partial<AIModelConfig>;
  /** 是否需要预热 */
  requiresWarmup?: boolean;
}

/**
 * AI模型工厂配置
 */
export interface AIModelFactoryConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModels?: boolean;
  /** 模型API密钥 */
  apiKeys?: Record<string, string>;
  /** 模型基础URL */
  baseUrls?: Record<string, string>;
  /** 模型版本 */
  modelVersions?: Record<string, string>;
  /** 最大缓存大小 */
  maxCacheSize?: number;
  /** 模型池大小 */
  poolSize?: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用自动预热 */
  enableAutoWarmup?: boolean;
  /** 预热超时时间（毫秒） */
  warmupTimeout?: number;
  /** 模型创建超时时间（毫秒） */
  creationTimeout?: number;
}

/**
 * AI模型工厂
 */
export class AIModelFactory extends EventEmitter {
  /** 配置 */
  private config: Required<AIModelFactoryConfig>;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<AIModelFactoryConfig> = {
    debug: false,
    useLocalModels: false,
    apiKeys: {},
    baseUrls: {},
    modelVersions: {},
    maxCacheSize: 50,
    poolSize: 10,
    enablePerformanceMonitoring: false,
    enableAutoWarmup: false,
    warmupTimeout: 30000,
    creationTimeout: 60000
  };

  /** 模型缓存 */
  private modelCache: Map<string, ModelInstance> = new Map();

  /** 模型注册表 */
  private modelRegistry: Map<AIModelType, ModelRegistration> = new Map();

  /** 模型池 */
  private modelPool: Map<AIModelType, IAIModel[]> = new Map();

  /** 创建中的模型 */
  private creatingModels: Map<string, Promise<IAIModel | null>> = new Map();

  /** 性能监控数据 */
  private performanceMetrics: Map<string, any> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIModelFactoryConfig = {}) {
    super();

    this.config = {
      ...AIModelFactory.DEFAULT_CONFIG,
      ...config
    };

    // 初始化模型注册表
    this.initializeModelRegistry();

    if (this.config.debug) {
      console.log('AI模型工厂初始化完成');
    }
  }

  /**
   * 创建模型（同步）
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 创建选项
   * @returns 模型实例
   */
  public createModel(
    modelType: AIModelType,
    config: AIModelConfig = {},
    options: ModelCreationOptions = {}
  ): IAIModel | null {
    try {
      // 生成模型ID
      const modelId = this.generateModelId(modelType, config);

      // 检查缓存
      const cachedInstance = this.modelCache.get(modelId);
      if (cachedInstance) {
        cachedInstance.lastUsedAt = Date.now();
        cachedInstance.usageCount++;
        return cachedInstance.model;
      }

      // 从注册表创建模型
      const model = this.createModelFromRegistry(modelType, config);

      if (model) {
        // 创建模型实例信息
        const instance: ModelInstance = {
          model,
          status: ModelStatus.READY,
          createdAt: Date.now(),
          lastUsedAt: Date.now(),
          usageCount: 1,
          memoryUsage: this.estimateMemoryUsage(model),
          isWarmedUp: false
        };

        // 添加到缓存
        this.addToCache(modelId, instance);

        // 发出事件
        this.emit('modelCreated', { modelId, modelType, model });

        if (this.config.debug) {
          console.log(`模型创建成功: ${modelId}`);
        }

        return model;
      }

      return null;
    } catch (error) {
      console.error(`创建模型实例失败: ${error}`);
      this.emit('modelCreationError', { modelType, config, error });
      return null;
    }
  }

  /**
   * 异步创建模型
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 创建选项
   * @returns 模型实例Promise
   */
  public async createModelAsync(
    modelType: AIModelType,
    config: AIModelConfig = {},
    options: ModelCreationOptions = {}
  ): Promise<IAIModel | null> {
    const modelId = this.generateModelId(modelType, config);

    // 检查是否正在创建
    if (this.creatingModels.has(modelId)) {
      return this.creatingModels.get(modelId)!;
    }

    // 创建Promise
    const createPromise = this.createModelWithRetry(modelType, config, options);
    this.creatingModels.set(modelId, createPromise);

    try {
      const model = await createPromise;
      return model;
    } finally {
      this.creatingModels.delete(modelId);
    }
  }

  /**
   * 带重试的模型创建
   */
  private async createModelWithRetry(
    modelType: AIModelType,
    config: AIModelConfig,
    options: ModelCreationOptions
  ): Promise<IAIModel | null> {
    const maxRetries = options.retryCount || 3;
    const retryDelay = options.retryDelay || 1000;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const model = await this.createModelWithTimeout(modelType, config, options);

        if (model && options.warmup) {
          await this.warmupModel(model);
        }

        return model;
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }

        if (this.config.debug) {
          console.log(`模型创建失败，重试 ${attempt + 1}/${maxRetries}: ${error}`);
        }

        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }

    return null;
  }

  /**
   * 带超时的模型创建
   */
  private async createModelWithTimeout(
    modelType: AIModelType,
    config: AIModelConfig,
    options: ModelCreationOptions
  ): Promise<IAIModel | null> {
    const timeout = options.timeout || this.config.creationTimeout;

    return new Promise<IAIModel | null>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`模型创建超时: ${timeout}ms`));
      }, timeout);

      try {
        const model = this.createModel(modelType, config, options);
        clearTimeout(timer);
        resolve(model);
      } catch (error) {
        clearTimeout(timer);
        reject(error);
      }
    });
  }

  /**
   * 初始化模型注册表
   */
  private initializeModelRegistry(): void {
    // 注册内置模型
    this.registerModel({
      type: AIModelType.GPT,
      constructor: GPTModel as any,
      supportedVariants: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'text-davinci-003'],
      requiresWarmup: true
    });

    this.registerModel({
      type: AIModelType.STABLE_DIFFUSION,
      constructor: StableDiffusionModel as any,
      supportedVariants: ['v1.4', 'v1.5', 'v2.0', 'v2.1', 'xl', 'xl-turbo', 'custom'],
      requiresWarmup: true
    });

    this.registerModel({
      type: AIModelType.BERT,
      constructor: BERTModel as any,
      supportedVariants: ['base', 'large', 'multilingual'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.ROBERTA,
      constructor: RoBERTaModel as any,
      supportedVariants: ['base', 'large', 'distilled'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.DISTILBERT,
      constructor: DistilBERTModel as any,
      supportedVariants: ['base', 'multilingual'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.ALBERT,
      constructor: ALBERTModel as any,
      supportedVariants: ['base', 'large', 'xlarge', 'xxlarge'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.XLNET,
      constructor: XLNetModel as any,
      supportedVariants: ['base', 'large'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.BART,
      constructor: BARTModel as any,
      supportedVariants: ['base', 'large', 'cnn'],
      requiresWarmup: false
    });

    this.registerModel({
      type: AIModelType.T5,
      constructor: T5Model as any,
      supportedVariants: ['small', 'base', 'large', 'xl', 'xxl'],
      requiresWarmup: false
    });
  }

  /**
   * 注册模型
   */
  public registerModel(registration: ModelRegistration): void {
    this.modelRegistry.set(registration.type, registration);

    if (this.config.debug) {
      console.log(`模型已注册: ${registration.type}`);
    }

    this.emit('modelRegistered', registration);
  }

  /**
   * 从注册表创建模型
   */
  private createModelFromRegistry(modelType: AIModelType, config: AIModelConfig): IAIModel | null {
    const registration = this.modelRegistry.get(modelType);
    if (!registration) {
      throw new Error(`未注册的模型类型: ${modelType}`);
    }

    // 验证变体
    if (config.variant && registration.supportedVariants) {
      if (!registration.supportedVariants.includes(config.variant)) {
        throw new Error(`不支持的模型变体: ${config.variant} for ${modelType}`);
      }
    }

    // 合并默认配置
    const finalConfig = {
      ...registration.defaultConfig,
      ...config
    };

    // 创建模型实例
    return new registration.constructor(finalConfig, this.config);
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(model: IAIModel): number {
    // 简单的内存估算，实际应该根据模型类型和配置计算
    const baseMemory = 100 * 1024 * 1024; // 100MB基础内存
    const modelType = model.getType();

    switch (modelType) {
      case AIModelType.GPT:
        return baseMemory * 10; // 1GB
      case AIModelType.STABLE_DIFFUSION:
        return baseMemory * 20; // 2GB
      case AIModelType.BERT:
        return baseMemory * 5; // 500MB
      default:
        return baseMemory;
    }
  }

  /**
   * 添加到缓存
   */
  private addToCache(modelId: string, instance: ModelInstance): void {
    // 检查缓存大小限制
    if (this.modelCache.size >= this.config.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.modelCache.set(modelId, instance);

    this.emit('modelCached', { modelId, instance });
  }

  /**
   * 驱逐最近最少使用的模型
   */
  private evictLeastRecentlyUsed(): void {
    let oldestTime = Date.now();
    let oldestId = '';

    for (const [id, instance] of this.modelCache) {
      if (instance.lastUsedAt < oldestTime) {
        oldestTime = instance.lastUsedAt;
        oldestId = id;
      }
    }

    if (oldestId) {
      this.releaseModel(oldestId);
    }
  }

  /**
   * 预热模型
   */
  private async warmupModel(model: IAIModel): Promise<void> {
    try {
      // 执行一些预热操作
      await model.initialize();

      // 执行简单的推理来预热模型
      if (typeof model.generateText === 'function') {
        await model.generateText('warmup', { maxTokens: 1 });
      }

      this.emit('modelWarmedUp', { model });

      if (this.config.debug) {
        console.log(`模型预热完成: ${model.getId()}`);
      }
    } catch (error) {
      console.warn(`模型预热失败: ${error}`);
    }
  }

  /**
   * 生成模型ID
   * @param modelType 模型类型
   * @param config 模型配置
   * @returns 模型ID
   */
  private generateModelId(modelType: AIModelType, config: AIModelConfig): string {
    // 基本ID
    let id = `${modelType}`;

    // 添加版本信息
    if (config.version) {
      id += `-${config.version}`;
    } else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
      id += `-${this.config.modelVersions[modelType]}`;
    }

    // 添加其他配置信息
    if (config.variant) {
      id += `-${config.variant}`;
    }

    return id;
  }

  /**
   * 获取模型
   * @param modelId 模型ID
   * @returns 模型实例
   */
  public getModel(modelId: string): IAIModel | null {
    const instance = this.modelCache.get(modelId);
    if (instance) {
      instance.lastUsedAt = Date.now();
      instance.usageCount++;
      return instance.model;
    }
    return null;
  }

  /**
   * 获取所有模型
   * @returns 模型实例映射
   */
  public getAllModels(): Map<string, IAIModel> {
    const result = new Map<string, IAIModel>();
    for (const [id, instance] of this.modelCache) {
      result.set(id, instance.model);
    }
    return result;
  }

  /**
   * 获取模型实例信息
   * @param modelId 模型ID
   * @returns 模型实例信息
   */
  public getModelInstance(modelId: string): ModelInstance | null {
    return this.modelCache.get(modelId) || null;
  }

  /**
   * 获取所有模型实例信息
   * @returns 模型实例信息映射
   */
  public getAllModelInstances(): Map<string, ModelInstance> {
    return new Map(this.modelCache);
  }

  /**
   * 释放模型
   * @param modelId 模型ID
   * @returns 是否成功
   */
  public releaseModel(modelId: string): boolean {
    // 检查模型是否存在
    const instance = this.modelCache.get(modelId);
    if (!instance) {
      return false;
    }

    try {
      // 释放模型资源
      if (typeof instance.model.dispose === 'function') {
        instance.model.dispose();
      }

      // 从缓存中移除
      this.modelCache.delete(modelId);

      // 发出事件
      this.emit('modelReleased', { modelId, instance });

      if (this.config.debug) {
        console.log(`模型已释放: ${modelId}`);
      }

      return true;
    } catch (error) {
      console.error(`释放模型失败: ${modelId}`, error);
      return false;
    }
  }

  /**
   * 释放所有模型
   */
  public releaseAllModels(): void {
    const modelIds = Array.from(this.modelCache.keys());

    for (const modelId of modelIds) {
      this.releaseModel(modelId);
    }

    // 清空其他缓存
    this.creatingModels.clear();
    this.performanceMetrics.clear();

    if (this.config.debug) {
      console.log('所有模型已释放');
    }

    this.emit('allModelsReleased');
  }

  /**
   * 批量预热模型
   */
  public async warmupModels(modelTypes: AIModelType[]): Promise<void> {
    const warmupPromises = modelTypes.map(async (modelType) => {
      try {
        const model = await this.createModelAsync(modelType, {}, { warmup: true });
        if (model) {
          const modelId = this.generateModelId(modelType, {});
          const instance = this.modelCache.get(modelId);
          if (instance) {
            instance.isWarmedUp = true;
          }
        }
      } catch (error) {
        console.warn(`预热模型失败: ${modelType}`, error);
      }
    });

    await Promise.all(warmupPromises);

    this.emit('modelsWarmedUp', modelTypes);

    if (this.config.debug) {
      console.log(`批量预热完成: ${modelTypes.join(', ')}`);
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): any {
    const stats = {
      totalModels: this.modelCache.size,
      modelsByType: new Map<AIModelType, number>(),
      totalMemoryUsage: 0,
      averageUsageCount: 0,
      warmedUpModels: 0
    };

    let totalUsage = 0;

    for (const instance of this.modelCache.values()) {
      const modelType = instance.model.getType();
      stats.modelsByType.set(modelType, (stats.modelsByType.get(modelType) || 0) + 1);
      stats.totalMemoryUsage += instance.memoryUsage;
      totalUsage += instance.usageCount;

      if (instance.isWarmedUp) {
        stats.warmedUpModels++;
      }
    }

    if (this.modelCache.size > 0) {
      stats.averageUsageCount = totalUsage / this.modelCache.size;
    }

    return stats;
  }

  /**
   * 清理未使用的模型
   */
  public cleanupUnusedModels(maxIdleTime: number = 3600000): number { // 默认1小时
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [modelId, instance] of this.modelCache) {
      if (now - instance.lastUsedAt > maxIdleTime) {
        toRemove.push(modelId);
      }
    }

    for (const modelId of toRemove) {
      this.releaseModel(modelId);
    }

    if (this.config.debug && toRemove.length > 0) {
      console.log(`清理了 ${toRemove.length} 个未使用的模型`);
    }

    this.emit('modelsCleanedUp', toRemove);

    return toRemove.length;
  }

  /**
   * 检查模型是否支持特定功能
   */
  public isFeatureSupported(modelType: AIModelType, feature: string): boolean {
    const registration = this.modelRegistry.get(modelType);
    if (!registration) {
      return false;
    }

    // 这里可以根据模型类型和功能进行更详细的检查
    // 目前简单返回true，实际应该根据模型能力进行判断
    return true;
  }

  /**
   * 获取支持的模型类型
   */
  public getSupportedModelTypes(): AIModelType[] {
    return Array.from(this.modelRegistry.keys());
  }

  /**
   * 获取模型注册信息
   */
  public getModelRegistration(modelType: AIModelType): ModelRegistration | null {
    return this.modelRegistry.get(modelType) || null;
  }

  /**
   * 检查模型是否已缓存
   */
  public isModelCached(modelType: AIModelType, config: AIModelConfig = {}): boolean {
    const modelId = this.generateModelId(modelType, config);
    return this.modelCache.has(modelId);
  }

  /**
   * 获取缓存使用情况
   */
  public getCacheUsage(): {
    used: number;
    total: number;
    percentage: number;
  } {
    const used = this.modelCache.size;
    const total = this.config.maxCacheSize;
    const percentage = total > 0 ? (used / total) * 100 : 0;

    return { used, total, percentage };
  }

  /**
   * 设置模型优先级
   */
  public setModelPriority(modelId: string, priority: number): boolean {
    const instance = this.modelCache.get(modelId);
    if (instance) {
      // 这里可以实现优先级逻辑
      // 目前只是记录，实际可以影响驱逐策略
      (instance as any).priority = priority;
      return true;
    }
    return false;
  }

  /**
   * 获取模型健康状态
   */
  public async getModelHealth(modelId: string): Promise<{
    status: ModelStatus;
    lastUsed: number;
    usageCount: number;
    memoryUsage: number;
    isResponsive: boolean;
  } | null> {
    const instance = this.modelCache.get(modelId);
    if (!instance) {
      return null;
    }

    // 简单的健康检查
    let isResponsive = true;
    try {
      // 可以执行一个简单的测试来检查模型是否响应
      if (typeof instance.model.generateText === 'function') {
        await Promise.race([
          instance.model.generateText('test', { maxTokens: 1 }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), 5000))
        ]);
      }
    } catch (error) {
      isResponsive = false;
    }

    return {
      status: instance.status,
      lastUsed: instance.lastUsedAt,
      usageCount: instance.usageCount,
      memoryUsage: instance.memoryUsage,
      isResponsive
    };
  }

  /**
   * 销毁工厂
   */
  public dispose(): void {
    // 释放所有模型
    this.releaseAllModels();

    // 清空注册表
    this.modelRegistry.clear();

    // 清空模型池
    this.modelPool.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('AI模型工厂已销毁');
    }
  }
}
