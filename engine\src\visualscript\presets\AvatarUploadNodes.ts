/**
 * 虚拟化身上传相关的视觉脚本节点
 * 
 * 提供虚拟化身文件上传、解析、数字人创建等功能的节点实现
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeSlot, SlotType } from '../NodeSlot';
import { AvatarUploadSystem, UploadFileInfo, UploadConfig, UploadResult } from '../../avatar/AvatarUploadSystem';
import { Vector3 } from '../../math/Vector3';

/**
 * 上传虚拟化身文件节点
 */
export class UploadAvatarFileNode extends VisualScriptNode {
  constructor() {
    super('UploadAvatarFile', '上传虚拟化身文件', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('fileName', '文件名', SlotType.String));
    this.addInputSlot(new NodeSlot('fileContent', '文件内容', SlotType.String));
    this.addInputSlot(new NodeSlot('targetSceneId', '目标场景ID', SlotType.String, 'default'));
    this.addInputSlot(new NodeSlot('spawnX', '生成位置X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('spawnY', '生成位置Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('spawnZ', '生成位置Z', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('digitalHumanName', '数字人名称', SlotType.String, ''));
    this.addInputSlot(new NodeSlot('enableAI', '启用AI', SlotType.Boolean, false));
    this.addInputSlot(new NodeSlot('enableVoice', '启用语音', SlotType.Boolean, false));
    this.addInputSlot(new NodeSlot('enableInteraction', '启用交互', SlotType.Boolean, true));
    this.addInputSlot(new NodeSlot('autoActivate', '自动激活', SlotType.Boolean, true));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '上传成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('uploadId', '上传ID', SlotType.String));
    this.addOutputSlot(new NodeSlot('digitalHumanEntity', '数字人实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));
    this.addOutputSlot(new NodeSlot('processingTime', '处理时间', SlotType.Number));
    this.addOutputSlot(new NodeSlot('uploadResult', '上传结果', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const fileName = this.getInputValue('fileName') as string;
    const fileContent = this.getInputValue('fileContent') as string;
    const targetSceneId = this.getInputValue('targetSceneId') as string;
    const spawnX = this.getInputValue('spawnX') as number;
    const spawnY = this.getInputValue('spawnY') as number;
    const spawnZ = this.getInputValue('spawnZ') as number;
    const digitalHumanName = this.getInputValue('digitalHumanName') as string;
    const enableAI = this.getInputValue('enableAI') as boolean;
    const enableVoice = this.getInputValue('enableVoice') as boolean;
    const enableInteraction = this.getInputValue('enableInteraction') as boolean;
    const autoActivate = this.getInputValue('autoActivate') as boolean;

    if (!fileName || !fileContent) {
      this.triggerOutput('onError');
      throw new Error('文件名和文件内容不能为空');
    }

    try {
      // 获取上传系统
      const uploadSystem = this.context?.world?.getSystem('AvatarUploadSystem') as AvatarUploadSystem;
      if (!uploadSystem) {
        throw new Error('虚拟化身上传系统未找到');
      }

      // 构建文件信息
      const fileInfo: UploadFileInfo = {
        fileName,
        fileSize: fileContent.length,
        fileType: fileName.split('.').pop() || 'unknown',
        fileContent: new TextEncoder().encode(fileContent),
        uploadTime: new Date()
      };

      // 构建上传配置
      const uploadConfig: UploadConfig = {
        targetSceneId,
        spawnPosition: new Vector3(spawnX, spawnY, spawnZ),
        autoActivate,
        digitalHumanName: digitalHumanName || undefined,
        digitalHumanConfig: {
          enableAI,
          enableVoice,
          enableInteraction
        }
      };

      // 执行上传
      const result = await uploadSystem.uploadAvatarFile(fileInfo, uploadConfig);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('uploadId', result.uploadId);
      this.setOutputValue('digitalHumanEntity', result.digitalHumanEntity);
      this.setOutputValue('avatarData', result.avatarData);
      this.setOutputValue('processingTime', result.processingTime);
      this.setOutputValue('uploadResult', result);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return result;
    } catch (error) {
      console.error('上传虚拟化身文件失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 创建数字人节点
 */
export class CreateDigitalHumanNode extends VisualScriptNode {
  constructor() {
    super('CreateDigitalHuman', '创建数字人', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarData', '虚拟化身数据', SlotType.Object));
    this.addInputSlot(new NodeSlot('sceneId', '场景ID', SlotType.String, 'default'));
    this.addInputSlot(new NodeSlot('positionX', '位置X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('positionY', '位置Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('positionZ', '位置Z', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('name', '数字人名称', SlotType.String, ''));
    this.addInputSlot(new NodeSlot('enableAI', '启用AI', SlotType.Boolean, false));
    this.addInputSlot(new NodeSlot('enableVoice', '启用语音', SlotType.Boolean, false));
    this.addInputSlot(new NodeSlot('knowledgeBaseId', '知识库ID', SlotType.String, ''));
    this.addInputSlot(new NodeSlot('personality', '个性设置', SlotType.String, 'friendly'));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '创建成功', SlotType.Boolean));
    this.addOutputSlot(new NodeSlot('digitalHumanEntity', '数字人实体', SlotType.Object));
    this.addOutputSlot(new NodeSlot('digitalHumanId', '数字人ID', SlotType.String));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const avatarData = this.getInputValue('avatarData');
    const sceneId = this.getInputValue('sceneId') as string;
    const positionX = this.getInputValue('positionX') as number;
    const positionY = this.getInputValue('positionY') as number;
    const positionZ = this.getInputValue('positionZ') as number;
    const name = this.getInputValue('name') as string;
    const enableAI = this.getInputValue('enableAI') as boolean;
    const enableVoice = this.getInputValue('enableVoice') as boolean;
    const knowledgeBaseId = this.getInputValue('knowledgeBaseId') as string;
    const personality = this.getInputValue('personality') as string;

    if (!avatarData) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身数据不能为空');
    }

    try {
      // 获取上传系统
      const uploadSystem = this.context?.world?.getSystem('AvatarUploadSystem') as AvatarUploadSystem;
      if (!uploadSystem) {
        throw new Error('虚拟化身上传系统未找到');
      }

      // 构建上传配置
      const uploadConfig: UploadConfig = {
        targetSceneId: sceneId,
        spawnPosition: new Vector3(positionX, positionY, positionZ),
        autoActivate: true,
        digitalHumanName: name || `DigitalHuman_${Date.now()}`,
        digitalHumanConfig: {
          enableAI,
          enableVoice,
          enableInteraction: true,
          knowledgeBaseId: knowledgeBaseId || undefined,
          personality: personality || 'friendly'
        }
      };

      // 模拟文件信息（从虚拟化身数据创建）
      const fileInfo: UploadFileInfo = {
        fileName: `${avatarData.id}.json`,
        fileSize: JSON.stringify(avatarData).length,
        fileType: 'json',
        fileContent: new TextEncoder().encode(JSON.stringify(avatarData)),
        uploadTime: new Date()
      };

      // 执行创建
      const result = await uploadSystem.uploadAvatarFile(fileInfo, uploadConfig);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('digitalHumanEntity', result.digitalHumanEntity);
      this.setOutputValue('digitalHumanId', result.uploadId);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return result;
    } catch (error) {
      console.error('创建数字人失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取数字人列表节点
 */
export class GetDigitalHumansNode extends VisualScriptNode {
  constructor() {
    super('GetDigitalHumans', '获取数字人列表', '虚拟化身');

    // 输出插槽
    this.addOutputSlot(new NodeSlot('digitalHumans', '数字人列表', SlotType.Array));
    this.addOutputSlot(new NodeSlot('count', '数字人数量', SlotType.Number));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    try {
      // 获取上传系统
      const uploadSystem = this.context?.world?.getSystem('AvatarUploadSystem') as AvatarUploadSystem;
      if (!uploadSystem) {
        throw new Error('虚拟化身上传系统未找到');
      }

      // 获取数字人列表
      const digitalHumans = uploadSystem.getDigitalHumans();

      // 设置输出值
      this.setOutputValue('digitalHumans', digitalHumans);
      this.setOutputValue('count', digitalHumans.length);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return digitalHumans;
    } catch (error) {
      console.error('获取数字人列表失败:', error);
      throw error;
    }
  }
}

/**
 * 删除数字人节点
 */
export class RemoveDigitalHumanNode extends VisualScriptNode {
  constructor() {
    super('RemoveDigitalHuman', '删除数字人', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('digitalHumanId', '数字人ID', SlotType.String));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '删除成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected async executeImpl(): Promise<any> {
    const digitalHumanId = this.getInputValue('digitalHumanId') as string;

    if (!digitalHumanId) {
      this.triggerOutput('onError');
      throw new Error('数字人ID不能为空');
    }

    try {
      // 获取上传系统
      const uploadSystem = this.context?.world?.getSystem('AvatarUploadSystem') as AvatarUploadSystem;
      if (!uploadSystem) {
        throw new Error('虚拟化身上传系统未找到');
      }

      // 执行删除
      const success = await uploadSystem.removeDigitalHuman(digitalHumanId);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('删除数字人失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取上传统计信息节点
 */
export class GetUploadStatisticsNode extends VisualScriptNode {
  constructor() {
    super('GetUploadStatistics', '获取上传统计', '虚拟化身');

    // 输出插槽
    this.addOutputSlot(new NodeSlot('totalUploads', '总上传次数', SlotType.Number));
    this.addOutputSlot(new NodeSlot('successfulUploads', '成功上传次数', SlotType.Number));
    this.addOutputSlot(new NodeSlot('failedUploads', '失败上传次数', SlotType.Number));
    this.addOutputSlot(new NodeSlot('activeDigitalHumans', '活跃数字人数量', SlotType.Number));
    this.addOutputSlot(new NodeSlot('averageProcessingTime', '平均处理时间', SlotType.Number));
    this.addOutputSlot(new NodeSlot('supportedFormats', '支持的格式', SlotType.Array));
    this.addOutputSlot(new NodeSlot('statistics', '统计信息', SlotType.Object));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    try {
      // 获取上传系统
      const uploadSystem = this.context?.world?.getSystem('AvatarUploadSystem') as AvatarUploadSystem;
      if (!uploadSystem) {
        throw new Error('虚拟化身上传系统未找到');
      }

      // 获取统计信息
      const statistics = uploadSystem.getUploadStatistics();

      // 设置输出值
      this.setOutputValue('totalUploads', statistics.totalUploads);
      this.setOutputValue('successfulUploads', statistics.successfulUploads);
      this.setOutputValue('failedUploads', statistics.failedUploads);
      this.setOutputValue('activeDigitalHumans', statistics.activeDigitalHumans);
      this.setOutputValue('averageProcessingTime', statistics.averageProcessingTime);
      this.setOutputValue('supportedFormats', statistics.supportedFormats);
      this.setOutputValue('statistics', statistics);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return statistics;
    } catch (error) {
      console.error('获取上传统计失败:', error);
      throw error;
    }
  }
}

/**
 * 注册虚拟化身上传节点
 */
export function registerAvatarUploadNodes(registry: any): void {
  registry.registerNode('UploadAvatarFileNode', UploadAvatarFileNode);
  registry.registerNode('CreateDigitalHumanNode', CreateDigitalHumanNode);
  registry.registerNode('GetDigitalHumansNode', GetDigitalHumansNode);
  registry.registerNode('RemoveDigitalHumanNode', RemoveDigitalHumanNode);
  registry.registerNode('GetUploadStatisticsNode', GetUploadStatisticsNode);
}
