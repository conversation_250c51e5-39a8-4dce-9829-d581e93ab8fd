# 视觉脚本节点使用指南

## 概述

本指南详细介绍了DL引擎视觉脚本系统中413个节点的使用方法、最佳实践和常见应用场景。这些节点分布在61个节点文件中，覆盖了从基础编程逻辑到高级AI功能的完整功能体系，实现100%功能覆盖。

## 快速入门

### 基础概念

1. **节点 (Node)**: 执行特定功能的基本单元，每个节点都有明确的输入输出接口
2. **插槽 (Socket)**: 节点的输入输出接口，支持强类型数据流验证
3. **连接 (Connection)**: 节点间的数据流连接，实现可视化编程
4. **图形 (Graph)**: 由节点和连接组成的完整脚本，支持子图和函数节点
5. **作用域 (Scope)**: 变量的有效范围，包括全局、局部和会话作用域

### 节点分类体系

#### 按功能分类
- **核心节点**: 基础流程控制和数据操作（14个节点）
- **数学节点**: 完整的数学运算功能（16个节点）
- **AI节点**: 人工智能和机器学习（46个节点）
- **网络节点**: 网络通信和数据传输（43个节点）
- **UI节点**: 用户界面组件和交互（34个节点）
- **物理节点**: 物理模拟和碰撞检测（22个节点）
- **动画节点**: 动画播放和控制（21个节点）
- **音频节点**: 音频处理和播放（13个节点）
- **输入节点**: 各种输入设备处理（6个节点）
- **数据处理节点**: 文件、数据库、加密等（28个节点）
- **专业应用节点**: 医疗、工业、区块链等（45个节点）
- **系统节点**: 调试、时间、实体管理等（125个节点）

#### 按复杂度分类
- **基础节点**: 简单操作，易于理解和使用
- **中级节点**: 需要一定配置，功能较为复杂
- **高级节点**: 专业功能，需要深入理解相关领域知识

## 核心节点使用详解 (CoreNodes.ts - 14个节点)

### 1. 事件节点

#### OnStartNode (开始事件节点)
**原理**: 脚本执行的入口点，当视觉脚本开始运行时自动触发
**用途**: 初始化变量、设置初始状态、启动主要逻辑流程

```typescript
// 基础使用示例
const startNode = new OnStartNode({
  id: 'start_1',
  type: 'core/events/onStart'
});

// 连接到初始化逻辑
startNode.connectTo(initializeNode, 'flow');
```

**最佳实践**:
- 用于设置初始变量和配置参数
- 加载必要的资源文件和数据
- 建立网络连接和数据库连接
- 初始化UI组件和界面状态

**常见应用场景**:
- 游戏开始时的初始化设置
- 应用启动时的资源加载
- 用户登录后的数据初始化
- 场景切换时的环境准备

#### OnUpdateNode (更新事件节点)
**原理**: 每帧执行一次，提供持续的逻辑更新
**用途**: 实时监控、动画更新、状态检查

```typescript
// 持续监控示例
const updateNode = new OnUpdateNode({
  id: 'update_1',
  type: 'core/events/onUpdate'
});

// 连接到监控逻辑
updateNode.connectTo(monitorNode, 'flow');
```

**性能优化建议**:
- 避免在更新循环中执行重复计算
- 使用条件判断减少不必要的执行
- 合理使用缓存机制存储计算结果
- 设置执行频率限制，避免过度消耗资源

**常见应用场景**:
- 角色移动和动画更新
- 实时数据监控和显示
- 用户输入状态检测
- 物理模拟的持续计算

### 2. 流程控制节点

#### BranchNode (分支节点)
**原理**: 根据布尔条件选择执行路径
**用途**: 条件判断、逻辑分支、决策控制

```typescript
// 基础条件判断示例
const branchNode = new BranchNode({
  id: 'branch_1',
  type: 'core/flow/branch'
});

// 设置条件
branchNode.setInputValue('condition', true);

// 连接不同路径
branchNode.connectTo(truePathNode, 'true');
branchNode.connectTo(falsePathNode, 'false');

// 复杂条件示例
const complexBranch = new BranchNode({
  id: 'complex_branch',
  condition: 'playerLevel >= 10 && hasKey === true'
});
```

**最佳实践**:
- 使用清晰的条件表达式，避免过于复杂的逻辑
- 为每个分支路径添加注释说明
- 考虑使用SwitchNode处理多条件分支
- 确保所有可能的条件都有对应的处理路径

**常见应用场景**:
- 用户权限验证和访问控制
- 游戏状态检查和场景切换
- 表单数据验证和错误处理
- 设备状态检测和响应处理

#### SequenceNode (序列节点)
**原理**: 按顺序依次执行多个操作
**用途**: 步骤化执行、流程控制

```typescript
// 步骤化执行示例
const sequenceNode = new SequenceNode({
  id: 'sequence_1',
  type: 'core/flow/sequence'
});

// 连接多个步骤
sequenceNode.connectTo(step1Node, 'step1');
sequenceNode.connectTo(step2Node, 'step2');
sequenceNode.connectTo(step3Node, 'step3');
```

**应用场景**:
- 用户注册流程的多步骤执行
- 数据处理的管道式操作
- 动画序列的顺序播放
- 系统初始化的分步执行

#### ForLoopNode (For循环节点)
**原理**: 指定次数的循环执行
**用途**: 批量操作、重复任务、数组遍历

```typescript
// 批量处理示例
const forLoopNode = new ForLoopNode({
  id: 'loop_1',
  type: 'core/flow/forLoop'
});

// 设置循环参数
forLoopNode.setInputValue('start', 0);
forLoopNode.setInputValue('end', 10);
forLoopNode.setInputValue('step', 1);

// 连接循环体
forLoopNode.connectTo(processNode, 'loopBody');

// 数组遍历示例
const arrayLoop = new ForLoopNode({
  array: ['item1', 'item2', 'item3'],
  indexVariable: 'currentIndex',
  valueVariable: 'currentItem'
});
```

**性能优化策略**:
- 设置合理的循环次数上限，避免无限循环
- 避免嵌套过深的循环结构
- 使用break条件提前退出不必要的循环
- 在循环内部避免重复的资源分配和释放

**常见应用场景**:
- 批量数据处理和转换
- 数组元素的逐一操作
- 重复动画效果的生成
- 多个对象的统一属性设置

#### WhileLoopNode (While循环节点)
**原理**: 基于条件的循环执行
**用途**: 条件循环、状态等待、动态重复

```typescript
// 条件循环示例
const whileLoop = new WhileLoopNode({
  id: 'while_1',
  condition: 'playerHealth > 0',
  maxIterations: 1000 // 防止无限循环
});

// 状态等待示例
const waitLoop = new WhileLoopNode({
  condition: 'connectionStatus !== "connected"',
  checkInterval: 100, // 每100ms检查一次
  timeout: 30000 // 30秒超时
});
```

**安全使用建议**:
- 始终设置最大迭代次数限制
- 确保循环条件最终会变为false
- 添加超时机制防止死循环
- 在循环体内添加适当的延迟

#### SwitchNode (多路分支节点)
**原理**: 根据输入值选择对应的执行路径
**用途**: 多条件分支、状态机、菜单选择

```typescript
// 多路分支示例
const switchNode = new SwitchNode({
  id: 'switch_1',
  type: 'core/flow/switch'
});

// 设置分支条件
switchNode.setInputValue('value', 'option1');
switchNode.addCase('option1', option1Handler);
switchNode.addCase('option2', option2Handler);
switchNode.addCase('option3', option3Handler);
switchNode.setDefault(defaultHandler);

// 状态机示例
const stateMachine = new SwitchNode({
  value: 'currentState',
  cases: {
    'idle': idleStateHandler,
    'running': runningStateHandler,
    'jumping': jumpingStateHandler,
    'attacking': attackingStateHandler
  }
});
```

**应用场景**:
- 游戏角色状态机控制
- 用户界面菜单导航
- 设备模式切换控制
- 工作流程状态管理

### 3. 数据操作节点

#### SetVariableNode (设置变量节点)
**原理**: 在指定作用域中设置变量值
**用途**: 数据存储、状态保存、参数传递

```typescript
// 基础变量设置
const setVarNode = new SetVariableNode({
  id: 'setVar_1',
  type: 'core/variable/set'
});

// 设置不同作用域的变量
setVarNode.setInputValue('name', 'playerScore');
setVarNode.setInputValue('value', 100);
setVarNode.setInputValue('scope', 'global');

// 复杂数据类型设置
const setObjectVar = new SetVariableNode({
  name: 'playerData',
  value: {
    name: 'Player1',
    level: 5,
    health: 100,
    inventory: ['sword', 'potion']
  },
  scope: 'session'
});
```

**作用域详细说明**:
- `global`: 全局作用域，所有脚本和会话可访问，持久保存
- `local`: 局部作用域，当前脚本内有效，脚本结束后清除
- `session`: 会话作用域，用户会话期间有效，会话结束后清除
- `temporary`: 临时作用域，仅在当前执行周期有效

**最佳实践**:
- 根据数据的生命周期选择合适的作用域
- 使用有意义的变量名，遵循命名规范
- 避免在全局作用域存储大量临时数据
- 定期清理不再使用的变量

#### GetVariableNode (获取变量节点)
**原理**: 从指定作用域获取变量值
**用途**: 数据读取、状态获取、参数传递

```typescript
// 基础变量获取
const getVarNode = new GetVariableNode({
  id: 'getVar_1',
  name: 'playerScore',
  scope: 'global',
  defaultValue: 0 // 变量不存在时的默认值
});

// 条件获取示例
const conditionalGet = new GetVariableNode({
  name: 'userPreferences',
  scope: 'session',
  fallbackScope: 'global', // 备用作用域
  validateType: 'object' // 类型验证
});
```

#### ArrayOperationNode (数组操作节点)
**原理**: 对数组进行各种操作（增删改查）
**用途**: 数据集合处理、列表管理、批量操作

```typescript
// 数组基础操作
const arrayOpNode = new ArrayOperationNode({
  id: 'arrayOp_1',
  type: 'core/array/operation'
});

// 添加元素
arrayOpNode.setInputValue('array', [1, 2, 3]);
arrayOpNode.setInputValue('operation', 'push');
arrayOpNode.setInputValue('value', 4);
// 结果: [1, 2, 3, 4]

// 复杂数组操作示例
const complexArrayOp = new ArrayOperationNode({
  array: [
    {id: 1, name: 'Item1', active: true},
    {id: 2, name: 'Item2', active: false},
    {id: 3, name: 'Item3', active: true}
  ],
  operation: 'filter',
  predicate: 'item.active === true'
});
```

**支持的操作类型**:
- `push`: 添加元素到末尾
- `pop`: 移除并返回末尾元素
- `shift`: 移除并返回首个元素
- `unshift`: 添加元素到开头
- `slice`: 提取子数组
- `splice`: 删除/插入元素
- `indexOf`: 查找元素索引
- `find`: 查找符合条件的元素
- `filter`: 过滤符合条件的元素
- `map`: 映射转换数组元素
- `reduce`: 归约数组为单一值
- `sort`: 排序数组元素
- `reverse`: 反转数组顺序

**性能优化建议**:
- 对于大数组操作，考虑使用批处理
- 避免在循环中频繁修改数组结构
- 使用适当的数据结构（如Set、Map）提高查找效率
- 合理使用数组方法链，避免多次遍历

#### DelayNode (延迟节点)
**原理**: 延迟指定时间后执行后续操作
**用途**: 时间控制、动画延迟、定时触发

```typescript
// 基础延迟示例
const delayNode = new DelayNode({
  id: 'delay_1',
  seconds: 2.5,
  useGameTime: true // 使用游戏时间而非系统时间
});

// 动画延迟示例
const animationDelay = new DelayNode({
  seconds: 1.0,
  onComplete: () => {
    console.log('延迟完成，开始执行动画');
  }
});
```

#### TryCatchNode (异常处理节点)
**原理**: 捕获和处理执行过程中的异常
**用途**: 错误处理、异常恢复、程序稳定性

```typescript
// 异常处理示例
const tryCatchNode = new TryCatchNode({
  id: 'tryCatch_1',
  logErrors: true,
  continueOnError: false
});

// 连接可能出错的操作
tryCatchNode.connectTo(riskyOperation, 'try');
tryCatchNode.connectTo(errorHandler, 'catch');
tryCatchNode.connectTo(finallyHandler, 'finally');
```

#### TypeConvertNode (类型转换节点)
**原理**: 在不同数据类型之间进行转换
**用途**: 数据格式化、类型适配、接口对接

```typescript
// 类型转换示例
const convertNode = new TypeConvertNode({
  sourceType: 'string',
  targetType: 'number',
  value: '123.45'
});
// 结果: 123.45

// 复杂类型转换
const complexConvert = new TypeConvertNode({
  sourceType: 'object',
  targetType: 'json',
  value: {name: 'test', value: 100}
});
// 结果: '{"name":"test","value":100}'
```

#### PrintLogNode (打印日志节点)
**原理**: 在控制台输出调试信息
**用途**: 调试、监控、信息输出

```typescript
// 日志输出示例
const logNode = new PrintLogNode({
  level: 'info',
  message: '脚本执行状态',
  includeTimestamp: true,
  includeStackTrace: false
});

// 条件日志
const conditionalLog = new PrintLogNode({
  level: 'debug',
  condition: 'debugMode === true',
  message: '调试信息：变量值为 ${variableValue}'
});
```

## 数学节点使用详解 (MathNodes.ts - 16个节点)

### 基础运算节点

#### AddNode (加法节点)
**原理**: 执行两个或多个数值的加法运算，支持标量和向量
**用途**: 数值计算、坐标运算、参数调整、向量相加

```typescript
// 基础数值加法
const addNode = new AddNode({
  id: 'add_1'
});
addNode.setInputValue('a', 10);
addNode.setInputValue('b', 20);
const result = addNode.execute(); // 结果: 30

// 向量加法
const vectorAdd = new AddNode({
  supportVectors: true
});
vectorAdd.setInputValue('a', new Vector3(1, 2, 3));
vectorAdd.setInputValue('b', new Vector3(4, 5, 6));
// 结果: Vector3(5, 7, 9)

// 多数值加法
const multiAdd = new AddNode({
  inputs: [10, 20, 30, 40]
});
// 结果: 100
```

**应用场景**:
- 游戏中的分数累加和统计
- 3D空间中的位置偏移计算
- 物理模拟中的力的合成
- 用户界面中的尺寸调整

#### SubtractNode (减法节点)
**原理**: 执行两个数值的减法运算
**用途**: 数值计算、距离计算、差值分析

```typescript
// 基础减法
const subtractNode = new SubtractNode();
subtractNode.setInputValue('minuend', 100);    // 被减数
subtractNode.setInputValue('subtrahend', 30);  // 减数
const result = subtractNode.execute(); // 结果: 70

// 向量减法（计算方向）
const vectorSubtract = new SubtractNode({
  supportVectors: true
});
vectorSubtract.setInputValue('a', new Vector3(5, 3, 1));
vectorSubtract.setInputValue('b', new Vector3(2, 1, 1));
// 结果: Vector3(3, 2, 0)
```

#### MultiplyNode (乘法节点)
**原理**: 执行两个数值的乘法运算
**用途**: 缩放计算、面积计算、比例调整

```typescript
// 基础乘法
const multiplyNode = new MultiplyNode();
multiplyNode.setInputValue('a', 15);
multiplyNode.setInputValue('b', 4);
const result = multiplyNode.execute(); // 结果: 60

// 向量标量乘法（缩放）
const scaleVector = new MultiplyNode({
  vectorScalarMode: true
});
scaleVector.setInputValue('vector', new Vector3(1, 2, 3));
scaleVector.setInputValue('scalar', 2.5);
// 结果: Vector3(2.5, 5, 7.5)
```

#### DivideNode (除法节点)
**原理**: 执行两个数值的除法运算，包含除零检查
**用途**: 比率计算、平均值计算、归一化

```typescript
// 安全除法
const divideNode = new DivideNode({
  checkDivideByZero: true,
  defaultOnZero: 0
});
divideNode.setInputValue('dividend', 100);  // 被除数
divideNode.setInputValue('divisor', 4);     // 除数
const result = divideNode.execute(); // 结果: 25

// 除零处理示例
divideNode.setInputValue('divisor', 0);
const safeResult = divideNode.execute(); // 结果: 0 (默认值)
```

### 高级数学节点

#### TrigonometricNode (三角函数节点)
**原理**: 计算各种三角函数和反三角函数
**用途**: 角度计算、旋转变换、波形生成、周期运动

```typescript
// 基础三角函数
const sinNode = new TrigonometricNode({
  functionType: 'sin'
});
sinNode.setInputValue('angle', Math.PI / 2);
const result = sinNode.execute(); // 结果: 1

// 计算两点间角度
const atan2Node = new TrigonometricNode({
  functionType: 'atan2'
});
atan2Node.setInputValue('y', 1);
atan2Node.setInputValue('x', 1);
// 结果: π/4 (45度)

// 波形生成示例
const waveGenerator = new TrigonometricNode({
  functionType: 'sin',
  amplitude: 2.0,
  frequency: 0.5,
  phase: 0,
  offset: 1.0
});
```

**支持的函数类型**:
- `sin`, `cos`, `tan`: 基础三角函数
- `asin`, `acos`, `atan`, `atan2`: 反三角函数
- `sinh`, `cosh`, `tanh`: 双曲三角函数
- `sec`, `csc`, `cot`: 其他三角函数

#### VectorMathNode (向量数学节点)
**原理**: 执行2D/3D向量的各种数学运算
**用途**: 3D变换、物理计算、方向计算、向量分析

```typescript
// 向量长度计算
const vectorNode = new VectorMathNode({
  operation: 'length'
});
vectorNode.setInputValue('vector', new Vector3(3, 4, 0));
const length = vectorNode.execute(); // 结果: 5

// 向量归一化
const normalizeNode = new VectorMathNode({
  operation: 'normalize'
});
normalizeNode.setInputValue('vector', new Vector3(10, 0, 0));
// 结果: Vector3(1, 0, 0)

// 向量点积
const dotProduct = new VectorMathNode({
  operation: 'dot'
});
dotProduct.setInputValue('vectorA', new Vector3(1, 2, 3));
dotProduct.setInputValue('vectorB', new Vector3(4, 5, 6));
// 结果: 32

// 向量叉积
const crossProduct = new VectorMathNode({
  operation: 'cross'
});
crossProduct.setInputValue('vectorA', new Vector3(1, 0, 0));
crossProduct.setInputValue('vectorB', new Vector3(0, 1, 0));
// 结果: Vector3(0, 0, 1)
```

**支持的向量操作**:
- `length`: 计算向量长度
- `normalize`: 向量归一化
- `dot`: 点积计算
- `cross`: 叉积计算
- `distance`: 两点间距离
- `angle`: 两向量间夹角
- `lerp`: 线性插值
- `slerp`: 球面线性插值

#### RandomNode (随机数节点)
**原理**: 生成指定范围的随机数，支持种子设置
**用途**: 随机效果、程序化生成、游戏逻辑、噪声生成

```typescript
// 基础随机数
const randomNode = new RandomNode({
  min: 0,
  max: 100,
  seed: 12345, // 可选种子，确保可重现性
  type: 'float'
});

// 整数随机数
const intRandom = new RandomNode({
  min: 1,
  max: 6,
  type: 'integer'
});

// 随机向量
const vectorRandom = new RandomNode({
  type: 'vector3',
  min: new Vector3(-1, -1, -1),
  max: new Vector3(1, 1, 1)
});

// 权重随机选择
const weightedRandom = new RandomNode({
  type: 'weighted',
  options: [
    {value: 'common', weight: 70},
    {value: 'rare', weight: 25},
    {value: 'epic', weight: 5}
  ]
});
```

#### InterpolationNode (插值节点)
**原理**: 在两个值之间进行线性或非线性插值
**用途**: 动画过渡、平滑变化、缓动效果、渐变计算

```typescript
// 线性插值
const lerpNode = new InterpolationNode({
  type: 'linear',
  startValue: 0,
  endValue: 100,
  factor: 0.5
});
// 结果: 50

// 缓动插值
const easingNode = new InterpolationNode({
  type: 'easeInOut',
  startValue: 0,
  endValue: 100,
  factor: 0.5,
  easingFunction: 'cubic'
});

// 颜色插值
const colorLerp = new InterpolationNode({
  type: 'color',
  startValue: '#ff0000', // 红色
  endValue: '#0000ff',   // 蓝色
  factor: 0.5
});
// 结果: '#800080' (紫色)
```

## AI节点系统使用详解 (46个节点)

### AI模型节点 (AIModelNodes.ts - 12个节点)

#### LoadAIModelNode (加载AI模型节点)
**原理**: 从指定路径加载AI模型到内存，支持多种模型格式
**用途**: 模型初始化、资源管理、模型切换

```typescript
// 基础模型加载
const loadModel = new LoadAIModelNode({
  modelPath: './models/chatbot.onnx',
  modelType: 'onnx',
  deviceType: 'gpu', // 'cpu', 'gpu', 'auto'
  maxMemory: '2GB'
});

// 多模型管理
const modelManager = new LoadAIModelNode({
  models: [
    {id: 'nlp', path: './models/nlp.onnx', priority: 'high'},
    {id: 'vision', path: './models/vision.pt', priority: 'medium'},
    {id: 'audio', path: './models/audio.tflite', priority: 'low'}
  ],
  loadStrategy: 'lazy' // 'eager', 'lazy', 'on-demand'
});
```

#### TextGenerationNode (文本生成节点)
**原理**: 使用语言模型生成文本内容，支持上下文理解
**用途**: 对话生成、内容创作、自动回复、文章写作

```typescript
// 基础文本生成
const textGen = new TextGenerationNode({
  model: 'gpt-3.5-turbo',
  prompt: '请写一篇关于人工智能的短文',
  maxTokens: 500,
  temperature: 0.7,
  topP: 0.9
});

// 对话生成
const chatGen = new TextGenerationNode({
  model: 'chat-model',
  messages: [
    {role: 'system', content: '你是一个友好的AI助手'},
    {role: 'user', content: '你好，请介绍一下自己'}
  ],
  contextWindow: 4096
});
```

#### SpeechRecognitionNode (语音识别节点)
**原理**: 将音频信号转换为文本，支持多语言识别
**用途**: 语音输入、语音控制、语音转录、实时字幕

```typescript
// 实时语音识别
const speechRecognition = new SpeechRecognitionNode({
  language: 'zh-CN',
  continuous: true,
  interimResults: true,
  maxAlternatives: 3
});

// 设置音频输入
speechRecognition.setInputValue('audioStream', microphoneStream);

// 处理识别结果
speechRecognition.onResult = (result) => {
  console.log('识别结果:', result.transcript);
  console.log('置信度:', result.confidence);
};

// 离线语音识别
const offlineSpeech = new SpeechRecognitionNode({
  model: 'whisper-base',
  audioFile: './audio/speech.wav',
  language: 'auto-detect',
  outputFormat: 'srt' // 字幕格式
});
```

#### SpeechSynthesisNode (语音合成节点)
**原理**: 将文本转换为自然流畅的语音
**用途**: 语音播报、数字人对话、无障碍访问、语音助手

```typescript
// 基础语音合成
const tts = new SpeechSynthesisNode({
  text: '欢迎使用DL引擎视觉脚本系统',
  voice: 'zh-CN-XiaoxiaoNeural',
  rate: 1.0,
  pitch: 1.0,
  volume: 0.8
});

// 情感语音合成
const emotionalTTS = new SpeechSynthesisNode({
  text: '今天天气真好！',
  voice: 'zh-CN-XiaoxiaoNeural',
  emotion: 'happy',
  intensity: 0.7,
  prosody: {
    rate: 'medium',
    pitch: '+10%',
    volume: 'loud'
  }
});

// SSML语音合成
const ssmlTTS = new SpeechSynthesisNode({
  ssml: `
    <speak>
      <prosody rate="slow" pitch="low">
        这是一段<emphasis level="strong">重要</emphasis>的内容。
      </prosody>
      <break time="1s"/>
      <prosody rate="fast" pitch="high">
        这是另一段内容。
      </prosody>
    </speak>
  `,
  outputFormat: 'wav'
});
```

### 自然语言处理节点 (AINLPNodes.ts - 14个节点)

#### DialogueManagementNode (对话管理节点)
**原理**: 管理多轮对话的上下文、状态和流程
**用途**: 聊天机器人、智能客服、交互式对话系统

```typescript
// 多轮对话管理
const dialogueManager = new DialogueManagementNode({
  id: 'dialogue_1',
  contextWindow: 10, // 保持最近10轮对话
  memoryType: 'sliding_window'
});

// 设置对话上下文
dialogueManager.setInputValue('userInput', '今天天气怎么样？');
dialogueManager.setInputValue('context', {
  location: '北京',
  user_preferences: ['简洁回答', '包含温度']
});
dialogueManager.setInputValue('knowledgeBase', weatherKnowledgeBase);

// 生成智能回复
const response = await dialogueManager.executeAsync();
console.log('AI回复:', response.text);
console.log('置信度:', response.confidence);
console.log('意图识别:', response.intent);
```

#### IntentRecognitionNode (意图识别节点)
**原理**: 识别用户输入文本的意图和目的
**用途**: 智能客服、语音助手、对话系统、命令解析

```typescript
// 意图识别配置
const intentRecognition = new IntentRecognitionNode({
  model: 'intent-classifier-v2',
  intents: [
    'weather_query',
    'booking_request',
    'complaint',
    'information_request',
    'greeting'
  ],
  confidenceThreshold: 0.7
});

// 识别用户意图
intentRecognition.setInputValue('text', '我想预订明天的会议室');
const result = await intentRecognition.executeAsync();
// 结果: {intent: 'booking_request', confidence: 0.95, entities: ['明天', '会议室']}
```

#### LanguageTranslationNode (语言翻译节点)
**原理**: 使用神经机器翻译技术进行多语言翻译
**用途**: 多语言支持、国际化应用、跨语言交流

```typescript
// 多语言翻译
const translator = new LanguageTranslationNode({
  sourceLanguage: 'zh-CN',
  targetLanguage: 'en-US',
  model: 'neural-mt-v3',
  preserveFormatting: true
});

translator.setInputValue('text', '人工智能正在改变我们的世界');
const translation = await translator.executeAsync();
// 结果: "Artificial intelligence is changing our world"

// 批量翻译
const batchTranslator = new LanguageTranslationNode({
  sourceLanguage: 'auto-detect',
  targetLanguages: ['en', 'ja', 'ko', 'fr'],
  texts: ['你好', '谢谢', '再见']
});
```

### 情感计算节点 (AIEmotionNodes.ts - 8个节点)

#### EmotionAnalysisNode (情感分析节点)
**原理**: 分析文本、语音或图像中的情感倾向和强度
**用途**: 情感监测、用户体验分析、智能交互、心理健康评估

```typescript
// 文本情感分析
const emotionAnalysis = new EmotionAnalysisNode({
  inputType: 'text',
  model: 'emotion-bert-v2',
  emotions: ['joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust'],
  outputFormat: 'detailed'
});

emotionAnalysis.setInputValue('text', '今天的天气真是太棒了！');
const result = await emotionAnalysis.executeAsync();
// 结果: {primary: 'joy', confidence: 0.89, scores: {joy: 0.89, sadness: 0.02, ...}}

// 多模态情感分析
const multimodalEmotion = new EmotionAnalysisNode({
  inputTypes: ['text', 'audio', 'facial'],
  fusionStrategy: 'weighted_average',
  weights: {text: 0.4, audio: 0.4, facial: 0.2}
});
```

#### EmotionDrivenAnimationNode (情感驱动动画节点)
**原理**: 根据情感状态自动生成相应的动画表现
**用途**: 数字人表情、角色动画、情感可视化

```typescript
// 情感动画生成
const emotionAnimation = new EmotionDrivenAnimationNode({
  characterModel: 'digital_human_v1',
  emotionMapping: {
    'joy': 'smile_animation',
    'sadness': 'sad_expression',
    'anger': 'frown_animation',
    'surprise': 'surprised_look'
  },
  intensityRange: [0.1, 1.0],
  transitionDuration: 500 // 毫秒
});

emotionAnimation.setInputValue('emotion', 'joy');
emotionAnimation.setInputValue('intensity', 0.8);
const animationData = await emotionAnimation.executeAsync();
```

## 网络通信节点使用详解 (43个节点)

### 基础网络节点 (NetworkNodes.ts - 7个节点)

#### ConnectToServerNode (连接服务器节点)
**原理**: 建立与服务器的TCP/UDP网络连接
**用途**: 多人游戏、数据同步、远程控制、客户端通信

```typescript
// TCP连接示例
const tcpConnection = new ConnectToServerNode({
  protocol: 'tcp',
  host: 'game.example.com',
  port: 8080,
  timeout: 5000,
  retryAttempts: 3,
  keepAlive: true
});

// WebSocket连接示例
const wsConnection = new ConnectToServerNode({
  protocol: 'websocket',
  url: 'wss://api.example.com/ws',
  headers: {
    'Authorization': 'Bearer token123'
  },
  reconnectInterval: 1000,
  maxReconnectAttempts: 5
});

// 连接事件处理
tcpConnection.onConnected = () => {
  console.log('服务器连接成功');
};

tcpConnection.onError = (error) => {
  console.error('连接错误:', error);
};
```

#### SendNetworkMessageNode (发送网络消息节点)
**原理**: 通过已建立的网络连接发送数据包
**用途**: 数据传输、状态同步、命令发送、实时通信

```typescript
// 基础消息发送
const sendMessage = new SendNetworkMessageNode({
  connectionId: 'server_connection_1',
  messageType: 'json',
  compression: 'gzip',
  encryption: 'aes256'
});

// 发送游戏状态
sendMessage.setInputValue('data', {
  type: 'player_move',
  playerId: 'player_123',
  position: {x: 100, y: 200, z: 50},
  timestamp: Date.now()
});

// 批量消息发送
const batchSend = new SendNetworkMessageNode({
  batchMode: true,
  batchSize: 10,
  flushInterval: 100 // 100ms
});
```

### WebRTC节点 (WebRTCNodes.ts - 13个节点)

#### CreateWebRTCConnectionNode (创建WebRTC连接节点)
**原理**: 建立点对点的实时通信连接，支持NAT穿透
**用途**: 视频通话、实时协作、P2P数据传输、直播

```typescript
// 基础WebRTC连接
const webrtcConnection = new CreateWebRTCConnectionNode({
  configuration: {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      {
        urls: 'turn:turn.example.com:3478',
        username: 'user',
        credential: 'pass'
      }
    ],
    iceCandidatePoolSize: 10
  },
  dataChannelConfig: {
    ordered: true,
    maxRetransmits: 3
  }
});

// 高级配置
const advancedWebRTC = new CreateWebRTCConnectionNode({
  configuration: {
    iceServers: [...],
    bundlePolicy: 'balanced',
    rtcpMuxPolicy: 'require',
    iceCandidatePoolSize: 10
  },
  constraints: {
    offerToReceiveAudio: true,
    offerToReceiveVideo: true,
    voiceActivityDetection: false
  }
});
```

#### GetUserMediaNode (获取用户媒体节点)
**原理**: 访问用户的摄像头和麦克风设备
**用途**: 视频采集、音频录制、实时通信、直播

```typescript
// 基础媒体获取
const getUserMedia = new GetUserMediaNode({
  constraints: {
    video: {
      width: { min: 640, ideal: 1280, max: 1920 },
      height: { min: 480, ideal: 720, max: 1080 },
      frameRate: { ideal: 30, max: 60 }
    },
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    }
  },
  fallbackConstraints: {
    video: { width: 640, height: 480 },
    audio: true
  }
});

// 屏幕共享
const getDisplayMedia = new GetUserMediaNode({
  mediaType: 'display',
  constraints: {
    video: {
      cursor: 'always',
      displaySurface: 'monitor'
    },
    audio: {
      echoCancellation: false,
      noiseSuppression: false
    }
  }
});
```

### HTTP节点 (HTTPNodes.ts - 5个节点)

#### HTTPGetNode (HTTP GET请求节点)
**原理**: 发送HTTP GET请求获取数据
**用途**: 数据获取、API调用、资源下载

```typescript
// 基础GET请求
const httpGet = new HTTPGetNode({
  url: 'https://api.example.com/users',
  headers: {
    'Authorization': 'Bearer token123',
    'Accept': 'application/json',
    'User-Agent': 'DL-Engine/1.0'
  },
  timeout: 10000,
  retries: 3,
  retryDelay: 1000
});

// 带参数的GET请求
const parameterizedGet = new HTTPGetNode({
  baseUrl: 'https://api.example.com',
  endpoint: '/search',
  queryParams: {
    q: 'artificial intelligence',
    limit: 20,
    offset: 0,
    sort: 'relevance'
  },
  validateStatus: (status) => status >= 200 && status < 300
});

// 响应处理
httpGet.onSuccess = (response) => {
  console.log('请求成功:', response.data);
  console.log('状态码:', response.status);
  console.log('响应头:', response.headers);
};

httpGet.onError = (error) => {
  console.error('请求失败:', error.message);
  if (error.response) {
    console.error('错误状态:', error.response.status);
    console.error('错误数据:', error.response.data);
  }
};
```

#### HTTPPostNode (HTTP POST请求节点)
**原理**: 发送HTTP POST请求提交数据
**用途**: 数据提交、表单发送、API调用

```typescript
// 基础POST请求
const httpPost = new HTTPPostNode({
  url: 'https://api.example.com/users',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
  },
  data: {
    name: '张三',
    email: '<EMAIL>',
    age: 25
  },
  timeout: 15000
});

// 文件上传
const fileUpload = new HTTPPostNode({
  url: 'https://api.example.com/upload',
  headers: {
    'Content-Type': 'multipart/form-data'
  },
  data: new FormData(),
  onUploadProgress: (progressEvent) => {
    const progress = (progressEvent.loaded / progressEvent.total) * 100;
    console.log(`上传进度: ${progress}%`);
  }
});
```

## UI系统节点使用详解 (34个节点)

### 基础UI节点 (UINodes.ts - 14个节点)

#### CreateButtonNode (创建按钮节点)
**原理**: 在界面中创建可点击的按钮组件
**用途**: 用户交互、功能触发、界面控制、表单提交

```typescript
// 基础按钮创建
const buttonNode = new CreateButtonNode({
  id: 'button_1',
  text: '点击我',
  position: { x: 100, y: 50 },
  size: { width: 120, height: 40 },
  style: {
    backgroundColor: '#007bff',
    color: 'white',
    borderRadius: '4px',
    fontSize: '14px',
    fontWeight: 'bold'
  }
});

// 高级按钮配置
const advancedButton = new CreateButtonNode({
  id: 'advanced_button',
  text: '提交表单',
  icon: 'submit-icon',
  variant: 'primary',
  size: 'large',
  disabled: false,
  loading: false,
  tooltip: '点击提交表单数据',
  animation: {
    hover: 'scale',
    click: 'ripple'
  }
});

// 事件处理
buttonNode.onClick = (event) => {
  console.log('按钮被点击了！', event);
  // 执行业务逻辑
  submitForm();
};

buttonNode.onHover = (isHovering) => {
  if (isHovering) {
    buttonNode.setStyle({ backgroundColor: '#0056b3' });
  } else {
    buttonNode.setStyle({ backgroundColor: '#007bff' });
  }
};
```

**最佳实践**:
- 使用语义化的按钮文本，清楚表达按钮功能
- 为重要操作使用醒目的颜色和样式
- 添加适当的图标增强用户体验
- 实现加载状态和禁用状态的视觉反馈
- 确保按钮在不同设备上的可访问性

#### CreateTextNode (创建文本节点)
**原理**: 在界面中显示静态或动态文本内容
**用途**: 信息展示、标签显示、内容呈现、状态显示

```typescript
// 基础文本显示
const textNode = new CreateTextNode({
  id: 'text_1',
  content: '欢迎使用DL引擎',
  position: { x: 50, y: 20 },
  style: {
    fontSize: '18px',
    color: '#333333',
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center'
  }
});

// 动态文本更新
const dynamicText = new CreateTextNode({
  id: 'dynamic_text',
  content: '当前时间: ${currentTime}',
  variables: {
    currentTime: () => new Date().toLocaleTimeString()
  },
  updateInterval: 1000, // 每秒更新
  animation: {
    type: 'fadeIn',
    duration: 300
  }
});

// 富文本支持
const richText = new CreateTextNode({
  id: 'rich_text',
  content: '<strong>重要提示:</strong> 请仔细阅读<em>使用说明</em>',
  allowHTML: true,
  maxLength: 200,
  ellipsis: true
});
```

#### CreateInputNode (创建输入框节点)
**原理**: 创建用户文本输入组件
**用途**: 数据输入、表单填写、搜索框、用户交互

```typescript
// 基础输入框
const inputNode = new CreateInputNode({
  id: 'input_1',
  type: 'text',
  placeholder: '请输入您的姓名',
  position: { x: 100, y: 100 },
  size: { width: 200, height: 32 },
  style: {
    border: '1px solid #ccc',
    borderRadius: '4px',
    padding: '8px'
  }
});

// 高级输入框配置
const advancedInput = new CreateInputNode({
  id: 'email_input',
  type: 'email',
  label: '电子邮箱',
  placeholder: '<EMAIL>',
  required: true,
  validation: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的电子邮箱地址'
  },
  autocomplete: 'email',
  maxLength: 100
});

// 密码输入框
const passwordInput = new CreateInputNode({
  id: 'password_input',
  type: 'password',
  label: '密码',
  placeholder: '请输入密码',
  showToggle: true, // 显示/隐藏密码切换
  strength: true,   // 显示密码强度
  validation: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true
  }
});

// 事件处理
inputNode.onChange = (value) => {
  console.log('输入值变化:', value);
  validateInput(value);
};

inputNode.onFocus = () => {
  console.log('输入框获得焦点');
};

inputNode.onBlur = () => {
  console.log('输入框失去焦点');
  validateAndSave();
};
```

#### CreateSliderNode (创建滑块节点)
**原理**: 创建数值选择滑块组件
**用途**: 数值调整、参数控制、音量控制、进度设置

```typescript
// 基础滑块
const sliderNode = new CreateSliderNode({
  id: 'slider_1',
  min: 0,
  max: 100,
  value: 50,
  step: 1,
  orientation: 'horizontal',
  position: { x: 100, y: 150 },
  size: { width: 200, height: 20 }
});

// 音量控制滑块
const volumeSlider = new CreateSliderNode({
  id: 'volume_slider',
  min: 0,
  max: 1,
  value: 0.5,
  step: 0.01,
  label: '音量',
  showValue: true,
  valueFormat: (value) => `${Math.round(value * 100)}%`,
  marks: [
    { value: 0, label: '静音' },
    { value: 0.5, label: '50%' },
    { value: 1, label: '最大' }
  ]
});

// 范围滑块
const rangeSlider = new CreateSliderNode({
  id: 'range_slider',
  type: 'range',
  min: 0,
  max: 1000,
  value: [200, 800],
  step: 10,
  label: '价格范围',
  showValue: true,
  valueFormat: (value) => `¥${value[0]} - ¥${value[1]}`
});

// 事件处理
sliderNode.onChange = (value) => {
  console.log('滑块值变化:', value);
  updateParameter(value);
};

sliderNode.onChangeComplete = (value) => {
  console.log('滑块调整完成:', value);
  saveSettings(value);
};
```

### 高级UI节点 (AdvancedUINodes.ts - 6个节点)

#### CreateDataGridNode (创建数据表格节点)
**原理**: 创建功能丰富的数据表格组件
**用途**: 数据展示、表格编辑、信息管理、数据分析

```typescript
// 基础数据表格
const dataGrid = new CreateDataGridNode({
  id: 'data_grid_1',
  columns: [
    {
      field: 'id',
      headerName: 'ID',
      width: 70,
      type: 'number',
      sortable: true
    },
    {
      field: 'name',
      headerName: '姓名',
      width: 130,
      editable: true,
      required: true
    },
    {
      field: 'email',
      headerName: '邮箱',
      width: 200,
      type: 'email',
      validation: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    {
      field: 'age',
      headerName: '年龄',
      width: 90,
      type: 'number',
      editable: true,
      min: 0,
      max: 120
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 150,
      sortable: false,
      renderCell: (params) => (
        `<button onclick="editRow(${params.row.id})">编辑</button>
         <button onclick="deleteRow(${params.row.id})">删除</button>`
      )
    }
  ],
  rows: [
    { id: 1, name: '张三', email: '<EMAIL>', age: 25 },
    { id: 2, name: '李四', email: '<EMAIL>', age: 30 },
    { id: 3, name: '王五', email: '<EMAIL>', age: 28 }
  ],
  features: {
    pagination: true,
    sorting: true,
    filtering: true,
    searching: true,
    selection: 'multiple',
    export: ['csv', 'excel', 'pdf']
  }
});

// 高级表格配置
const advancedGrid = new CreateDataGridNode({
  id: 'advanced_grid',
  dataSource: {
    type: 'api',
    url: '/api/users',
    method: 'GET',
    pagination: 'server',
    sorting: 'server',
    filtering: 'server'
  },
  virtualScrolling: true,
  rowHeight: 'auto',
  grouping: {
    enabled: true,
    defaultGroupBy: ['department']
  },
  aggregation: {
    enabled: true,
    functions: ['sum', 'avg', 'count']
  },
  toolbar: {
    enabled: true,
    buttons: ['add', 'edit', 'delete', 'export', 'refresh']
  }
});

// 事件处理
dataGrid.onRowClick = (row) => {
  console.log('行被点击:', row);
  showRowDetails(row);
};

dataGrid.onCellEdit = (params) => {
  console.log('单元格编辑:', params);
  validateAndSave(params);
};

dataGrid.onSelectionChange = (selectedRows) => {
  console.log('选择变化:', selectedRows);
  updateToolbarState(selectedRows);
};
```

#### CreateTreeViewNode (创建树形视图节点)
**原理**: 创建层次化的树形结构显示组件
**用途**: 文件浏览、层级导航、分类展示、组织结构

```typescript
// 基础树形视图
const treeView = new CreateTreeViewNode({
  id: 'tree_view_1',
  data: [
    {
      id: '1',
      label: '根目录',
      icon: 'folder',
      expanded: true,
      children: [
        {
          id: '1-1',
          label: '文档',
          icon: 'folder',
          children: [
            { id: '1-1-1', label: '说明.txt', icon: 'file' },
            { id: '1-1-2', label: '手册.pdf', icon: 'pdf' }
          ]
        },
        {
          id: '1-2',
          label: '图片',
          icon: 'folder',
          children: [
            { id: '1-2-1', label: 'logo.png', icon: 'image' },
            { id: '1-2-2', label: 'banner.jpg', icon: 'image' }
          ]
        }
      ]
    }
  ],
  features: {
    checkboxes: true,
    dragDrop: true,
    search: true,
    contextMenu: true
  }
});

// 动态加载树形视图
const dynamicTree = new CreateTreeViewNode({
  id: 'dynamic_tree',
  lazyLoading: true,
  loadChildren: async (node) => {
    const response = await fetch(`/api/tree/${node.id}/children`);
    return response.json();
  },
  virtualScrolling: true,
  maxHeight: 400
});

// 事件处理
treeView.onNodeClick = (node) => {
  console.log('节点被点击:', node);
  if (node.type === 'file') {
    openFile(node);
  }
};

treeView.onNodeExpand = (node) => {
  console.log('节点展开:', node);
  loadChildrenIfNeeded(node);
};

treeView.onNodeCheck = (node, checked) => {
  console.log('节点选择状态变化:', node, checked);
  updateSelectionState(node, checked);
};
```

## 物理系统节点使用详解 (22个节点)

### 刚体物理节点 (PhysicsNodes.ts - 12个节点)

#### CreatePhysicsBodyNode (创建物理体节点)
**原理**: 为实体创建物理属性和行为
**用途**: 物理模拟、重力效果、碰撞响应、运动控制

```typescript
// 基础物理体创建
const physicsBody = new CreatePhysicsBodyNode({
  id: 'physics_body_1',
  entity: targetEntity,
  bodyType: 'dynamic', // 'static', 'kinematic', 'dynamic'
  mass: 1.0,
  friction: 0.5,
  restitution: 0.3, // 弹性系数
  linearDamping: 0.1,
  angularDamping: 0.1
});

// 复杂物理体配置
const complexPhysicsBody = new CreatePhysicsBodyNode({
  entity: carEntity,
  bodyType: 'dynamic',
  mass: 1500, // 汽车质量
  centerOfMass: new Vector3(0, -0.5, 0),
  material: {
    friction: 0.7,
    restitution: 0.1,
    density: 2.5
  },
  constraints: {
    freezeRotationX: false,
    freezeRotationY: true,
    freezeRotationZ: false,
    freezePositionY: false
  },
  collisionDetection: 'continuous'
});

// 物理体组合
const compoundBody = new CreatePhysicsBodyNode({
  entity: robotEntity,
  bodyType: 'dynamic',
  shapes: [
    {
      type: 'box',
      size: new Vector3(1, 2, 0.5),
      position: new Vector3(0, 1, 0),
      material: 'metal'
    },
    {
      type: 'sphere',
      radius: 0.3,
      position: new Vector3(0, 2.3, 0),
      material: 'plastic'
    }
  ]
});
```

**最佳实践**:
- 根据对象的真实物理特性设置质量和材质
- 合理设置阻尼参数避免物体运动过于激烈
- 使用复合形状提高碰撞检测的精确度
- 对于不需要物理交互的装饰性对象使用静态类型

#### ApplyForceNode (施加力节点)
**原理**: 对物理体施加力或冲量，影响物体运动
**用途**: 推动物体、模拟风力、爆炸效果、重力模拟

```typescript
// 基础力的施加
const applyForce = new ApplyForceNode({
  id: 'apply_force_1',
  entity: targetEntity,
  force: new Vector3(0, 100, 0), // 向上100N的力
  position: new Vector3(0, 0, 0), // 施力点（相对于物体中心）
  mode: 'force', // 'force', 'impulse', 'velocity_change'
  space: 'world' // 'world', 'local'
});

// 爆炸效果
const explosionForce = new ApplyForceNode({
  entity: targetEntity,
  explosionCenter: new Vector3(0, 0, 0),
  explosionForce: 1000,
  explosionRadius: 10,
  upwardsModifier: 0.3, // 向上的额外力
  mode: 'impulse',
  falloffType: 'linear' // 'linear', 'inverse_square'
});

// 风力效果
const windForce = new ApplyForceNode({
  entity: leafEntity,
  force: new Vector3(5, 0, 0), // 水平风力
  mode: 'force',
  continuous: true,
  variation: {
    enabled: true,
    amplitude: 2.0,
    frequency: 0.5
  }
});

// 磁力效果
const magneticForce = new ApplyForceNode({
  entity: metalObject,
  targetPosition: magnetPosition,
  forceType: 'attraction',
  strength: 50,
  maxDistance: 5,
  falloffCurve: 'inverse_square'
});
```

#### RaycastNode (射线检测节点)
**原理**: 从指定点发射射线检测碰撞和交点
**用途**: 视线检测、点击检测、距离测量、碰撞检测

```typescript
// 基础射线检测
const raycast = new RaycastNode({
  id: 'raycast_1',
  origin: new Vector3(0, 1, 0),
  direction: new Vector3(0, -1, 0), // 向下的射线
  maxDistance: 10,
  layerMask: ['ground', 'obstacles'],
  hitTriggers: false
});

// 鼠标点击检测
const mouseRaycast = new RaycastNode({
  origin: cameraPosition,
  direction: mouseWorldDirection,
  maxDistance: 100,
  layerMask: ['clickable'],
  returnMultipleHits: false,
  sortByDistance: true
});

// 视线检测
const lineOfSight = new RaycastNode({
  origin: playerEyePosition,
  direction: playerLookDirection,
  maxDistance: 50,
  layerMask: ['enemies', 'walls'],
  ignoreEntities: [playerEntity],
  debugDraw: true,
  debugColor: '#ff0000'
});

// 地面检测
const groundCheck = new RaycastNode({
  origin: playerPosition,
  direction: Vector3.down,
  maxDistance: 0.1,
  layerMask: ['ground'],
  continuous: true,
  onHit: (hitInfo) => {
    console.log('检测到地面:', hitInfo.point);
    playerController.setGrounded(true);
  },
  onMiss: () => {
    playerController.setGrounded(false);
  }
});
```

#### CollisionDetectionNode (碰撞检测节点)
**原理**: 检测两个物理体之间的碰撞
**用途**: 碰撞响应、触发事件、物理交互

```typescript
// 基础碰撞检测
const collisionDetection = new CollisionDetectionNode({
  id: 'collision_1',
  entityA: playerEntity,
  entityB: enemyEntity,
  detectionType: 'continuous',
  precision: 'high'
});

// 触发器碰撞
const triggerCollision = new CollisionDetectionNode({
  entity: playerEntity,
  triggerZone: checkpointTrigger,
  onEnter: (otherEntity) => {
    console.log('玩家进入检查点');
    saveGame();
  },
  onExit: (otherEntity) => {
    console.log('玩家离开检查点');
  },
  onStay: (otherEntity) => {
    // 持续在触发区域内
    healPlayer(1);
  }
});

// 多对象碰撞检测
const multiCollision = new CollisionDetectionNode({
  entities: [bullet1, bullet2, bullet3],
  targets: enemyEntities,
  onCollision: (bullet, enemy) => {
    console.log('子弹击中敌人');
    dealDamage(enemy, bullet.damage);
    destroyEntity(bullet);
  },
  filterCallback: (bullet, enemy) => {
    // 自定义过滤逻辑
    return bullet.team !== enemy.team;
  }
});
```

### 软体物理节点 (SoftBodyNodes.ts - 5个节点)

#### CreateClothNode (创建布料节点)
**原理**: 创建基于质点弹簧系统的可变形布料
**用途**: 服装模拟、旗帜效果、窗帘动画、织物表现

```typescript
// 基础布料创建
const cloth = new CreateClothNode({
  id: 'cloth_1',
  width: 2.0,
  height: 2.0,
  resolutionX: 20,
  resolutionY: 20,
  mass: 1.0,
  stiffness: 0.8,
  damping: 0.1
});

// 旗帜效果
const flag = new CreateClothNode({
  width: 3.0,
  height: 2.0,
  resolutionX: 30,
  resolutionY: 20,
  material: {
    stiffness: 0.6,
    bendingStiffness: 0.3,
    damping: 0.2
  },
  constraints: {
    fixedPoints: [
      {x: 0, y: 0}, // 左上角固定
      {x: 0, y: 20} // 左下角固定
    ]
  },
  wind: {
    enabled: true,
    direction: new Vector3(1, 0, 0),
    strength: 5.0,
    turbulence: 0.3
  }
});

// 窗帘模拟
const curtain = new CreateClothNode({
  width: 4.0,
  height: 3.0,
  resolutionX: 40,
  resolutionY: 30,
  material: {
    stiffness: 0.4,
    damping: 0.3,
    thickness: 0.01
  },
  constraints: {
    fixedPoints: generateTopRowPoints(40), // 顶部一排固定点
    tearable: true,
    tearThreshold: 100
  },
  selfCollision: true
});
```

#### CreateRopeNode (创建绳索节点)
**原理**: 创建由连接粒子组成的柔性绳索
**用途**: 绳索模拟、链条效果、吊桥、缆线系统

```typescript
// 基础绳索
const rope = new CreateRopeNode({
  id: 'rope_1',
  startPoint: new Vector3(0, 5, 0),
  endPoint: new Vector3(0, 0, 0),
  segments: 20,
  mass: 0.1,
  stiffness: 0.9,
  damping: 0.1
});

// 吊桥绳索
const bridgeRope = new CreateRopeNode({
  startPoint: new Vector3(-5, 3, 0),
  endPoint: new Vector3(5, 3, 0),
  segments: 50,
  material: {
    stiffness: 0.8,
    damping: 0.2,
    breakingForce: 1000
  },
  attachments: [
    {segment: 0, entity: leftPillar, type: 'fixed'},
    {segment: 49, entity: rightPillar, type: 'fixed'},
    {segment: 25, entity: bridgeDeck, type: 'hinge'}
  ],
  sag: 0.5 // 自然下垂
});

// 攀爬绳索
const climbingRope = new CreateRopeNode({
  startPoint: cliffTop,
  endPoint: cliffBottom,
  segments: 30,
  material: {
    stiffness: 0.95,
    damping: 0.05,
    friction: 0.8
  },
  interaction: {
    climbable: true,
    gripPoints: 'all_segments',
    swingable: true
  },
  visualization: {
    thickness: 0.05,
    texture: 'rope_texture',
    segments_visible: true
  }
});
```

### 流体模拟节点 (FluidSimulationNodes.ts - 5个节点)

#### FluidSimulatorNode (流体模拟器节点)
**原理**: 基于粒子系统的流体动力学模拟
**用途**: 水体模拟、液体效果、流体交互、物理仿真

```typescript
// 基础流体模拟
const fluidSimulator = new FluidSimulatorNode({
  id: 'fluid_sim_1',
  particleCount: 1000,
  viscosity: 0.1,
  density: 1000,
  surfaceTension: 0.0728,
  gravity: new Vector3(0, -9.81, 0),
  containerBounds: {
    min: new Vector3(-5, 0, -5),
    max: new Vector3(5, 10, 5)
  }
});

// 高级流体配置
const advancedFluid = new FluidSimulatorNode({
  particleCount: 5000,
  solverType: 'SPH', // Smoothed Particle Hydrodynamics
  kernelRadius: 0.1,
  restDensity: 1000,
  stiffness: 200,
  viscosity: 0.05,
  damping: 0.99,
  collisionResponse: {
    enabled: true,
    restitution: 0.3,
    friction: 0.1
  },
  rendering: {
    metaballs: true,
    surfaceReconstruction: 'marching_cubes',
    transparency: 0.8
  }
});
```

#### FluidForceFieldNode (流体力场节点)
**原理**: 对流体施加外部力场影响
**用途**: 重力模拟、风力效果、磁场影响、流体控制

```typescript
// 重力场
const gravityField = new FluidForceFieldNode({
  type: 'gravity',
  strength: 9.81,
  direction: new Vector3(0, -1, 0),
  affectRadius: 100,
  falloffType: 'none'
});

// 风力场
const windField = new FluidForceFieldNode({
  type: 'wind',
  strength: 5.0,
  direction: new Vector3(1, 0, 0),
  turbulence: {
    enabled: true,
    frequency: 0.1,
    amplitude: 2.0
  },
  affectRadius: 20,
  falloffType: 'linear'
});

// 漩涡场
const vortexField = new FluidForceFieldNode({
  type: 'vortex',
  center: new Vector3(0, 5, 0),
  axis: new Vector3(0, 1, 0),
  strength: 10.0,
  radius: 3.0,
  falloffType: 'inverse_square'
});
```

## 输入系统节点使用详解 (6个节点)

### 输入节点 (InputNodes.ts - 6个节点)

#### KeyboardInputNode (键盘输入节点)
**原理**: 检测和处理键盘按键事件
**用途**: 游戏控制、快捷键、文本输入、界面导航

```typescript
// 基础键盘输入
const keyboardInput = new KeyboardInputNode({
  id: 'keyboard_1',
  keys: ['W', 'A', 'S', 'D'],
  eventType: 'keydown', // 'keydown', 'keyup', 'keypress'
  preventDefault: true,
  repeatDelay: 100
});

// 组合键检测
const comboKeys = new KeyboardInputNode({
  combinations: [
    {keys: ['Ctrl', 'S'], action: 'save'},
    {keys: ['Ctrl', 'Z'], action: 'undo'},
    {keys: ['Ctrl', 'Shift', 'Z'], action: 'redo'},
    {keys: ['Alt', 'F4'], action: 'close'}
  ],
  globalCapture: true,
  caseSensitive: false
});

// 游戏控制映射
const gameControls = new KeyboardInputNode({
  mapping: {
    'W': 'move_forward',
    'S': 'move_backward',
    'A': 'move_left',
    'D': 'move_right',
    'Space': 'jump',
    'Shift': 'run',
    'E': 'interact'
  },
  deadzone: 0.1,
  sensitivity: 1.0
});
```

#### MouseInputNode (鼠标输入节点)
**原理**: 处理鼠标移动、点击和滚轮事件
**用途**: 界面交互、相机控制、物体选择、绘图操作

```typescript
// 基础鼠标输入
const mouseInput = new MouseInputNode({
  id: 'mouse_1',
  events: ['click', 'move', 'wheel'],
  button: 'left', // 'left', 'right', 'middle', 'all'
  sensitivity: 1.0,
  invertY: false
});

// 鼠标拖拽
const mouseDrag = new MouseInputNode({
  dragEnabled: true,
  dragThreshold: 5, // 像素
  dragButton: 'left',
  onDragStart: (event) => {
    console.log('开始拖拽:', event.position);
  },
  onDrag: (event) => {
    console.log('拖拽中:', event.delta);
  },
  onDragEnd: (event) => {
    console.log('拖拽结束:', event.totalDelta);
  }
});

// 相机控制
const cameraControl = new MouseInputNode({
  mode: 'camera_control',
  sensitivity: {
    rotation: 0.5,
    zoom: 0.1,
    pan: 1.0
  },
  constraints: {
    minZoom: 1.0,
    maxZoom: 10.0,
    verticalAngleLimit: 89
  },
  smoothing: 0.1
});
```

#### TouchInputNode (触摸输入节点)
**原理**: 处理触摸屏的单点和多点触摸事件
**用途**: 移动设备交互、手势识别、多点触控、触摸绘图

```typescript
// 基础触摸输入
const touchInput = new TouchInputNode({
  id: 'touch_1',
  maxTouches: 2,
  gestureRecognition: true,
  preventDefault: true,
  touchRadius: 20
});

// 多点触摸手势
const multiTouch = new TouchInputNode({
  gestures: {
    pinch: {
      enabled: true,
      minDistance: 50,
      onPinch: (scale, center) => {
        console.log('缩放手势:', scale, center);
      }
    },
    rotate: {
      enabled: true,
      minAngle: 5,
      onRotate: (angle, center) => {
        console.log('旋转手势:', angle, center);
      }
    },
    swipe: {
      enabled: true,
      minDistance: 100,
      maxTime: 500,
      onSwipe: (direction, velocity) => {
        console.log('滑动手势:', direction, velocity);
      }
    }
  }
});

// 触摸绘图
const touchDraw = new TouchInputNode({
  mode: 'drawing',
  brushSize: 5,
  pressure: true,
  smoothing: 0.5,
  onStroke: (points) => {
    console.log('绘制笔画:', points);
  }
});
```

#### GamepadInputNode (游戏手柄输入节点)
**原理**: 检测和处理游戏手柄的按键和摇杆输入
**用途**: 游戏控制、手柄映射、振动反馈、多玩家输入

```typescript
// 基础手柄输入
const gamepadInput = new GamepadInputNode({
  id: 'gamepad_1',
  playerIndex: 0,
  deadzone: 0.15,
  vibrationEnabled: true,
  autoConnect: true
});

// 手柄映射配置
const gamepadMapping = new GamepadInputNode({
  mapping: {
    buttons: {
      0: 'jump',      // A/X按钮
      1: 'attack',    // B/Circle按钮
      2: 'defend',    // X/Square按钮
      3: 'interact',  // Y/Triangle按钮
      9: 'pause'      // Start按钮
    },
    axes: {
      0: 'move_horizontal',    // 左摇杆X轴
      1: 'move_vertical',      // 左摇杆Y轴
      2: 'camera_horizontal',  // 右摇杆X轴
      3: 'camera_vertical'     // 右摇杆Y轴
    }
  },
  sensitivity: {
    movement: 1.0,
    camera: 0.8
  }
});

// 振动反馈
const hapticFeedback = new GamepadInputNode({
  vibration: {
    enabled: true,
    patterns: {
      'hit': {duration: 200, strongMagnitude: 0.8, weakMagnitude: 0.3},
      'explosion': {duration: 500, strongMagnitude: 1.0, weakMagnitude: 0.8},
      'heartbeat': {duration: 100, strongMagnitude: 0.5, weakMagnitude: 0.2, repeat: 2}
    }
  }
});
```

#### MotionCaptureInputNode (动作捕捉输入节点)
**原理**: 处理动作捕捉设备的骨骼和关节数据
**用途**: 动作捕捉、虚拟表演、动画制作、体感交互

```typescript
// 基础动作捕捉
const mocapInput = new MotionCaptureInputNode({
  id: 'mocap_1',
  deviceType: 'kinect', // 'kinect', 'leap_motion', 'vive_tracker'
  trackingMode: 'skeleton',
  smoothing: 0.3,
  confidenceThreshold: 0.7
});

// 全身骨骼跟踪
const skeletonTracking = new MotionCaptureInputNode({
  deviceType: 'kinect',
  joints: [
    'head', 'neck', 'spine_shoulder', 'spine_mid', 'spine_base',
    'shoulder_left', 'elbow_left', 'wrist_left', 'hand_left',
    'shoulder_right', 'elbow_right', 'wrist_right', 'hand_right',
    'hip_left', 'knee_left', 'ankle_left', 'foot_left',
    'hip_right', 'knee_right', 'ankle_right', 'foot_right'
  ],
  coordinateSystem: 'unity',
  filterNoise: true,
  predictiveTracking: true
});

// 手部精确跟踪
const handTracking = new MotionCaptureInputNode({
  deviceType: 'leap_motion',
  trackingMode: 'hands',
  handModel: 'detailed',
  fingerTracking: true,
  gestureRecognition: {
    enabled: true,
    gestures: ['point', 'grab', 'pinch', 'swipe', 'circle']
  },
  calibration: {
    autoCalibrate: true,
    userSpecific: true
  }
});
```

#### GestureRecognitionNode (手势识别节点)
**原理**: 识别预定义的手势模式和动作
**用途**: 手势控制、自然交互、无接触操作、智能识别

```typescript
// 基础手势识别
const gestureRecognition = new GestureRecognitionNode({
  id: 'gesture_1',
  inputSource: 'camera', // 'camera', 'depth_sensor', 'mocap'
  gestureLibrary: 'standard',
  recognitionMode: 'real_time',
  confidenceThreshold: 0.8
});

// 自定义手势库
const customGestures = new GestureRecognitionNode({
  customGestures: [
    {
      name: 'wave_hello',
      pattern: 'hand_up_wave_motion',
      duration: [1000, 3000],
      confidence: 0.7,
      action: 'greeting'
    },
    {
      name: 'thumbs_up',
      pattern: 'thumb_extended_fingers_closed',
      duration: [500, 2000],
      confidence: 0.9,
      action: 'approval'
    },
    {
      name: 'stop_gesture',
      pattern: 'palm_forward_fingers_extended',
      duration: [300, 1000],
      confidence: 0.85,
      action: 'stop'
    }
  ],
  learningMode: true,
  adaptiveThreshold: true
});

// 连续手势序列
const gestureSequence = new GestureRecognitionNode({
  sequenceMode: true,
  sequences: [
    {
      name: 'unlock_sequence',
      gestures: ['swipe_right', 'circle_clockwise', 'tap'],
      timeout: 5000,
      action: 'unlock_device'
    },
    {
      name: 'navigation_sequence',
      gestures: ['point_left', 'point_right', 'point_up', 'point_down'],
      allowPartial: true,
      action: 'navigate'
    }
  ],
  contextAware: true
});
```

## 动画系统节点使用详解 (21个节点)

### 基础动画节点 (AnimationNodes.ts - 8个节点)

#### PlayAnimationNode (播放动画节点)
**原理**: 播放指定的动画片段，支持循环和速度控制
**用途**: 角色动画、物体动画、UI动画、场景动画

```typescript
// 基础动画播放
const playAnimation = new PlayAnimationNode({
  id: 'play_anim_1',
  entity: characterEntity,
  animationName: 'walk',
  speed: 1.0,
  loop: true,
  fadeInTime: 0.2,
  startTime: 0
});

// 复杂动画控制
const complexAnimation = new PlayAnimationNode({
  entity: robotEntity,
  animationClip: walkAnimationClip,
  playbackSettings: {
    speed: 1.5,
    loop: true,
    pingPong: false,
    reverse: false
  },
  blending: {
    fadeInTime: 0.3,
    fadeOutTime: 0.2,
    blendMode: 'additive'
  },
  events: {
    onStart: () => console.log('动画开始'),
    onComplete: () => console.log('动画完成'),
    onLoop: () => console.log('动画循环')
  }
});

// 动画序列播放
const animationSequence = new PlayAnimationNode({
  entity: playerEntity,
  sequence: [
    {name: 'idle', duration: 2.0, loop: true},
    {name: 'walk', duration: 3.0, speed: 1.2},
    {name: 'run', duration: 2.0, speed: 0.8},
    {name: 'idle', duration: 1.0}
  ],
  autoAdvance: true,
  seamlessTransition: true
});
```

#### AnimationBlendNode (动画混合节点)
**原理**: 混合多个动画以创建平滑过渡和复合效果
**用途**: 动画过渡、复合动作、表情混合、状态融合

```typescript
// 基础动画混合
const animationBlend = new AnimationBlendNode({
  id: 'blend_1',
  entity: characterEntity,
  animations: [
    {name: 'walk', weight: 0.7},
    {name: 'run', weight: 0.3}
  ],
  blendMode: 'linear'
});

// 表情混合
const facialBlend = new AnimationBlendNode({
  entity: faceEntity,
  blendType: 'facial',
  expressions: [
    {name: 'happy', weight: 0.6},
    {name: 'surprised', weight: 0.4},
    {name: 'neutral', weight: 0.0}
  ],
  blendMode: 'additive',
  normalizeWeights: true
});

// 分层动画混合
const layeredBlend = new AnimationBlendNode({
  entity: characterEntity,
  layers: [
    {
      name: 'base_layer',
      animations: [{name: 'walk', weight: 1.0}],
      mask: 'full_body',
      weight: 1.0
    },
    {
      name: 'upper_body',
      animations: [{name: 'wave', weight: 1.0}],
      mask: 'upper_body_mask',
      weight: 0.8,
      blendMode: 'override'
    },
    {
      name: 'facial',
      animations: [{name: 'smile', weight: 1.0}],
      mask: 'face_mask',
      weight: 1.0,
      blendMode: 'additive'
    }
  ]
});
```

### 高级动画节点 (AdvancedAnimationNodes.ts - 5个节点)

#### IKSolverNode (IK求解器节点)
**原理**: 反向动力学计算，从目标位置计算关节角度
**用途**: 手部抓取、脚部着地、视线跟踪、精确定位

```typescript
// 基础IK求解
const ikSolver = new IKSolverNode({
  id: 'ik_solver_1',
  entity: characterEntity,
  chain: {
    startBone: 'shoulder',
    endBone: 'hand',
    bones: ['shoulder', 'upper_arm', 'forearm', 'hand']
  },
  target: targetPosition,
  iterations: 10,
  tolerance: 0.01
});

// 脚部IK（地面适应）
const footIK = new IKSolverNode({
  entity: characterEntity,
  ikType: 'two_bone', // 适用于腿部
  chain: {
    startBone: 'thigh',
    middleBone: 'shin',
    endBone: 'foot'
  },
  target: groundPosition,
  poleTarget: kneeDirection,
  constraints: {
    bendDirection: 'forward',
    maxAngle: 160,
    minAngle: 10
  },
  groundAdaptation: {
    enabled: true,
    raycastDistance: 1.0,
    smoothing: 0.1
  }
});

// 视线跟踪IK
const lookAtIK = new IKSolverNode({
  entity: characterEntity,
  ikType: 'look_at',
  chain: {
    bones: ['neck', 'head']
  },
  target: lookAtTarget,
  constraints: {
    maxHorizontalAngle: 90,
    maxVerticalAngle: 45,
    smoothing: 0.2
  },
  eyeTracking: {
    enabled: true,
    leftEye: 'left_eye_bone',
    rightEye: 'right_eye_bone',
    convergence: 0.1
  }
});
```

#### RetargetAnimationNode (动画重定向节点)
**原理**: 将动画从一个骨架映射到另一个骨架
**用途**: 动画复用、角色适配、动作迁移、跨模型动画

```typescript
// 基础动画重定向
const retargetAnimation = new RetargetAnimationNode({
  id: 'retarget_1',
  sourceRig: humanoidRig,
  targetRig: robotRig,
  animationClip: walkAnimation,
  mapping: {
    'human_spine': 'robot_torso',
    'human_left_arm': 'robot_left_arm',
    'human_right_arm': 'robot_right_arm',
    'human_left_leg': 'robot_left_leg',
    'human_right_leg': 'robot_right_leg'
  }
});

// 高级重定向配置
const advancedRetarget = new RetargetAnimationNode({
  sourceRig: mocapRig,
  targetRig: gameCharacterRig,
  animationClip: mocapAnimation,
  retargetingMode: 'skeleton',
  boneMapping: {
    autoMap: true,
    customMappings: {
      'mixamorig:Hips': 'root',
      'mixamorig:Spine': 'spine_01',
      'mixamorig:LeftShoulder': 'clavicle_l'
    }
  },
  scaling: {
    enabled: true,
    method: 'proportional',
    preserveFootPlacement: true
  },
  filtering: {
    positionFilter: 0.1,
    rotationFilter: 0.05,
    scaleFilter: 0.02
  }
});
```

## 音频系统节点使用详解 (13个节点)

### 音频节点 (AudioNodes.ts - 13个节点)

#### PlayAudioNode (播放音频节点)
**原理**: 播放指定的音频文件，支持多种格式
**用途**: 背景音乐、音效播放、语音播报、环境声音

```typescript
// 基础音频播放
const playAudio = new PlayAudioNode({
  id: 'audio_1',
  audioFile: './sounds/background_music.mp3',
  volume: 0.8,
  loop: true,
  autoPlay: true,
  fadeIn: 1.0
});

// 3D空间音频
const spatialAudio = new PlayAudioNode({
  audioFile: './sounds/footsteps.wav',
  spatialAudio: {
    enabled: true,
    position: playerPosition,
    maxDistance: 50,
    rolloffFactor: 1.0,
    dopplerFactor: 1.0
  },
  volume: 1.0,
  pitch: 1.0,
  loop: true
});

// 动态音频播放
const dynamicAudio = new PlayAudioNode({
  audioSources: [
    {file: './sounds/rain_light.wav', condition: 'weather === "light_rain"'},
    {file: './sounds/rain_heavy.wav', condition: 'weather === "heavy_rain"'},
    {file: './sounds/sunny.wav', condition: 'weather === "sunny"'}
  ],
  crossfade: {
    enabled: true,
    duration: 2.0
  },
  adaptiveVolume: {
    enabled: true,
    baseVolume: 0.7,
    environmentFactor: 0.3
  }
});
```

#### Audio3DNode (3D音频节点)
**原理**: 基于位置的空间音频效果和衰减
**用途**: 环境音效、距离衰减、方向性音频、沉浸体验

```typescript
// 基础3D音频
const audio3D = new Audio3DNode({
  id: 'audio_3d_1',
  audioSource: engineSoundSource,
  position: carPosition,
  velocity: carVelocity,
  settings: {
    minDistance: 1.0,
    maxDistance: 100.0,
    rolloffMode: 'logarithmic',
    dopplerLevel: 1.0
  }
});

// 方向性音频
const directionalAudio = new Audio3DNode({
  audioSource: speakerSource,
  position: speakerPosition,
  orientation: speakerDirection,
  cone: {
    innerAngle: 30,
    outerAngle: 90,
    outerGain: 0.3
  },
  settings: {
    minDistance: 0.5,
    maxDistance: 20.0,
    rolloffMode: 'linear'
  }
});

// 环境音频区域
const ambientZone = new Audio3DNode({
  audioSource: forestAmbienceSource,
  zone: {
    type: 'sphere',
    center: forestCenter,
    radius: 50,
    falloffCurve: 'smooth'
  },
  layering: {
    layers: [
      {source: 'birds.wav', volume: 0.6, frequency: 'high'},
      {source: 'wind.wav', volume: 0.4, frequency: 'low'},
      {source: 'leaves.wav', volume: 0.3, frequency: 'mid'}
    ],
    randomization: {
      enabled: true,
      volumeVariation: 0.2,
      pitchVariation: 0.1
    }
  }
});
```

#### AudioMixerNode (音频混合器节点)
**原理**: 混合多个音频源为单一输出
**用途**: 音频混音、多轨合成、音效叠加

```typescript
// 基础音频混合
const audioMixer = new AudioMixerNode({
  id: 'mixer_1',
  inputs: [
    {source: backgroundMusic, volume: 0.6, pan: 0},
    {source: dialogueAudio, volume: 0.8, pan: 0},
    {source: soundEffects, volume: 0.7, pan: 0}
  ],
  masterVolume: 1.0,
  outputFormat: 'stereo'
});

// 高级混音配置
const advancedMixer = new AudioMixerNode({
  channels: [
    {
      name: 'music',
      sources: [backgroundMusic, ambientMusic],
      effects: ['reverb', 'eq'],
      volume: 0.5,
      mute: false,
      solo: false
    },
    {
      name: 'sfx',
      sources: soundEffectSources,
      effects: ['compressor'],
      volume: 0.8,
      ducking: {
        enabled: true,
        trigger: 'dialogue',
        reduction: 0.3,
        attack: 0.1,
        release: 0.5
      }
    },
    {
      name: 'dialogue',
      sources: [voiceAudio],
      effects: ['noise_gate', 'eq'],
      volume: 1.0,
      priority: 'high'
    }
  ],
  masterEffects: ['limiter', 'master_eq'],
  routing: {
    outputBus: 'main',
    sendEffects: ['hall_reverb']
  }
});
```

## 数据处理节点系统使用详解 (28个节点)

### 文件系统节点 (FileSystemNodes.ts - 10个节点)

#### ReadTextFileNode (读取文本文件节点)
**原理**: 从文件系统读取文本文件内容
**用途**: 配置文件读取、数据导入、文档处理、日志分析

```typescript
// 基础文本文件读取
const readTextFile = new ReadTextFileNode({
  id: 'read_text_1',
  filePath: './config/settings.txt',
  encoding: 'utf-8',
  errorHandling: 'throw'
});

// 异步文件读取
const asyncReadFile = new ReadTextFileNode({
  filePath: './data/large_file.txt',
  async: true,
  chunkSize: 1024 * 1024, // 1MB chunks
  onProgress: (progress) => {
    console.log(`读取进度: ${progress}%`);
  },
  onComplete: (content) => {
    console.log('文件读取完成');
  }
});

// 条件文件读取
const conditionalRead = new ReadTextFileNode({
  filePath: './logs/app.log',
  conditions: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    checkExists: true,
    checkPermissions: true
  },
  fallbackContent: '文件不可用',
  cacheResult: true
});
```

#### WriteTextFileNode (写入文本文件节点)
**原理**: 将文本内容写入到文件系统
**用途**: 数据导出、日志记录、配置保存、报告生成

```typescript
// 基础文本文件写入
const writeTextFile = new WriteTextFileNode({
  id: 'write_text_1',
  filePath: './output/result.txt',
  content: '处理结果数据',
  encoding: 'utf-8',
  overwrite: true
});

// 追加模式写入
const appendToFile = new WriteTextFileNode({
  filePath: './logs/application.log',
  content: `${new Date().toISOString()} - 应用启动\n`,
  mode: 'append',
  createIfNotExists: true,
  backup: {
    enabled: true,
    maxSize: 50 * 1024 * 1024, // 50MB
    rotateCount: 5
  }
});

// 批量文件写入
const batchWrite = new WriteTextFileNode({
  files: [
    {path: './reports/summary.txt', content: summaryData},
    {path: './reports/details.txt', content: detailData},
    {path: './reports/errors.txt', content: errorData}
  ],
  atomic: true, // 原子操作
  permissions: '644',
  onSuccess: (filePath) => {
    console.log(`文件写入成功: ${filePath}`);
  }
});
```

#### CopyFileNode (复制文件节点)
**原理**: 复制文件或目录到指定位置
**用途**: 文件备份、数据迁移、模板复制、资源管理

```typescript
// 基础文件复制
const copyFile = new CopyFileNode({
  id: 'copy_1',
  sourcePath: './source/data.json',
  destinationPath: './backup/data_backup.json',
  overwrite: false,
  preserveTimestamps: true
});

// 目录递归复制
const copyDirectory = new CopyFileNode({
  sourcePath: './project/assets',
  destinationPath: './build/assets',
  recursive: true,
  filter: {
    include: ['*.png', '*.jpg', '*.json'],
    exclude: ['*.tmp', '*.log']
  },
  onProgress: (current, total) => {
    console.log(`复制进度: ${current}/${total}`);
  }
});

// 智能复制策略
const smartCopy = new CopyFileNode({
  sourcePath: './data',
  destinationPath: './backup',
  strategy: 'incremental', // 'full', 'incremental', 'differential'
  verification: {
    enabled: true,
    method: 'checksum',
    algorithm: 'md5'
  },
  compression: {
    enabled: true,
    level: 6
  }
});
```

#### DeleteFileNode (删除文件节点)
**原理**: 删除指定的文件或目录
**用途**: 清理临时文件、数据清理、空间管理、维护操作

```typescript
// 基础文件删除
const deleteFile = new DeleteFileNode({
  id: 'delete_1',
  filePath: './temp/cache.tmp',
  confirmDelete: true,
  moveToTrash: true
});

// 批量删除
const batchDelete = new DeleteFileNode({
  paths: [
    './temp/*.tmp',
    './logs/*.old',
    './cache/*'
  ],
  pattern: 'glob',
  dryRun: false,
  onBeforeDelete: (filePath) => {
    console.log(`准备删除: ${filePath}`);
    return true; // 确认删除
  }
});

// 条件删除
const conditionalDelete = new DeleteFileNode({
  basePath: './logs',
  conditions: {
    olderThan: 30 * 24 * 60 * 60 * 1000, // 30天
    largerThan: 100 * 1024 * 1024, // 100MB
    pattern: '*.log'
  },
  safeMode: true,
  backup: {
    enabled: true,
    location: './backup/deleted'
  }
});
```

### 高级文件系统节点 (AdvancedFileSystemNodes.ts - 6个节点)

#### WatchFileNode (监视文件节点)
**原理**: 监视文件或目录的变化事件
**用途**: 热重载、自动同步、变化检测、实时更新

```typescript
// 基础文件监视
const watchFile = new WatchFileNode({
  id: 'watch_1',
  path: './config/settings.json',
  events: ['change', 'rename', 'delete'],
  debounce: 100,
  recursive: false
});

// 目录监视
const watchDirectory = new WatchFileNode({
  path: './src',
  recursive: true,
  filter: {
    include: ['*.js', '*.ts', '*.json'],
    exclude: ['node_modules/**', '*.tmp']
  },
  events: ['add', 'change', 'unlink'],
  onEvent: (event, filePath) => {
    console.log(`文件${event}: ${filePath}`);
    if (event === 'change') {
      recompileProject();
    }
  }
});

// 智能监视
const smartWatch = new WatchFileNode({
  path: './project',
  intelligent: true,
  batchEvents: true,
  batchDelay: 500,
  ignoreInitial: true,
  followSymlinks: false,
  onBatch: (events) => {
    console.log(`批量处理${events.length}个文件变化`);
    processChanges(events);
  }
});
```

#### CompressFileNode (文件压缩节点)
**原理**: 压缩文件或目录为归档格式
**用途**: 数据压缩、文件打包、存储优化、传输准备

```typescript
// 基础文件压缩
const compressFile = new CompressFileNode({
  id: 'compress_1',
  sourcePath: './data',
  outputPath: './archive/data.zip',
  format: 'zip',
  compressionLevel: 6
});

// 高级压缩配置
const advancedCompress = new CompressFileNode({
  sourcePath: './project',
  outputPath: './releases/project_v1.0.tar.gz',
  format: 'tar.gz',
  options: {
    compressionLevel: 9,
    password: 'secure123',
    encryption: 'aes256'
  },
  filter: {
    exclude: ['node_modules/**', '*.log', '.git/**'],
    include: ['src/**', 'assets/**', 'package.json']
  },
  onProgress: (progress) => {
    console.log(`压缩进度: ${progress}%`);
  }
});

// 分卷压缩
const splitCompress = new CompressFileNode({
  sourcePath: './large_dataset',
  outputPath: './archive/dataset',
  format: 'zip',
  splitSize: 100 * 1024 * 1024, // 100MB per volume
  volumeNaming: 'sequential',
  verification: true
});
```

### 数据库节点 (DatabaseNodes.ts - 6个节点)

#### ConnectDatabaseNode (连接数据库节点)
**原理**: 建立与数据库的连接
**用途**: 数据库访问、连接管理、会话建立、资源初始化

```typescript
// MySQL连接
const mysqlConnection = new ConnectDatabaseNode({
  id: 'mysql_conn',
  type: 'mysql',
  host: 'localhost',
  port: 3306,
  database: 'app_db',
  username: 'user',
  password: 'password',
  options: {
    charset: 'utf8mb4',
    timezone: '+08:00',
    acquireTimeout: 60000,
    timeout: 60000
  }
});

// MongoDB连接
const mongoConnection = new ConnectDatabaseNode({
  type: 'mongodb',
  connectionString: 'mongodb://localhost:27017/app_db',
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000
  },
  authentication: {
    username: 'admin',
    password: 'password',
    authSource: 'admin'
  }
});

// 连接池配置
const pooledConnection = new ConnectDatabaseNode({
  type: 'postgresql',
  host: 'localhost',
  port: 5432,
  database: 'app_db',
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000
  }
});
```

#### ExecuteQueryNode (执行查询节点)
**原理**: 执行SQL查询或数据库操作
**用途**: 数据查询、数据操作、报表生成、数据分析

```typescript
// 基础查询
const executeQuery = new ExecuteQueryNode({
  id: 'query_1',
  connection: mysqlConnection,
  sql: 'SELECT * FROM users WHERE active = ?',
  parameters: [true],
  timeout: 30000
});

// 复杂查询
const complexQuery = new ExecuteQueryNode({
  connection: postgresConnection,
  sql: `
    SELECT u.id, u.name, u.email,
           COUNT(o.id) as order_count,
           SUM(o.total) as total_spent
    FROM users u
    LEFT JOIN orders o ON u.id = o.user_id
    WHERE u.created_at >= ?
    GROUP BY u.id, u.name, u.email
    ORDER BY total_spent DESC
    LIMIT ?
  `,
  parameters: ['2023-01-01', 100],
  resultFormat: 'object',
  streaming: true
});

// 批量查询
const batchQuery = new ExecuteQueryNode({
  connection: mysqlConnection,
  queries: [
    {sql: 'SELECT COUNT(*) as user_count FROM users'},
    {sql: 'SELECT COUNT(*) as order_count FROM orders'},
    {sql: 'SELECT AVG(total) as avg_order FROM orders'}
  ],
  transaction: true,
  onResult: (index, result) => {
    console.log(`查询${index + 1}完成:`, result);
  }
});
```

### 加密安全节点 (CryptographyNodes.ts - 6个节点)

#### AESEncryptNode (AES加密节点)
**原理**: 使用AES算法加密数据
**用途**: 数据保护、敏感信息加密、安全传输、隐私保护

```typescript
// 基础AES加密
const aesEncrypt = new AESEncryptNode({
  id: 'aes_encrypt_1',
  data: '敏感数据内容',
  key: 'my-secret-key-32-characters-long',
  mode: 'CBC',
  padding: 'PKCS7'
});

// 高级加密配置
const advancedEncrypt = new AESEncryptNode({
  data: sensitiveUserData,
  keyDerivation: {
    method: 'PBKDF2',
    password: 'user-password',
    salt: 'random-salt-16-bytes',
    iterations: 100000,
    keyLength: 32
  },
  mode: 'GCM',
  ivLength: 12,
  tagLength: 16,
  additionalData: 'authentication-data'
});

// 批量加密
const batchEncrypt = new AESEncryptNode({
  dataArray: [file1Data, file2Data, file3Data],
  key: masterKey,
  mode: 'CTR',
  parallel: true,
  onProgress: (index, total) => {
    console.log(`加密进度: ${index}/${total}`);
  }
});
```

#### SHA256HashNode (SHA256哈希节点)
**原理**: 计算数据的SHA256哈希值
**用途**: 数据完整性验证、密码存储、数字签名、文件校验

```typescript
// 基础哈希计算
const sha256Hash = new SHA256HashNode({
  id: 'hash_1',
  data: '需要哈希的数据',
  encoding: 'hex',
  salt: 'optional-salt'
});

// 文件哈希
const fileHash = new SHA256HashNode({
  filePath: './important-file.pdf',
  chunkSize: 1024 * 1024, // 1MB chunks
  onProgress: (progress) => {
    console.log(`哈希计算进度: ${progress}%`);
  }
});

// 密码哈希
const passwordHash = new SHA256HashNode({
  data: userPassword,
  salt: generateRandomSalt(),
  iterations: 10000, // 多轮哈希
  pepper: systemPepper,
  outputFormat: 'base64'
});
```

## 常见使用模式和最佳实践

### 1. 事件驱动模式
```typescript
// 用户点击 -> 播放动画 -> 显示UI -> 更新数据
const eventChain = {
  trigger: new OnClickNode({element: 'startButton'}),
  animation: new PlayAnimationNode({name: 'buttonPress'}),
  ui: new CreateModalNode({content: 'gameStarted'}),
  data: new SetVariableNode({name: 'gameState', value: 'playing'})
};

// 连接事件链
eventChain.trigger.connectTo(eventChain.animation, 'flow');
eventChain.animation.connectTo(eventChain.ui, 'onComplete');
eventChain.ui.connectTo(eventChain.data, 'onShow');
```

### 2. 状态机模式
```typescript
// 游戏状态管理
const gameStateMachine = new SwitchNode({
  value: 'currentGameState',
  cases: {
    'menu': menuStateHandler,
    'playing': playingStateHandler,
    'paused': pausedStateHandler,
    'game_over': gameOverStateHandler
  },
  onStateChange: (oldState, newState) => {
    console.log(`状态从 ${oldState} 切换到 ${newState}`);
    updateUI(newState);
  }
});
```

### 3. 数据流模式
```typescript
// 数据处理管道
const dataProcessingPipeline = {
  input: new HTTPGetNode({url: '/api/data'}),
  validate: new ValidateJSONNode({schema: dataSchema}),
  transform: new JSONPathNode({path: '$.results[*]'}),
  filter: new ArrayOperationNode({operation: 'filter', predicate: 'item.active'}),
  output: new SetVariableNode({name: 'processedData'})
};

// 连接数据流
dataProcessingPipeline.input
  .connectTo(dataProcessingPipeline.validate, 'response')
  .connectTo(dataProcessingPipeline.transform, 'validData')
  .connectTo(dataProcessingPipeline.filter, 'data')
  .connectTo(dataProcessingPipeline.output, 'result');
```

### 4. 异步处理模式
```typescript
// 异步操作链
const asyncChain = {
  request: new HTTPPostNode({url: '/api/process', async: true}),
  wait: new DelayNode({seconds: 1.0}),
  check: new HTTPGetNode({url: '/api/status'}),
  retry: new ForLoopNode({maxIterations: 5}),
  complete: new OnCompleteNode()
};

// 异步错误处理
const errorHandling = new TryCatchNode({
  try: asyncChain.request,
  catch: new LogNode({level: 'error', message: '请求失败'}),
  finally: new SetVariableNode({name: 'requestComplete', value: true})
});
```

## 系统管理节点使用详解

### 调试系统节点 (DebugNodes.ts - 9个节点)

#### BreakpointNode (断点节点)
**原理**: 在脚本执行中设置断点，暂停执行进行调试
**用途**: 代码调试、执行控制、状态检查、问题诊断

```typescript
// 基础断点
const breakpoint = new BreakpointNode({
  id: 'breakpoint_1',
  enabled: true,
  condition: null, // 无条件断点
  logMessage: '执行到断点',
  pauseExecution: true
});

// 条件断点
const conditionalBreakpoint = new BreakpointNode({
  condition: 'playerHealth <= 10',
  enabled: debugMode,
  logMessage: '玩家生命值过低',
  onHit: () => {
    console.log('触发条件断点');
    inspectGameState();
  },
  hitCount: 0,
  hitCondition: 'greater_than_5' // 命中5次后才触发
});

// 临时断点
const temporaryBreakpoint = new BreakpointNode({
  temporary: true,
  autoRemove: true,
  condition: 'errorOccurred === true',
  stackTrace: true,
  variableInspection: ['currentState', 'errorDetails']
});
```

#### LogNode (日志节点)
**原理**: 记录和输出调试信息到控制台或日志文件
**用途**: 信息记录、错误跟踪、性能监控、调试输出

```typescript
// 基础日志
const logNode = new LogNode({
  id: 'log_1',
  level: 'info',
  message: '应用启动完成',
  timestamp: true,
  category: 'application'
});

// 结构化日志
const structuredLog = new LogNode({
  level: 'debug',
  message: '用户操作记录',
  data: {
    userId: '12345',
    action: 'login',
    timestamp: Date.now(),
    ip: '*************'
  },
  format: 'json',
  output: ['console', 'file']
});

// 性能日志
const performanceLog = new LogNode({
  level: 'performance',
  message: '操作执行时间: ${duration}ms',
  variables: {
    duration: () => performance.now() - startTime
  },
  threshold: 100, // 只记录超过100ms的操作
  includeStackTrace: false
});
```

#### PerformanceProfilerNode (性能分析节点)
**原理**: 分析和监控代码执行性能
**用途**: 性能优化、瓶颈识别、资源监控、性能报告

```typescript
// 基础性能分析
const profiler = new PerformanceProfilerNode({
  id: 'profiler_1',
  targetNodes: ['heavy_computation', 'network_request'],
  sampleRate: 100,
  duration: 60000, // 60秒
  autoStart: true
});

// 详细性能监控
const detailedProfiler = new PerformanceProfilerNode({
  metrics: {
    executionTime: true,
    memoryUsage: true,
    cpuUsage: true,
    networkIO: true,
    diskIO: true
  },
  sampling: {
    interval: 10, // 10ms
    adaptive: true,
    highPrecision: true
  },
  reporting: {
    realTime: true,
    generateReport: true,
    reportFormat: 'html',
    includeFlameGraph: true
  }
});

// 自动优化建议
const smartProfiler = new PerformanceProfilerNode({
  aiAnalysis: true,
  optimizationSuggestions: true,
  bottleneckDetection: {
    enabled: true,
    threshold: 50, // 50ms
    autoAlert: true
  },
  comparison: {
    baseline: 'previous_run',
    regression_detection: true
  }
});
```

#### MemoryMonitorNode (内存监控节点)
**原理**: 监控内存使用情况和内存泄漏
**用途**: 内存管理、泄漏检测、资源优化、稳定性保障

```typescript
// 基础内存监控
const memoryMonitor = new MemoryMonitorNode({
  id: 'memory_1',
  interval: 5000, // 5秒检查一次
  threshold: 100 * 1024 * 1024, // 100MB
  autoCleanup: true
});

// 详细内存分析
const detailedMemoryMonitor = new MemoryMonitorNode({
  tracking: {
    heapUsed: true,
    heapTotal: true,
    external: true,
    arrayBuffers: true,
    rss: true
  },
  leakDetection: {
    enabled: true,
    sensitivity: 'medium',
    reportThreshold: 10 * 1024 * 1024, // 10MB增长
    autoGC: true
  },
  alerts: {
    highUsage: 80, // 80%使用率警告
    rapidGrowth: 50 * 1024 * 1024, // 50MB/分钟增长警告
    lowMemory: 50 * 1024 * 1024 // 剩余50MB警告
  }
});
```

### 时间系统节点 (TimeNodes.ts - 9个节点)

#### GetTimeNode (获取时间节点)
**原理**: 获取当前系统时间或游戏时间
**用途**: 时间戳记录、时间计算、定时操作、时间同步

```typescript
// 基础时间获取
const getTime = new GetTimeNode({
  id: 'time_1',
  format: 'timestamp',
  timezone: 'local',
  precision: 'milliseconds'
});

// 游戏时间
const gameTime = new GetTimeNode({
  timeType: 'game_time',
  format: 'seconds',
  startTime: gameStartTime,
  timeScale: 1.0,
  paused: false
});

// 格式化时间
const formattedTime = new GetTimeNode({
  format: 'custom',
  pattern: 'YYYY-MM-DD HH:mm:ss',
  timezone: 'Asia/Shanghai',
  locale: 'zh-CN'
});
```

#### TimerNode (计时器节点)
**原理**: 创建可控制的计时器，支持倒计时和正计时
**用途**: 游戏计时、任务定时、超时控制、时间限制

```typescript
// 基础计时器
const timer = new TimerNode({
  id: 'timer_1',
  duration: 60000, // 60秒
  autoStart: true,
  loop: false
});

// 倒计时器
const countdown = new TimerNode({
  duration: 300000, // 5分钟倒计时
  direction: 'down',
  onTick: (remaining) => {
    updateUI(remaining);
  },
  onComplete: () => {
    console.log('时间到！');
    endGame();
  },
  warnings: [
    {time: 60000, message: '还剩1分钟'},
    {time: 10000, message: '还剩10秒'}
  ]
});

// 精确计时器
const precisionTimer = new TimerNode({
  duration: 1000,
  precision: 'high',
  compensation: true, // 补偿执行延迟
  loop: true,
  onTick: (elapsed, delta) => {
    updateAnimation(delta);
  }
});
```

#### DelayNode (延迟节点)
**原理**: 延迟指定时间后执行后续操作
**用途**: 时间控制、动画延迟、定时触发、流程控制

```typescript
// 基础延迟
const delay = new DelayNode({
  id: 'delay_1',
  seconds: 2.5,
  useGameTime: true,
  pausable: true
});

// 随机延迟
const randomDelay = new DelayNode({
  minSeconds: 1.0,
  maxSeconds: 3.0,
  distribution: 'uniform',
  onStart: () => console.log('延迟开始'),
  onComplete: () => console.log('延迟结束')
});

// 条件延迟
const conditionalDelay = new DelayNode({
  seconds: 5.0,
  condition: () => playerReady,
  checkInterval: 100,
  timeout: 30000,
  onTimeout: () => console.log('延迟超时')
});
```

### 实体管理节点 (EntityNodes.ts - 5个节点)

#### CreateEntityNode (创建实体节点)
**原理**: 在场景中创建新的游戏实体
**用途**: 对象创建、场景管理、动态生成、实体系统

```typescript
// 基础实体创建
const createEntity = new CreateEntityNode({
  id: 'create_entity_1',
  name: 'Player',
  position: new Vector3(0, 0, 0),
  rotation: new Vector3(0, 0, 0),
  scale: new Vector3(1, 1, 1)
});

// 复杂实体创建
const complexEntity = new CreateEntityNode({
  template: 'character_template',
  name: 'NPC_Guard',
  components: [
    {type: 'MeshRenderer', mesh: 'guard_mesh', material: 'guard_material'},
    {type: 'Collider', shape: 'capsule', size: {radius: 0.5, height: 2}},
    {type: 'Rigidbody', mass: 70, useGravity: true},
    {type: 'AIController', behavior: 'patrol', waypoints: patrolPoints}
  ],
  tags: ['NPC', 'Guard', 'Interactive'],
  layer: 'Characters'
});

// 批量实体创建
const batchCreate = new CreateEntityNode({
  count: 10,
  template: 'tree_template',
  distribution: {
    type: 'random',
    area: {min: new Vector3(-50, 0, -50), max: new Vector3(50, 0, 50)},
    avoidOverlap: true,
    minDistance: 5
  },
  variations: {
    scale: {min: 0.8, max: 1.2},
    rotation: {y: {min: 0, max: 360}},
    material: ['tree_material_1', 'tree_material_2', 'tree_material_3']
  }
});
```

#### GetEntityNode (获取实体节点)
**原理**: 根据条件查找和获取场景中的实体
**用途**: 实体查询、对象引用、场景遍历、条件筛选

```typescript
// 按名称获取实体
const getByName = new GetEntityNode({
  id: 'get_entity_1',
  searchType: 'name',
  value: 'Player',
  caseSensitive: false
});

// 按标签获取实体
const getByTag = new GetEntityNode({
  searchType: 'tag',
  value: 'Enemy',
  multiple: true,
  maxResults: 10
});

// 按距离获取实体
const getNearby = new GetEntityNode({
  searchType: 'distance',
  center: playerPosition,
  radius: 10,
  layerMask: ['Interactive', 'Collectible'],
  sortByDistance: true,
  filter: (entity) => entity.isActive
});

// 复杂查询
const complexQuery = new GetEntityNode({
  searchType: 'complex',
  criteria: {
    hasComponent: ['Health', 'Renderer'],
    tag: 'Enemy',
    layer: 'Characters',
    customFilter: (entity) => {
      return entity.getComponent('Health').currentHealth > 0;
    }
  },
  cacheResults: true,
  updateInterval: 1000
});
```

#### AddComponentNode (添加组件节点)
**原理**: 向实体添加新的组件
**用途**: 动态功能扩展、组件管理、行为添加、系统集成

```typescript
// 基础组件添加
const addComponent = new AddComponentNode({
  id: 'add_comp_1',
  entity: targetEntity,
  componentType: 'AudioSource',
  properties: {
    clip: 'sound_effect',
    volume: 0.8,
    loop: false,
    playOnAwake: false
  }
});

// 条件组件添加
const conditionalAdd = new AddComponentNode({
  entity: playerEntity,
  componentType: 'PowerUp',
  condition: () => playerLevel >= 5,
  properties: {
    type: 'speed_boost',
    duration: 30000,
    multiplier: 1.5
  },
  onAdded: (component) => {
    console.log('组件添加成功:', component);
  }
});

// 批量组件添加
const batchAddComponents = new AddComponentNode({
  entities: enemyEntities,
  components: [
    {type: 'Health', properties: {maxHealth: 100, currentHealth: 100}},
    {type: 'AI', properties: {behavior: 'aggressive', detectionRange: 15}},
    {type: 'Loot', properties: {dropTable: 'enemy_loot', dropChance: 0.3}}
  ],
  parallel: true
});
```

## 专业应用节点使用详解

### 虚拟化身系统节点 (30个节点)

#### CreateAvatarNode (创建化身节点)
**原理**: 基于参数生成虚拟化身
**用途**: 角色创建、用户化身、数字人生成

```typescript
// 基础化身创建
const createAvatar = new CreateAvatarNode({
  id: 'avatar_1',
  gender: 'female',
  ageRange: 'adult',
  bodyType: 'average',
  skinTone: 'medium',
  hairStyle: 'long_wavy',
  eyeColor: 'brown',
  randomSeed: 12345
});

// 高级化身定制
const customAvatar = new CreateAvatarNode({
  baseTemplate: 'realistic_human',
  customization: {
    facial: {
      eyeShape: 'almond',
      noseShape: 'straight',
      lipShape: 'full',
      facialHair: 'none'
    },
    body: {
      height: 170,
      weight: 65,
      muscleMass: 0.5,
      bodyFat: 0.3
    },
    clothing: {
      style: 'business_casual',
      colors: ['#2c3e50', '#ecf0f1'],
      accessories: ['glasses', 'watch']
    }
  },
  quality: 'high',
  optimization: 'realtime'
});
```

#### ReconstructFaceFromPhotoNode (照片重建面部节点)
**原理**: 从用户照片重建3D面部模型
**用途**: 个性化化身、面部识别、相似度匹配

```typescript
// 照片面部重建
const faceReconstruction = new ReconstructFaceFromPhotoNode({
  id: 'face_recon_1',
  photoInput: userPhotoFile,
  quality: 'high',
  landmarkDetection: true,
  textureResolution: 1024,
  symmetryCorrection: true,
  ageProgression: false
});

// 多角度面部重建
const multiFaceRecon = new ReconstructFaceFromPhotoNode({
  photos: [frontPhoto, leftProfile, rightProfile],
  reconstructionMode: 'multi_view',
  outputFormat: '3d_mesh',
  includeTextures: true,
  faceMapping: {
    preserveIdentity: true,
    enhanceFeatures: false,
    smoothing: 0.3
  }
});
```

### 医疗模拟节点 (4个节点)

#### MedicalKnowledgeQueryNode (医疗知识查询节点)
**原理**: 查询医疗知识库获取相关信息
**用途**: 医疗问答、诊断辅助、教育培训

```typescript
// 医疗知识查询
const medicalQuery = new MedicalKnowledgeQueryNode({
  id: 'medical_query_1',
  knowledgeBase: 'comprehensive_medical_db',
  query: '高血压的症状和治疗方法',
  language: 'zh-CN',
  includeImages: true,
  evidenceLevel: 'high',
  specialization: 'cardiology'
});

// 症状诊断查询
const symptomQuery = new MedicalKnowledgeQueryNode({
  queryType: 'symptom_analysis',
  symptoms: ['头痛', '发热', '咳嗽'],
  patientInfo: {
    age: 35,
    gender: 'male',
    medicalHistory: ['高血压']
  },
  differentialDiagnosis: true,
  confidenceThreshold: 0.7
});
```

## 数据格式处理节点使用详解

### 图像处理节点 (ImageProcessingNodes.ts - 12个节点)

#### LoadImageNode (加载图像节点)
**原理**: 从文件或URL加载图像数据
**用途**: 图像导入、资源加载、图像预处理、批量处理

```typescript
// 基础图像加载
const loadImage = new LoadImageNode({
  id: 'load_image_1',
  source: './assets/images/photo.jpg',
  format: 'auto_detect',
  quality: 'original'
});

// 批量图像加载
const batchLoadImages = new LoadImageNode({
  sources: [
    './images/img1.jpg',
    './images/img2.png',
    'https://example.com/img3.webp'
  ],
  async: true,
  onProgress: (loaded, total) => {
    console.log(`加载进度: ${loaded}/${total}`);
  },
  onComplete: (images) => {
    console.log('所有图像加载完成');
  }
});

// 智能加载
const smartLoad = new LoadImageNode({
  source: imageUrl,
  caching: {
    enabled: true,
    maxSize: 100 * 1024 * 1024, // 100MB缓存
    ttl: 3600000 // 1小时
  },
  optimization: {
    autoResize: true,
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.85
  },
  fallback: './assets/placeholder.jpg'
});
```

#### ResizeImageNode (调整图像大小节点)
**原理**: 改变图像的尺寸和分辨率
**用途**: 图像缩放、尺寸适配、性能优化、响应式处理

```typescript
// 基础图像缩放
const resizeImage = new ResizeImageNode({
  id: 'resize_1',
  image: sourceImage,
  width: 800,
  height: 600,
  method: 'bilinear',
  maintainAspectRatio: true
});

// 智能缩放
const smartResize = new ResizeImageNode({
  image: sourceImage,
  targetSize: {width: 1024, height: 768},
  scaleMode: 'fit', // 'fit', 'fill', 'stretch', 'crop'
  quality: 'high',
  sharpening: {
    enabled: true,
    amount: 0.5
  },
  optimization: {
    progressive: true,
    stripMetadata: true
  }
});

// 响应式缩放
const responsiveResize = new ResizeImageNode({
  image: sourceImage,
  breakpoints: [
    {width: 320, suffix: '_mobile'},
    {width: 768, suffix: '_tablet'},
    {width: 1200, suffix: '_desktop'}
  ],
  outputFormat: 'webp',
  fallbackFormat: 'jpg'
});
```

#### CropImageNode (裁剪图像节点)
**原理**: 从图像中提取指定区域
**用途**: 图像裁剪、区域提取、构图调整、内容聚焦

```typescript
// 基础图像裁剪
const cropImage = new CropImageNode({
  id: 'crop_1',
  image: sourceImage,
  x: 100,
  y: 100,
  width: 400,
  height: 300
});

// 智能裁剪
const smartCrop = new CropImageNode({
  image: sourceImage,
  mode: 'smart', // 'manual', 'smart', 'face_detection'
  targetRatio: '16:9',
  focusPoint: 'center',
  algorithm: 'attention_based',
  preserveImportantAreas: true
});

// 人脸检测裁剪
const faceCrop = new CropImageNode({
  image: portraitImage,
  mode: 'face_detection',
  faceDetection: {
    model: 'mtcnn',
    confidence: 0.9,
    padding: 0.2
  },
  outputSize: {width: 256, height: 256},
  centerFace: true
});
```

### 日期时间节点 (DateTimeNodes.ts - 6个节点)

#### GetCurrentTimeNode (获取当前时间节点)
**原理**: 获取系统当前日期和时间
**用途**: 时间戳记录、日期显示、时间计算、日志记录

```typescript
// 基础时间获取
const getCurrentTime = new GetCurrentTimeNode({
  id: 'current_time_1',
  format: 'iso',
  timezone: 'local',
  includeMilliseconds: true
});

// 自定义格式时间
const customTime = new GetCurrentTimeNode({
  format: 'custom',
  pattern: 'YYYY年MM月DD日 HH:mm:ss',
  timezone: 'Asia/Shanghai',
  locale: 'zh-CN'
});

// 多时区时间
const multiTimezone = new GetCurrentTimeNode({
  timezones: [
    {name: 'local', timezone: 'local'},
    {name: 'utc', timezone: 'UTC'},
    {name: 'tokyo', timezone: 'Asia/Tokyo'},
    {name: 'newyork', timezone: 'America/New_York'}
  ],
  format: 'object'
});
```

#### FormatDateNode (格式化日期节点)
**原理**: 将日期对象格式化为指定格式的字符串
**用途**: 日期显示、报表生成、用户界面、数据导出

```typescript
// 基础日期格式化
const formatDate = new FormatDateNode({
  id: 'format_date_1',
  date: new Date(),
  format: 'YYYY-MM-DD',
  timezone: 'local'
});

// 多语言日期格式化
const multiLangDate = new FormatDateNode({
  date: targetDate,
  locale: 'zh-CN',
  options: {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  },
  fallbackLocale: 'en-US'
});

// 相对时间格式化
const relativeTime = new FormatDateNode({
  date: pastDate,
  format: 'relative',
  relativeTo: new Date(),
  precision: 'minute',
  locale: 'zh-CN'
  // 输出: "3分钟前", "2小时前", "昨天", "上周"等
});
```

#### ParseDateNode (解析日期节点)
**原理**: 将字符串解析为日期对象
**用途**: 数据导入、用户输入处理、日期验证、格式转换

```typescript
// 基础日期解析
const parseDate = new ParseDateNode({
  id: 'parse_date_1',
  dateString: '2023-12-25',
  format: 'YYYY-MM-DD',
  strict: true
});

// 多格式解析
const multiFormatParse = new ParseDateNode({
  dateString: userInput,
  formats: [
    'YYYY-MM-DD',
    'MM/DD/YYYY',
    'DD.MM.YYYY',
    'YYYY年MM月DD日'
  ],
  autoDetect: true,
  locale: 'zh-CN'
});

// 智能日期解析
const smartParse = new ParseDateNode({
  dateString: naturalLanguageDate,
  mode: 'natural', // 支持"明天"、"下周一"、"3天后"等
  baseDate: new Date(),
  timezone: 'Asia/Shanghai',
  validation: {
    minDate: new Date('2020-01-01'),
    maxDate: new Date('2030-12-31')
  }
});
```

### JSON处理节点 (JSONNodes.ts - 6个节点)

#### ParseJSONNode (解析JSON节点)
**原理**: 将JSON字符串解析为JavaScript对象
**用途**: 数据解析、API响应处理、配置读取、数据转换

```typescript
// 基础JSON解析
const parseJSON = new ParseJSONNode({
  id: 'parse_json_1',
  jsonString: '{"name": "张三", "age": 25}',
  strict: true,
  errorHandling: 'throw'
});

// 安全JSON解析
const safeParseJSON = new ParseJSONNode({
  jsonString: untrustedJsonString,
  validation: {
    enabled: true,
    schema: {
      type: 'object',
      properties: {
        name: {type: 'string', maxLength: 50},
        age: {type: 'number', minimum: 0, maximum: 150}
      },
      required: ['name']
    }
  },
  sanitization: {
    removeNullBytes: true,
    maxDepth: 10,
    maxKeys: 100
  },
  fallbackValue: {}
});

// 流式JSON解析
const streamParseJSON = new ParseJSONNode({
  jsonStream: largeJsonStream,
  streaming: true,
  chunkSize: 1024,
  onObject: (obj, path) => {
    console.log(`解析到对象: ${path}`, obj);
  },
  onArray: (arr, path) => {
    console.log(`解析到数组: ${path}`, arr);
  }
});
```

#### StringifyJSONNode (序列化JSON节点)
**原理**: 将JavaScript对象转换为JSON字符串
**用途**: 数据序列化、API请求、数据存储、配置保存

```typescript
// 基础JSON序列化
const stringifyJSON = new StringifyJSONNode({
  id: 'stringify_1',
  object: {name: '李四', age: 30, city: '北京'},
  pretty: false,
  space: 0
});

// 格式化JSON序列化
const prettyStringify = new StringifyJSONNode({
  object: complexObject,
  pretty: true,
  space: 2,
  sortKeys: true,
  replacer: (key, value) => {
    // 过滤敏感信息
    if (key === 'password') return undefined;
    return value;
  }
});

// 自定义序列化
const customStringify = new StringifyJSONNode({
  object: dataObject,
  options: {
    maxDepth: 5,
    circularReference: 'remove',
    undefinedValues: 'null',
    functionValues: 'string',
    dateFormat: 'iso'
  },
  compression: {
    enabled: true,
    algorithm: 'gzip'
  }
});
```

#### JSONPathNode (JSON路径查询节点)
**原理**: 使用JSONPath表达式查询JSON数据
**用途**: 数据提取、复杂查询、数据筛选、路径导航

```typescript
// 基础JSONPath查询
const jsonPath = new JSONPathNode({
  id: 'json_path_1',
  data: jsonData,
  path: '$.users[*].name',
  returnType: 'array'
});

// 复杂查询
const complexQuery = new JSONPathNode({
  data: complexJsonData,
  path: '$.store.book[?(@.price < 10)].title',
  filters: {
    price: (value) => value < 10 && value > 0,
    inStock: true
  },
  defaultValue: []
});

// 多路径查询
const multiPathQuery = new JSONPathNode({
  data: jsonData,
  queries: [
    {name: 'userNames', path: '$.users[*].name'},
    {name: 'userEmails', path: '$.users[*].email'},
    {name: 'activeUsers', path: '$.users[?(@.active == true)]'}
  ],
  combineResults: true,
  resultFormat: 'object'
});
```

### 工业自动化节点 (7个节点)

#### PLCControlNode (PLC控制节点)
**原理**: 与可编程逻辑控制器通信
**用途**: 设备控制、生产线管理、自动化系统

```typescript
// PLC设备控制
const plcControl = new PLCControlNode({
  id: 'plc_1',
  plcAddress: '*************',
  protocol: 'modbus_tcp',
  port: 502,
  deviceId: 1,
  timeout: 5000,
  retryAttempts: 3
});

// 生产线控制
const productionLineControl = new PLCControlNode({
  plcAddress: '*************',
  protocol: 'profinet',
  commands: [
    {address: 'DB1.DBX0.0', value: true, description: '启动传送带'},
    {address: 'DB1.DBW2', value: 1500, description: '设置速度'},
    {address: 'DB1.DBX0.1', value: false, description: '停止信号'}
  ],
  monitoring: {
    enabled: true,
    interval: 100,
    alarms: true
  }
});
```

### 区块链系统节点 (BlockchainSystemNodes.ts - 4个节点)

#### WalletConnectorNode (钱包连接节点)
**原理**: 连接和管理区块链钱包
**用途**: 钱包集成、身份验证、交易授权、资产管理

```typescript
// 基础钱包连接
const walletConnector = new WalletConnectorNode({
  id: 'wallet_1',
  walletType: 'metamask',
  network: 'ethereum',
  chainId: 1,
  autoConnect: true
});

// 多钱包支持
const multiWalletConnector = new WalletConnectorNode({
  supportedWallets: [
    {type: 'metamask', priority: 1},
    {type: 'walletconnect', priority: 2},
    {type: 'coinbase', priority: 3}
  ],
  fallbackMode: 'readonly',
  onConnect: (wallet) => {
    console.log('钱包连接成功:', wallet.address);
  },
  onDisconnect: () => {
    console.log('钱包已断开');
  }
});

// 企业级钱包集成
const enterpriseWallet = new WalletConnectorNode({
  walletType: 'enterprise',
  authentication: {
    method: 'multi_signature',
    requiredSignatures: 2,
    signers: ['0x123...', '0x456...', '0x789...']
  },
  security: {
    encryptPrivateKeys: true,
    hardwareSecurityModule: true,
    auditLogging: true
  }
});
```

#### NFTManagerNode (NFT管理节点)
**原理**: 管理NFT的创建、转移和查询
**用途**: NFT铸造、数字资产管理、收藏品系统、版权保护

```typescript
// 基础NFT管理
const nftManager = new NFTManagerNode({
  id: 'nft_manager_1',
  contractAddress: '0x1234567890abcdef...',
  network: 'ethereum',
  standard: 'ERC721'
});

// NFT铸造
const mintNFT = new NFTManagerNode({
  action: 'mint',
  contractAddress: nftContractAddress,
  metadata: {
    name: '数字艺术品 #001',
    description: '独特的数字艺术创作',
    image: 'ipfs://QmHash...',
    attributes: [
      {trait_type: '稀有度', value: '传奇'},
      {trait_type: '艺术家', value: '张三'},
      {trait_type: '创作年份', value: '2023'}
    ]
  },
  recipient: userWalletAddress,
  royalty: {
    percentage: 5,
    recipient: artistWalletAddress
  }
});

// NFT市场集成
const nftMarketplace = new NFTManagerNode({
  marketplace: {
    enabled: true,
    platform: 'opensea',
    listingPrice: '0.1', // ETH
    currency: 'ETH',
    duration: 30 * 24 * 60 * 60, // 30天
    royaltyEnforcement: true
  },
  analytics: {
    trackViews: true,
    trackSales: true,
    priceHistory: true
  }
});
```

#### SmartContractNode (智能合约节点)
**原理**: 部署和调用智能合约
**用途**: 合约部署、函数调用、事件监听、DApp开发

```typescript
// 智能合约部署
const deployContract = new SmartContractNode({
  id: 'deploy_contract_1',
  action: 'deploy',
  contractCode: contractBytecode,
  constructorArgs: ['初始参数1', '初始参数2'],
  gasLimit: 3000000,
  gasPrice: '20000000000' // 20 Gwei
});

// 合约函数调用
const callContract = new SmartContractNode({
  action: 'call',
  contractAddress: deployedContractAddress,
  functionName: 'transfer',
  parameters: [recipientAddress, transferAmount],
  value: '0', // 发送的ETH数量
  gasEstimate: true
});

// 事件监听
const contractEvents = new SmartContractNode({
  action: 'listen',
  contractAddress: contractAddress,
  events: [
    {
      name: 'Transfer',
      filter: {from: userAddress},
      onEvent: (event) => {
        console.log('转账事件:', event);
        updateBalance();
      }
    },
    {
      name: 'Approval',
      onEvent: (event) => {
        console.log('授权事件:', event);
      }
    }
  ],
  fromBlock: 'latest'
});
```

### 学习跟踪节点 (LearningTrackingNodes.ts - 4个节点)

#### LearningRecordNode (学习记录节点)
**原理**: 记录和跟踪学习活动数据
**用途**: 学习分析、进度跟踪、行为记录、数据收集

```typescript
// 基础学习记录
const learningRecord = new LearningRecordNode({
  id: 'learning_record_1',
  userId: 'user_12345',
  activityType: 'video_watch',
  content: {
    videoId: 'video_001',
    title: 'JavaScript基础教程',
    duration: 1800, // 30分钟
    watchTime: 1650 // 实际观看27.5分钟
  },
  timestamp: Date.now()
});

// xAPI标准记录
const xapiRecord = new LearningRecordNode({
  standard: 'xapi',
  statement: {
    actor: {
      name: '张三',
      mbox: 'mailto:<EMAIL>'
    },
    verb: {
      id: 'http://adlnet.gov/expapi/verbs/completed',
      display: {'zh-CN': '完成了'}
    },
    object: {
      id: 'http://example.com/course/javascript-101',
      definition: {
        name: {'zh-CN': 'JavaScript入门课程'},
        type: 'http://adlnet.gov/expapi/activities/course'
      }
    },
    result: {
      score: {scaled: 0.85},
      completion: true,
      success: true,
      duration: 'PT2H30M'
    }
  }
});

// 详细学习分析
const detailedRecord = new LearningRecordNode({
  userId: userId,
  sessionId: sessionId,
  analytics: {
    engagement: {
      clickCount: 45,
      scrollDepth: 0.8,
      timeOnPage: 1200,
      interactionRate: 0.75
    },
    performance: {
      correctAnswers: 8,
      totalQuestions: 10,
      averageResponseTime: 15.5,
      hintsUsed: 2
    },
    learning_path: {
      currentModule: 'module_3',
      completedModules: ['module_1', 'module_2'],
      nextRecommendation: 'module_4'
    }
  }
});
```

#### ProgressAnalysisNode (进度分析节点)
**原理**: 分析学习进度和表现数据
**用途**: 进度评估、学习分析、个性化推荐、教学优化

```typescript
// 基础进度分析
const progressAnalysis = new ProgressAnalysisNode({
  id: 'progress_1',
  userId: 'user_12345',
  courseId: 'course_js_101',
  timeframe: 'last_30_days',
  metrics: ['completion_rate', 'time_spent', 'quiz_scores']
});

// 深度学习分析
const deepAnalysis = new ProgressAnalysisNode({
  userId: userId,
  analysisType: 'comprehensive',
  dimensions: {
    cognitive: {
      knowledgeRetention: true,
      conceptualUnderstanding: true,
      problemSolvingSkills: true
    },
    behavioral: {
      engagementPatterns: true,
      learningPreferences: true,
      motivationLevels: true
    },
    performance: {
      accuracyTrends: true,
      speedImprovement: true,
      difficultyProgression: true
    }
  },
  predictiveModeling: {
    enabled: true,
    models: ['completion_prediction', 'performance_forecast'],
    confidence_threshold: 0.8
  }
});

// 群体分析
const cohortAnalysis = new ProgressAnalysisNode({
  analysisScope: 'cohort',
  cohortId: 'class_2023_fall',
  comparisons: {
    individual_vs_cohort: true,
    cohort_vs_historical: true,
    peer_ranking: true
  },
  insights: {
    strugglingStudents: true,
    topPerformers: true,
    commonDifficulties: true,
    recommendedInterventions: true
  }
});
```

### 多区域部署节点 (MultiRegionDeploymentNodes.ts - 4个节点)

#### RegionManagerNode (区域管理节点)
**原理**: 管理多个部署区域的配置和状态
**用途**: 区域配置、负载均衡、故障转移、全球化部署

```typescript
// 基础区域管理
const regionManager = new RegionManagerNode({
  id: 'region_manager_1',
  regions: [
    {
      id: 'us-east-1',
      name: '美国东部',
      endpoint: 'https://us-east.example.com',
      priority: 1,
      capacity: 1000
    },
    {
      id: 'eu-west-1',
      name: '欧洲西部',
      endpoint: 'https://eu-west.example.com',
      priority: 2,
      capacity: 800
    },
    {
      id: 'ap-southeast-1',
      name: '亚太东南',
      endpoint: 'https://ap-southeast.example.com',
      priority: 3,
      capacity: 600
    }
  ],
  primaryRegion: 'us-east-1',
  failoverStrategy: 'automatic'
});

// 智能区域管理
const smartRegionManager = new RegionManagerNode({
  regions: regionConfigs,
  routing: {
    strategy: 'latency_based', // 'geographic', 'latency_based', 'load_based'
    geoLocation: true,
    latencyThreshold: 100, // ms
    loadBalancing: 'weighted_round_robin'
  },
  healthCheck: {
    interval: 30000,
    timeout: 5000,
    retries: 3,
    endpoints: ['/health', '/api/status']
  },
  autoScaling: {
    enabled: true,
    metrics: ['cpu', 'memory', 'requests_per_second'],
    thresholds: {
      scaleUp: 80,
      scaleDown: 30
    }
  }
});

// 灾难恢复配置
const drRegionManager = new RegionManagerNode({
  regions: regionConfigs,
  disasterRecovery: {
    enabled: true,
    rpo: 300, // 恢复点目标：5分钟
    rto: 900, // 恢复时间目标：15分钟
    backupRegions: ['us-west-2', 'eu-central-1'],
    dataReplication: 'synchronous',
    failoverTesting: {
      enabled: true,
      schedule: 'monthly',
      automated: true
    }
  }
});
```

#### DataSyncNode (数据同步节点)
**原理**: 在多个区域间同步数据
**用途**: 数据一致性、备份恢复、灾难恢复、分布式存储

```typescript
// 基础数据同步
const dataSync = new DataSyncNode({
  id: 'data_sync_1',
  sourceRegion: 'us-east-1',
  targetRegions: ['eu-west-1', 'ap-southeast-1'],
  syncMode: 'real_time',
  dataTypes: ['user_data', 'content', 'configurations']
});

// 高级同步配置
const advancedSync = new DataSyncNode({
  syncStrategy: 'multi_master',
  conflictResolution: {
    method: 'timestamp_based', // 'timestamp_based', 'version_based', 'custom'
    customResolver: (local, remote) => {
      // 自定义冲突解决逻辑
      return local.priority > remote.priority ? local : remote;
    }
  },
  compression: {
    enabled: true,
    algorithm: 'gzip',
    level: 6
  },
  encryption: {
    enabled: true,
    algorithm: 'AES-256-GCM',
    keyRotation: true
  },
  bandwidth: {
    limit: '100MB/s',
    priority: 'high',
    throttling: true
  }
});

// 增量同步
const incrementalSync = new DataSyncNode({
  syncMode: 'incremental',
  changeDetection: {
    method: 'checksum', // 'checksum', 'timestamp', 'version'
    granularity: 'record',
    batchSize: 1000
  },
  scheduling: {
    interval: 300000, // 5分钟
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential',
      maxDelay: 60000
    }
  },
  monitoring: {
    enabled: true,
    metrics: ['sync_latency', 'data_volume', 'error_rate'],
    alerts: {
      syncFailure: true,
      highLatency: 5000, // 5秒
      dataLoss: true
    }
  }
});
```

## 性能优化策略

### 1. 节点级别优化

#### 节点复用和缓存
```typescript
// 节点复用示例
const sharedMathNode = new AddNode({id: 'shared_add'});
// 在多个地方使用同一个节点实例

// 结果缓存
const cachedCalculation = new MathFunctionNode({
  function: 'complex_calculation',
  cacheResults: true,
  cacheSize: 100,
  cacheTTL: 60000 // 1分钟缓存
});

// 延迟加载
const lazyNode = new LoadAIModelNode({
  modelPath: './large_model.onnx',
  loadStrategy: 'lazy',
  preloadCondition: 'user_interaction_detected'
});
```

#### 批处理优化
```typescript
// 批量数据处理
const batchProcessor = new ArrayOperationNode({
  operation: 'batch_process',
  batchSize: 100,
  processingFunction: 'parallel',
  maxConcurrency: 4
});

// 批量网络请求
const batchHTTP = new HTTPPostNode({
  batchMode: true,
  batchSize: 10,
  batchInterval: 100,
  compression: 'gzip'
});
```

### 2. 系统级别优化

#### 内存管理
```typescript
// 内存监控
const memoryMonitor = new MemoryMonitorNode({
  interval: 5000,
  threshold: 100 * 1024 * 1024, // 100MB
  autoCleanup: true,
  onThresholdExceeded: () => {
    // 执行内存清理
    cleanupUnusedNodes();
    garbageCollect();
  }
});

// 对象池管理
const objectPool = new ObjectPoolNode({
  objectType: 'ParticleSystem',
  poolSize: 50,
  preAllocate: true,
  autoExpand: true,
  maxSize: 200
});
```

#### 异步执行优化
```typescript
// 异步任务队列
const taskQueue = new AsyncTaskQueueNode({
  maxConcurrency: 3,
  priority: 'high',
  timeout: 30000,
  retryPolicy: {
    maxRetries: 3,
    backoffStrategy: 'exponential'
  }
});

// 非阻塞操作
const nonBlockingOperation = new AsyncOperationNode({
  operation: heavyComputationNode,
  background: true,
  progressCallback: (progress) => {
    updateProgressBar(progress);
  }
});
```

### 3. 渲染和UI优化

#### 虚拟化渲染
```typescript
// 虚拟列表
const virtualList = new CreateDataGridNode({
  virtualScrolling: true,
  rowHeight: 40,
  visibleRows: 20,
  bufferSize: 5,
  lazyLoading: true
});

// LOD系统
const lodSystem = new LODSystemNode({
  levels: [
    {distance: 0, quality: 'high'},
    {distance: 50, quality: 'medium'},
    {distance: 100, quality: 'low'},
    {distance: 200, quality: 'billboard'}
  ],
  hysteresis: 0.1
});
```

### 4. 网络优化

#### 连接池管理
```typescript
// HTTP连接池
const httpPool = new HTTPConnectionPoolNode({
  maxConnections: 10,
  keepAlive: true,
  timeout: 30000,
  retryPolicy: 'exponential_backoff'
});

// WebSocket连接管理
const wsManager = new WebSocketManagerNode({
  maxConnections: 5,
  reconnectInterval: 1000,
  heartbeatInterval: 30000,
  compression: true
});
```

## 错误处理和调试最佳实践

### 1. 错误处理模式

#### 防御性编程
```typescript
// 输入验证
const validateInput = new ValidateInputNode({
  schema: {
    type: 'object',
    properties: {
      name: {type: 'string', minLength: 1},
      age: {type: 'number', minimum: 0, maximum: 150}
    },
    required: ['name', 'age']
  },
  onValidationError: (errors) => {
    showErrorMessage(errors);
    return false; // 阻止继续执行
  }
});

// 边界检查
const boundaryCheck = new BranchNode({
  condition: 'value >= minValue && value <= maxValue',
  onFalse: new LogNode({
    level: 'warning',
    message: '值超出有效范围: ${value}'
  })
});
```

#### 优雅降级
```typescript
// 功能降级
const featureFallback = new TryCatchNode({
  try: new AdvancedFeatureNode(),
  catch: new BasicFeatureNode(),
  finally: new LogNode({message: '功能执行完成'})
});

// 资源降级
const resourceFallback = new LoadResourceNode({
  primaryResource: 'high_quality_texture.jpg',
  fallbackResources: [
    'medium_quality_texture.jpg',
    'low_quality_texture.jpg',
    'placeholder.jpg'
  ],
  autoFallback: true
});
```

### 2. 调试工具和技巧

#### 断点和日志
```typescript
// 条件断点
const conditionalBreakpoint = new BreakpointNode({
  condition: 'playerHealth <= 0',
  enabled: debugMode,
  onHit: () => {
    console.log('玩家生命值为0，触发断点');
    inspectGameState();
  }
});

// 详细日志
const detailedLog = new LogNode({
  level: 'debug',
  message: '节点执行: ${nodeName}, 输入: ${JSON.stringify(inputs)}, 输出: ${JSON.stringify(outputs)}',
  includeStackTrace: true,
  includeTimestamp: true
});
```

#### 性能分析
```typescript
// 性能监控
const performanceProfiler = new PerformanceProfilerNode({
  targetNodes: ['heavy_computation', 'network_request'],
  sampleRate: 100,
  includeMemory: true,
  generateReport: true,
  reportInterval: 60000
});

// 执行时间测量
const executionTimer = new PerformanceTimerNode({
  precision: 'microseconds',
  autoStart: true,
  onComplete: (duration) => {
    if (duration > 16.67) { // 超过一帧时间
      console.warn(`节点执行时间过长: ${duration}ms`);
    }
  }
});
```

### 3. 错误恢复策略

#### 自动重试
```typescript
// 网络请求重试
const retryableRequest = new HTTPGetNode({
  url: 'https://api.example.com/data',
  retryPolicy: {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2,
    retryCondition: (error) => {
      return error.status >= 500 || error.code === 'NETWORK_ERROR';
    }
  }
});

// 状态恢复
const stateRecovery = new StateRecoveryNode({
  saveInterval: 30000,
  maxSaveStates: 10,
  autoRestore: true,
  onRestore: (savedState) => {
    console.log('恢复到保存状态:', savedState.timestamp);
    restoreGameState(savedState);
  }
});
```

## 节点使用指南总结

### 核心设计原则

#### 1. 统一接口规范
所有413个节点都遵循统一的设计规范：
- 继承自统一的基类，确保接口一致性
- 标准化的输入输出插槽系统，支持强类型验证
- 一致的执行和错误处理机制
- 统一的配置和参数设置方式

#### 2. 类型安全保障
- 强类型的数据流验证，防止类型错误
- 编译时类型检查，提前发现问题
- 运行时类型转换和验证，确保数据正确性
- 自动类型推导，简化开发流程

#### 3. 异步支持
- 支持异步操作的节点，适应现代应用需求
- 非阻塞的执行模式，提高系统响应性
- Promise和回调机制，灵活处理异步结果
- 任务队列和优先级管理

#### 4. 性能优化
- 延迟计算和缓存机制，避免重复计算
- 批处理和并行执行，提高处理效率
- 内存管理和资源回收，防止内存泄漏
- 虚拟化渲染，优化大数据集显示

### 节点分类和应用场景

#### 核心基础节点 (53个)
- **核心节点** (14个): 流程控制、数据操作、变量管理、异常处理
- **数学节点** (16个): 数值计算、向量运算、三角函数、随机数生成
- **调试节点** (9个): 断点调试、日志记录、性能分析、内存监控
- **时间节点** (9个): 时间获取、计时器、延迟执行、时间格式化
- **实体节点** (5个): 实体创建、组件管理、场景对象操作

**适用场景**: 所有类型的应用开发基础，提供核心编程功能

#### 数据处理节点 (56个)
- **文件系统节点** (16个): 文件读写、目录操作、文件监视、压缩解压
- **数据库节点** (6个): 数据库连接、查询执行、事务处理、数据操作
- **加密安全节点** (14个): 数据加密、哈希计算、数字签名、会话管理
- **JSON处理节点** (6个): JSON解析、序列化、路径查询、数据转换
- **日期时间节点** (6个): 日期格式化、时间解析、时区处理、相对时间
- **图像处理节点** (12个): 图像加载、尺寸调整、格式转换、滤镜效果

**适用场景**: 数据密集型应用、企业级系统、内容管理系统

#### 网络通信节点 (43个)
- **基础网络节点** (7个): 服务器连接、消息发送、网络状态管理
- **HTTP节点** (5个): HTTP请求、响应处理、RESTful API调用
- **WebRTC节点** (13个): 实时通信、媒体流、P2P连接、数据通道
- **网络安全节点** (18个): 数据加密、身份验证、会话管理、安全传输

**适用场景**: 网络应用、实时通信、分布式系统、安全要求高的应用

#### 用户界面节点 (34个)
- **基础UI节点** (14个): 按钮、输入框、文本显示、滑块控件
- **高级UI节点** (6个): 数据表格、树形视图、图表组件、复杂布局
- **UI主题节点** (8个): 主题管理、样式切换、响应式设计
- **UI事件节点** (6个): 事件处理、交互响应、手势识别

**适用场景**: Web应用、桌面应用、移动应用界面开发

#### 多媒体系统节点 (62个)
- **音频节点** (13个): 音频播放、3D音效、音频混合、空间化音频
- **动画节点** (21个): 动画播放、IK求解、动画混合、重定向、动作匹配
- **物理节点** (22个): 刚体物理、软体模拟、碰撞检测、流体仿真
- **输入节点** (6个): 键盘鼠标、触摸手势、游戏手柄、动作捕捉

**适用场景**: 游戏开发、虚拟现实、多媒体应用、物理仿真系统

#### AI智能节点 (46个)
- **AI模型节点** (12个): 模型加载、推理执行、训练管理、模型优化
- **自然语言处理节点** (14个): 文本处理、语音识别、对话管理、语言翻译
- **情感计算节点** (8个): 情感分析、表情生成、情感驱动动画、情绪识别
- **智能助手节点** (12个): 代码生成、智能推荐、自动化辅助、知识问答

**适用场景**: 智能应用、聊天机器人、内容生成、智能分析系统

#### 专业应用节点 (119个)
- **虚拟化身节点** (30个): 化身创建、面部重建、身体定制、动作捕捉
- **医疗模拟节点** (4个): 医疗知识查询、症状分析、虚拟病人、医疗程序
- **工业自动化节点** (7个): PLC控制、传感器数据、生产线管理、质量检测
- **区块链节点** (4个): NFT管理、智能合约、钱包连接、数字资产交易
- **学习跟踪节点** (4个): 学习记录、进度分析、知识推荐、xAPI跟踪
- **多区域部署节点** (4个): 区域管理、负载均衡、数据同步、健康监控
- **其他专业节点** (66个): 包括边缘计算、RAG应用、情感计算等

**适用场景**: 垂直行业应用、专业培训系统、行业解决方案、企业级平台

### 开发最佳实践

#### 1. 架构设计
```typescript
// 模块化设计
const gameModule = {
  core: [OnStartNode, OnUpdateNode, SetVariableNode],
  physics: [CreatePhysicsBodyNode, ApplyForceNode, RaycastNode],
  audio: [PlayAudioNode, Audio3DNode, AudioMixerNode],
  ui: [CreateButtonNode, CreateDataGridNode, UIEventListenerNode]
};

// 分层架构
const layeredArchitecture = {
  presentation: uiNodes,
  business: logicNodes,
  data: dataNodes,
  infrastructure: systemNodes
};
```

#### 2. 代码组织
```typescript
// 使用子图组织复杂逻辑
const playerControllerSubgraph = new SubgraphNode({
  name: 'PlayerController',
  inputs: ['inputEvents', 'deltaTime'],
  outputs: ['playerState', 'animations'],
  nodes: [
    inputProcessingNodes,
    movementNodes,
    animationNodes
  ]
});

// 函数节点封装可复用逻辑
const damageCalculationFunction = new FunctionNode({
  name: 'CalculateDamage',
  parameters: ['baseDamage', 'armor', 'criticalChance'],
  returnType: 'number',
  implementation: damageCalculationNodes
});
```

#### 3. 性能监控
```typescript
// 性能基准测试
const performanceBenchmark = new PerformanceProfilerNode({
  targetNodes: ['critical_path_nodes'],
  metrics: ['execution_time', 'memory_usage', 'cpu_usage'],
  alertThresholds: {
    execution_time: 16.67, // 60fps
    memory_usage: 100 * 1024 * 1024, // 100MB
    cpu_usage: 80 // 80%
  }
});
```

### 未来发展方向

#### 1. 技术演进
- **AI集成深化**: 更多AI模型支持，自动化节点生成
- **云原生支持**: 分布式执行，云端协作开发
- **边缘计算**: 轻量级运行时，移动端优化
- **实时协作**: 多人同时编辑，版本控制集成

#### 2. 生态扩展
- **第三方插件**: 开放插件API，社区贡献节点
- **行业模板**: 预制行业解决方案，快速项目启动
- **学习资源**: 在线教程，最佳实践案例库
- **开发工具**: 可视化调试器，性能分析工具

#### 3. 应用领域
- **元宇宙**: 虚拟世界构建，数字资产管理
- **数字孪生**: 工业仿真，城市建模
- **教育培训**: 沉浸式学习，技能培训
- **医疗健康**: 医疗模拟，健康管理

## 总结

DL引擎的视觉脚本系统通过413个精心设计的节点，构建了一个功能完整、性能优秀的企业级可视化编程平台。本使用指南详细介绍了每一个节点的使用方法、最佳实践和应用场景，实现了100%的节点覆盖。

### 完整节点覆盖统计

#### 按文件分类的节点分布 (27个节点文件)
1. **CoreNodes.ts** - 14个核心节点：基础流程控制和数据操作
2. **MathNodes.ts** - 16个数学节点：完整的数学运算功能
3. **AIModelNodes.ts** - 12个AI模型节点：AI模型管理和推理
4. **AINLPNodes.ts** - 14个自然语言处理节点：文本和语音处理
5. **AIEmotionNodes.ts** - 8个情感计算节点：情感分析和表达
6. **AIAssistantNodes.ts** - 12个智能助手节点：代码生成和智能推荐
7. **NetworkNodes.ts** - 7个基础网络节点：网络连接和通信
8. **HTTPNodes.ts** - 5个HTTP节点：HTTP请求和响应处理
9. **WebRTCNodes.ts** - 13个WebRTC节点：实时通信和媒体流
10. **NetworkSecurityNodes.ts** - 18个网络安全节点：加密和身份验证
11. **UINodes.ts** - 14个基础UI节点：用户界面组件
12. **AdvancedUINodes.ts** - 6个高级UI节点：复杂界面组件
13. **UIThemeNodes.ts** - 8个UI主题节点：主题和样式管理
14. **UIEventListenerNodes.ts** - 6个UI事件节点：事件处理和交互
15. **PhysicsNodes.ts** - 12个刚体物理节点：物理模拟和碰撞
16. **SoftBodyNodes.ts** - 5个软体物理节点：布料和绳索模拟
17. **FluidSimulationNodes.ts** - 5个流体模拟节点：流体动力学
18. **AnimationNodes.ts** - 8个基础动画节点：动画播放和控制
19. **AdvancedAnimationNodes.ts** - 5个高级动画节点：IK和重定向
20. **MotionCaptureNodes.ts** - 8个动作捕捉节点：动作识别和匹配
21. **AudioNodes.ts** - 13个音频节点：音频处理和播放
22. **InputNodes.ts** - 6个输入节点：各种输入设备处理
23. **FileSystemNodes.ts** - 10个文件系统节点：文件操作
24. **AdvancedFileSystemNodes.ts** - 6个高级文件节点：文件监视和压缩
25. **DatabaseNodes.ts** - 6个数据库节点：数据库操作
26. **CryptographyNodes.ts** - 6个加密节点：数据加密和哈希
27. **其他专业节点文件** - 包含虚拟化身、医疗、工业、区块链等专业应用节点

#### 按功能领域的节点分布
- **核心基础功能**: 53个节点 (12.8%)
- **数据处理系统**: 56个节点 (13.6%)
- **网络通信系统**: 43个节点 (10.4%)
- **用户界面系统**: 34个节点 (8.2%)
- **多媒体系统**: 62个节点 (15.0%)
- **AI智能系统**: 46个节点 (11.1%)
- **专业应用系统**: 119个节点 (28.8%)

### 核心技术优势

1. **完整性**: 413个节点实现100%功能覆盖，满足从基础开发到专业应用的所有需求
2. **一致性**: 统一的接口设计和使用模式，降低学习成本和开发复杂度
3. **可扩展性**: 模块化架构支持自定义节点开发，满足特殊需求
4. **高性能**: 优化的执行引擎和资源管理，适合生产环境使用
5. **易用性**: 直观的可视化编程界面，提高开发效率
6. **专业性**: 深度支持医疗、工业、教育、金融等专业应用领域
7. **智能化**: 集成AI功能，提供智能推荐和自动化辅助
8. **安全性**: 完善的加密和安全机制，保障数据和系统安全

### 应用价值和影响

#### 开发效率提升
- **可视化编程**: 降低编程门槛，提高开发效率300%以上
- **模块化复用**: 节点可重复使用，减少重复开发工作
- **快速原型**: 快速构建应用原型，缩短开发周期
- **团队协作**: 统一的开发模式，促进团队协作

#### 技术能力扩展
- **跨领域整合**: 轻松整合AI、物理、网络等多种技术
- **专业应用**: 支持医疗、工业、教育等专业领域应用开发
- **前沿技术**: 集成最新的AI、区块链、XR等前沿技术
- **标准化开发**: 遵循行业标准，确保兼容性和可维护性

#### 商业价值创造
- **降低成本**: 减少开发时间和人力成本
- **提高质量**: 标准化组件确保应用质量
- **加速创新**: 快速验证和实现创新想法
- **市场竞争**: 提升产品开发速度和技术竞争力

### 未来发展展望

这个包含413个节点的视觉脚本系统已经成为一个功能完整、性能优秀的企业级可视化编程平台。它不仅为当前的应用开发提供了强大支撑，更为未来的技术发展奠定了坚实基础。随着AI技术的不断进步和应用场景的不断扩展，这个系统将继续演进，为数字化转型和智能化应用开发提供更加强大的技术支撑。

通过本使用指南的详细介绍，开发者可以充分利用这413个节点的强大功能，构建出高质量、高性能的现代化应用，推动各行各业的数字化创新和发展。
