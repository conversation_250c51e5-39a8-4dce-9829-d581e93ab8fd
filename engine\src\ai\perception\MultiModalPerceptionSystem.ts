/**
 * 多模态感知系统
 * 
 * 提供全面的感知能力，整合视觉、听觉、触觉、社交等多种感知模态。
 * 支持感知融合、上下文理解和预测性感知。
 */

import { EventEmitter } from 'events';
import * as THREE from 'three';

/**
 * 感知模态类型
 */
export enum PerceptionModality {
  VISUAL = 'visual',           // 视觉感知
  AUDITORY = 'auditory',       // 听觉感知
  TACTILE = 'tactile',         // 触觉感知
  SOCIAL = 'social',           // 社交感知
  ENVIRONMENTAL = 'environmental', // 环境感知
  TEMPORAL = 'temporal',       // 时间感知
  SPATIAL = 'spatial'          // 空间感知
}

/**
 * 感知数据接口
 */
export interface PerceptionData {
  modality: PerceptionModality;
  timestamp: number;
  confidence: number;
  source: string;
  data: any;
  metadata?: { [key: string]: any };
}

/**
 * 视觉感知数据
 */
export interface VisualPerceptionData extends PerceptionData {
  modality: PerceptionModality.VISUAL;
  data: {
    objects: DetectedObject[];
    lighting: LightingInfo;
    visibility: number;
    fieldOfView: number;
    viewDirection: THREE.Vector3;
  };
}

/**
 * 检测到的对象
 */
export interface DetectedObject {
  id: string;
  type: string;
  position: THREE.Vector3;
  size: THREE.Vector3;
  distance: number;
  velocity: THREE.Vector3;
  confidence: number;
  attributes: { [key: string]: any };
}

/**
 * 光照信息
 */
export interface LightingInfo {
  intensity: number;
  direction: THREE.Vector3;
  color: THREE.Color;
  shadows: boolean;
}

/**
 * 听觉感知数据
 */
export interface AuditoryPerceptionData extends PerceptionData {
  modality: PerceptionModality.AUDITORY;
  data: {
    sounds: DetectedSound[];
    ambientNoise: number;
    speechRecognition: SpeechData[];
  };
}

/**
 * 检测到的声音
 */
export interface DetectedSound {
  id: string;
  type: string;
  source: THREE.Vector3;
  volume: number;
  frequency: number;
  duration: number;
  confidence: number;
}

/**
 * 语音数据
 */
export interface SpeechData {
  speaker: string;
  text: string;
  emotion: string;
  intent: string;
  confidence: number;
}

/**
 * 社交感知数据
 */
export interface SocialPerceptionData extends PerceptionData {
  modality: PerceptionModality.SOCIAL;
  data: {
    nearbyEntities: SocialEntity[];
    interactions: SocialInteraction[];
    groupDynamics: GroupDynamics;
    communicationEvents: CommunicationEvent[];
  };
}

/**
 * 社交实体
 */
export interface SocialEntity {
  id: string;
  type: string;
  position: THREE.Vector3;
  relationship: number; // -1 到 1，表示关系好坏
  trustLevel: number;   // 0 到 1，表示信任程度
  emotionalState: string;
  currentActivity: string;
  attentionFocus: string;
}

/**
 * 社交交互
 */
export interface SocialInteraction {
  participants: string[];
  type: string;
  intensity: number;
  duration: number;
  outcome: string;
}

/**
 * 群体动态
 */
export interface GroupDynamics {
  groupSize: number;
  cohesion: number;
  leadership: string[];
  conflicts: string[];
  cooperation: number;
}

/**
 * 通信事件
 */
export interface CommunicationEvent {
  sender: string;
  receiver: string;
  type: string;
  content: any;
  timestamp: number;
}

/**
 * 感知融合结果
 */
export interface FusedPerceptionData {
  timestamp: number;
  confidence: number;
  worldModel: WorldModel;
  attentionFocus: AttentionFocus[];
  predictions: PerceptionPrediction[];
  anomalies: PerceptionAnomaly[];
}

/**
 * 世界模型
 */
export interface WorldModel {
  entities: Map<string, EntityModel>;
  environment: EnvironmentModel;
  social: SocialModel;
  temporal: TemporalModel;
}

/**
 * 实体模型
 */
export interface EntityModel {
  id: string;
  type: string;
  position: THREE.Vector3;
  velocity: THREE.Vector3;
  properties: { [key: string]: any };
  lastSeen: number;
  confidence: number;
}

/**
 * 环境模型
 */
export interface EnvironmentModel {
  layout: any;
  lighting: LightingInfo;
  weather: any;
  obstacles: any[];
  resources: any[];
  hazards: any[];
}

/**
 * 社交模型
 */
export interface SocialModel {
  relationships: Map<string, number>;
  groups: any[];
  hierarchies: any[];
  norms: any[];
}

/**
 * 时间模型
 */
export interface TemporalModel {
  currentTime: number;
  timeOfDay: string;
  schedule: any[];
  patterns: any[];
}

/**
 * 注意力焦点
 */
export interface AttentionFocus {
  target: string;
  type: string;
  priority: number;
  reason: string;
  duration: number;
}

/**
 * 感知预测
 */
export interface PerceptionPrediction {
  type: string;
  target: string;
  prediction: any;
  confidence: number;
  timeHorizon: number;
}

/**
 * 感知异常
 */
export interface PerceptionAnomaly {
  type: string;
  description: string;
  severity: number;
  location?: THREE.Vector3;
  timestamp: number;
}

/**
 * 感知配置
 */
export interface PerceptionConfig {
  maxHistorySize: number;
  updateFrequency: number;
  enablePersistence: boolean;
  enablePerformanceMonitoring: boolean;
  confidenceThreshold: number;
  fusionAlgorithm: string;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  totalProcessingTime: number;
  averageProcessingTime: number;
  processedCount: number;
  errorCount: number;
  modalityPerformance: Map<PerceptionModality, ModalityMetrics>;
  lastUpdateTime: number;
}

/**
 * 模态性能指标
 */
export interface ModalityMetrics {
  processingTime: number;
  successCount: number;
  errorCount: number;
  averageConfidence: number;
  lastProcessTime: number;
}

/**
 * 感知处理器接口
 */
export interface PerceptionProcessor {
  modality: PerceptionModality;
  process(rawData: any): PerceptionData;
  isEnabled(): boolean;
  setEnabled(enabled: boolean): void;
}

/**
 * 视觉感知处理器
 */
export class VisualPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.VISUAL;
  private enabled = true;
  private detectionRange = 100;
  private fieldOfView = Math.PI / 3; // 60度

  public process(rawData: any): VisualPerceptionData {
    const objects = this.detectObjects(rawData);
    const lighting = this.analyzeLighting(rawData);
    
    return {
      modality: PerceptionModality.VISUAL,
      timestamp: Date.now(),
      confidence: 0.8,
      source: 'visual_processor',
      data: {
        objects,
        lighting,
        visibility: this.calculateVisibility(lighting),
        fieldOfView: this.fieldOfView,
        viewDirection: rawData.viewDirection || new THREE.Vector3(0, 0, -1)
      }
    };
  }

  private detectObjects(rawData: any): DetectedObject[] {
    // 简化的对象检测逻辑
    const objects: DetectedObject[] = [];
    
    if (rawData.entities) {
      for (const entity of rawData.entities) {
        const distance = entity.position.distanceTo(rawData.observerPosition);
        
        if (distance <= this.detectionRange) {
          objects.push({
            id: entity.id,
            type: entity.type,
            position: entity.position,
            size: entity.size || new THREE.Vector3(1, 1, 1),
            distance,
            velocity: entity.velocity || new THREE.Vector3(0, 0, 0),
            confidence: Math.max(0, 1 - distance / this.detectionRange),
            attributes: entity.attributes || {}
          });
        }
      }
    }
    
    return objects;
  }

  private analyzeLighting(rawData: any): LightingInfo {
    return {
      intensity: rawData.lightIntensity || 1.0,
      direction: rawData.lightDirection || new THREE.Vector3(0, -1, 0),
      color: rawData.lightColor || new THREE.Color(1, 1, 1),
      shadows: rawData.shadows || false
    };
  }

  private calculateVisibility(lighting: LightingInfo): number {
    return Math.min(lighting.intensity, 1.0);
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 听觉感知处理器
 */
export class AuditoryPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.AUDITORY;
  private enabled = true;
  private hearingRange = 50;

  public process(rawData: any): AuditoryPerceptionData {
    const sounds = this.detectSounds(rawData);
    const speechData = this.processSpeech(rawData);

    return {
      modality: PerceptionModality.AUDITORY,
      timestamp: Date.now(),
      confidence: 0.7,
      source: 'auditory_processor',
      data: {
        sounds,
        ambientNoise: rawData.ambientNoise || 0.1,
        speechRecognition: speechData
      }
    };
  }

  private detectSounds(rawData: any): DetectedSound[] {
    const sounds: DetectedSound[] = [];

    if (rawData.audioSources) {
      for (const source of rawData.audioSources) {
        const distance = source.position.distanceTo(rawData.observerPosition);

        if (distance <= this.hearingRange) {
          sounds.push({
            id: source.id,
            type: source.type,
            source: source.position,
            volume: source.volume * (1 - distance / this.hearingRange),
            frequency: source.frequency || 440,
            duration: source.duration || 1000,
            confidence: Math.max(0, 1 - distance / this.hearingRange)
          });
        }
      }
    }

    return sounds;
  }

  private processSpeech(rawData: any): SpeechData[] {
    // 简化的语音处理逻辑
    const speechData: SpeechData[] = [];

    if (rawData.speechEvents) {
      for (const event of rawData.speechEvents) {
        speechData.push({
          speaker: event.speaker,
          text: event.text,
          emotion: event.emotion || 'neutral',
          intent: event.intent || 'unknown',
          confidence: event.confidence || 0.5
        });
      }
    }

    return speechData;
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 触觉感知数据
 */
export interface TactilePerceptionData extends PerceptionData {
  modality: PerceptionModality.TACTILE;
  data: {
    contacts: TactileContact[];
    pressure: number;
    temperature: number;
    texture: string;
    vibration: VibrationData;
  };
}

/**
 * 触觉接触
 */
export interface TactileContact {
  id: string;
  position: THREE.Vector3;
  normal: THREE.Vector3;
  force: number;
  area: number;
  material: string;
  duration: number;
}

/**
 * 振动数据
 */
export interface VibrationData {
  frequency: number;
  amplitude: number;
  pattern: string;
  duration: number;
}

/**
 * 触觉感知处理器
 */
export class TactilePerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.TACTILE;
  private enabled = true;
  private sensitivityThreshold = 0.1;

  public process(rawData: any): TactilePerceptionData {
    const contacts = this.detectContacts(rawData);
    const vibration = this.analyzeVibration(rawData);

    return {
      modality: PerceptionModality.TACTILE,
      timestamp: Date.now(),
      confidence: 0.9,
      source: 'tactile_processor',
      data: {
        contacts,
        pressure: rawData.pressure || 0,
        temperature: rawData.temperature || 20,
        texture: rawData.texture || 'smooth',
        vibration
      }
    };
  }

  private detectContacts(rawData: any): TactileContact[] {
    const contacts: TactileContact[] = [];

    if (rawData.collisions) {
      for (const collision of rawData.collisions) {
        if (collision.force > this.sensitivityThreshold) {
          contacts.push({
            id: collision.id,
            position: collision.position,
            normal: collision.normal,
            force: collision.force,
            area: collision.area || 1,
            material: collision.material || 'unknown',
            duration: collision.duration || 100
          });
        }
      }
    }

    return contacts;
  }

  private analyzeVibration(rawData: any): VibrationData {
    return {
      frequency: rawData.vibrationFrequency || 0,
      amplitude: rawData.vibrationAmplitude || 0,
      pattern: rawData.vibrationPattern || 'none',
      duration: rawData.vibrationDuration || 0
    };
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 社交感知处理器
 */
export class SocialPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.SOCIAL;
  private enabled = true;
  private socialRange = 30;

  public process(rawData: any): SocialPerceptionData {
    const nearbyEntities = this.detectNearbyEntities(rawData);
    const interactions = this.analyzeInteractions(rawData);
    const groupDynamics = this.analyzeGroupDynamics(nearbyEntities);
    const communicationEvents = this.processCommunication(rawData);

    return {
      modality: PerceptionModality.SOCIAL,
      timestamp: Date.now(),
      confidence: 0.8,
      source: 'social_processor',
      data: {
        nearbyEntities,
        interactions,
        groupDynamics,
        communicationEvents
      }
    };
  }

  private detectNearbyEntities(rawData: any): SocialEntity[] {
    const entities: SocialEntity[] = [];

    if (rawData.socialEntities) {
      for (const entity of rawData.socialEntities) {
        const distance = entity.position.distanceTo(rawData.observerPosition);

        if (distance <= this.socialRange) {
          entities.push({
            id: entity.id,
            type: entity.type,
            position: entity.position,
            relationship: entity.relationship || 0,
            trustLevel: entity.trustLevel || 0.5,
            emotionalState: entity.emotionalState || 'neutral',
            currentActivity: entity.currentActivity || 'idle',
            attentionFocus: entity.attentionFocus || 'none'
          });
        }
      }
    }

    return entities;
  }

  private analyzeInteractions(rawData: any): SocialInteraction[] {
    const interactions: SocialInteraction[] = [];

    if (rawData.interactions) {
      for (const interaction of rawData.interactions) {
        interactions.push({
          participants: interaction.participants,
          type: interaction.type,
          intensity: interaction.intensity || 0.5,
          duration: interaction.duration || 1000,
          outcome: interaction.outcome || 'neutral'
        });
      }
    }

    return interactions;
  }

  private analyzeGroupDynamics(entities: SocialEntity[]): GroupDynamics {
    return {
      groupSize: entities.length,
      cohesion: this.calculateCohesion(entities),
      leadership: this.identifyLeaders(entities),
      conflicts: this.identifyConflicts(entities),
      cooperation: this.calculateCooperation(entities)
    };
  }

  private calculateCohesion(entities: SocialEntity[]): number {
    if (entities.length < 2) return 1.0;

    let totalRelationship = 0;
    let count = 0;

    for (let i = 0; i < entities.length; i++) {
      for (let j = i + 1; j < entities.length; j++) {
        totalRelationship += entities[i].relationship;
        count++;
      }
    }

    return count > 0 ? Math.max(0, totalRelationship / count) : 0;
  }

  private identifyLeaders(entities: SocialEntity[]): string[] {
    return entities
      .filter(entity => entity.trustLevel > 0.7)
      .map(entity => entity.id);
  }

  private identifyConflicts(entities: SocialEntity[]): string[] {
    return entities
      .filter(entity => entity.relationship < -0.5)
      .map(entity => entity.id);
  }

  private calculateCooperation(entities: SocialEntity[]): number {
    const cooperativeEntities = entities.filter(entity =>
      entity.currentActivity.includes('cooperat') || entity.relationship > 0.3
    );

    return entities.length > 0 ? cooperativeEntities.length / entities.length : 0;
  }

  private processCommunication(rawData: any): CommunicationEvent[] {
    const events: CommunicationEvent[] = [];

    if (rawData.communicationEvents) {
      for (const event of rawData.communicationEvents) {
        events.push({
          sender: event.sender,
          receiver: event.receiver,
          type: event.type,
          content: event.content,
          timestamp: event.timestamp || Date.now()
        });
      }
    }

    return events;
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 环境感知数据
 */
export interface EnvironmentalPerceptionData extends PerceptionData {
  modality: PerceptionModality.ENVIRONMENTAL;
  data: {
    weather: WeatherData;
    terrain: TerrainData;
    hazards: EnvironmentalHazard[];
    resources: EnvironmentalResource[];
    airQuality: number;
    noiseLevel: number;
  };
}

/**
 * 天气数据
 */
export interface WeatherData {
  type: string;
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection: THREE.Vector3;
  precipitation: number;
  visibility: number;
}

/**
 * 地形数据
 */
export interface TerrainData {
  type: string;
  elevation: number;
  slope: number;
  roughness: number;
  material: string;
}

/**
 * 环境危险
 */
export interface EnvironmentalHazard {
  id: string;
  type: string;
  position: THREE.Vector3;
  severity: number;
  radius: number;
  duration: number;
}

/**
 * 环境资源
 */
export interface EnvironmentalResource {
  id: string;
  type: string;
  position: THREE.Vector3;
  quantity: number;
  quality: number;
  accessibility: number;
}

/**
 * 环境感知处理器
 */
export class EnvironmentalPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.ENVIRONMENTAL;
  private enabled = true;
  private detectionRange = 200;

  public process(rawData: any): EnvironmentalPerceptionData {
    const weather = this.analyzeWeather(rawData);
    const terrain = this.analyzeTerrain(rawData);
    const hazards = this.detectHazards(rawData);
    const resources = this.detectResources(rawData);

    return {
      modality: PerceptionModality.ENVIRONMENTAL,
      timestamp: Date.now(),
      confidence: 0.85,
      source: 'environmental_processor',
      data: {
        weather,
        terrain,
        hazards,
        resources,
        airQuality: rawData.airQuality || 1.0,
        noiseLevel: rawData.noiseLevel || 0.1
      }
    };
  }

  private analyzeWeather(rawData: any): WeatherData {
    return {
      type: rawData.weatherType || 'clear',
      temperature: rawData.temperature || 20,
      humidity: rawData.humidity || 0.5,
      windSpeed: rawData.windSpeed || 0,
      windDirection: rawData.windDirection || new THREE.Vector3(1, 0, 0),
      precipitation: rawData.precipitation || 0,
      visibility: rawData.visibility || 1000
    };
  }

  private analyzeTerrain(rawData: any): TerrainData {
    return {
      type: rawData.terrainType || 'flat',
      elevation: rawData.elevation || 0,
      slope: rawData.slope || 0,
      roughness: rawData.roughness || 0,
      material: rawData.terrainMaterial || 'grass'
    };
  }

  private detectHazards(rawData: any): EnvironmentalHazard[] {
    const hazards: EnvironmentalHazard[] = [];

    if (rawData.hazards) {
      for (const hazard of rawData.hazards) {
        const distance = hazard.position.distanceTo(rawData.observerPosition);

        if (distance <= this.detectionRange) {
          hazards.push({
            id: hazard.id,
            type: hazard.type,
            position: hazard.position,
            severity: hazard.severity || 0.5,
            radius: hazard.radius || 10,
            duration: hazard.duration || -1
          });
        }
      }
    }

    return hazards;
  }

  private detectResources(rawData: any): EnvironmentalResource[] {
    const resources: EnvironmentalResource[] = [];

    if (rawData.resources) {
      for (const resource of rawData.resources) {
        const distance = resource.position.distanceTo(rawData.observerPosition);

        if (distance <= this.detectionRange) {
          resources.push({
            id: resource.id,
            type: resource.type,
            position: resource.position,
            quantity: resource.quantity || 1,
            quality: resource.quality || 1,
            accessibility: Math.max(0, 1 - distance / this.detectionRange)
          });
        }
      }
    }

    return resources;
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 时间感知数据
 */
export interface TemporalPerceptionData extends PerceptionData {
  modality: PerceptionModality.TEMPORAL;
  data: {
    currentTime: number;
    timeOfDay: string;
    season: string;
    timeFlow: number;
    scheduleEvents: ScheduleEvent[];
    temporalPatterns: TemporalPattern[];
  };
}

/**
 * 计划事件
 */
export interface ScheduleEvent {
  id: string;
  name: string;
  startTime: number;
  endTime: number;
  priority: number;
  participants: string[];
}

/**
 * 时间模式
 */
export interface TemporalPattern {
  type: string;
  frequency: number;
  lastOccurrence: number;
  predictedNext: number;
  confidence: number;
}

/**
 * 时间感知处理器
 */
export class TemporalPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.TEMPORAL;
  private enabled = true;

  public process(rawData: any): TemporalPerceptionData {
    const currentTime = Date.now();
    const timeOfDay = this.calculateTimeOfDay(currentTime);
    const season = this.calculateSeason(currentTime);
    const scheduleEvents = this.getScheduleEvents(rawData);
    const temporalPatterns = this.analyzeTemporalPatterns(rawData);

    return {
      modality: PerceptionModality.TEMPORAL,
      timestamp: currentTime,
      confidence: 1.0,
      source: 'temporal_processor',
      data: {
        currentTime,
        timeOfDay,
        season,
        timeFlow: rawData.timeFlow || 1.0,
        scheduleEvents,
        temporalPatterns
      }
    };
  }

  private calculateTimeOfDay(timestamp: number): string {
    const hour = new Date(timestamp).getHours();

    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  }

  private calculateSeason(timestamp: number): string {
    const month = new Date(timestamp).getMonth();

    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  private getScheduleEvents(rawData: any): ScheduleEvent[] {
    const events: ScheduleEvent[] = [];

    if (rawData.scheduleEvents) {
      for (const event of rawData.scheduleEvents) {
        events.push({
          id: event.id,
          name: event.name,
          startTime: event.startTime,
          endTime: event.endTime,
          priority: event.priority || 0.5,
          participants: event.participants || []
        });
      }
    }

    return events;
  }

  private analyzeTemporalPatterns(rawData: any): TemporalPattern[] {
    const patterns: TemporalPattern[] = [];

    if (rawData.temporalPatterns) {
      for (const pattern of rawData.temporalPatterns) {
        patterns.push({
          type: pattern.type,
          frequency: pattern.frequency,
          lastOccurrence: pattern.lastOccurrence,
          predictedNext: pattern.predictedNext,
          confidence: pattern.confidence || 0.5
        });
      }
    }

    return patterns;
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 空间感知数据
 */
export interface SpatialPerceptionData extends PerceptionData {
  modality: PerceptionModality.SPATIAL;
  data: {
    layout: SpatialLayout;
    navigation: NavigationData;
    boundaries: SpaceBoundary[];
    landmarks: Landmark[];
    spatialRelations: SpatialRelation[];
  };
}

/**
 * 空间布局
 */
export interface SpatialLayout {
  type: string;
  dimensions: THREE.Vector3;
  center: THREE.Vector3;
  orientation: THREE.Quaternion;
  accessibility: number;
}

/**
 * 导航数据
 */
export interface NavigationData {
  currentPosition: THREE.Vector3;
  currentDirection: THREE.Vector3;
  availablePaths: Path[];
  obstacles: Obstacle[];
  clearance: number;
}

/**
 * 路径
 */
export interface Path {
  id: string;
  waypoints: THREE.Vector3[];
  difficulty: number;
  length: number;
  estimatedTime: number;
}

/**
 * 障碍物
 */
export interface Obstacle {
  id: string;
  position: THREE.Vector3;
  size: THREE.Vector3;
  type: string;
  movable: boolean;
}

/**
 * 空间边界
 */
export interface SpaceBoundary {
  id: string;
  type: string;
  points: THREE.Vector3[];
  height: number;
  permeability: number;
}

/**
 * 地标
 */
export interface Landmark {
  id: string;
  name: string;
  position: THREE.Vector3;
  type: string;
  visibility: number;
  significance: number;
}

/**
 * 空间关系
 */
export interface SpatialRelation {
  object1: string;
  object2: string;
  relation: string;
  distance: number;
  confidence: number;
}

/**
 * 空间感知处理器
 */
export class SpatialPerceptionProcessor implements PerceptionProcessor {
  public modality = PerceptionModality.SPATIAL;
  private enabled = true;
  private spatialRange = 100;

  public process(rawData: any): SpatialPerceptionData {
    const layout = this.analyzeSpatialLayout(rawData);
    const navigation = this.analyzeNavigation(rawData);
    const boundaries = this.detectBoundaries(rawData);
    const landmarks = this.detectLandmarks(rawData);
    const spatialRelations = this.analyzeSpatialRelations(rawData);

    return {
      modality: PerceptionModality.SPATIAL,
      timestamp: Date.now(),
      confidence: 0.85,
      source: 'spatial_processor',
      data: {
        layout,
        navigation,
        boundaries,
        landmarks,
        spatialRelations
      }
    };
  }

  private analyzeSpatialLayout(rawData: any): SpatialLayout {
    return {
      type: rawData.spaceType || 'open',
      dimensions: rawData.spaceDimensions || new THREE.Vector3(100, 10, 100),
      center: rawData.spaceCenter || new THREE.Vector3(0, 0, 0),
      orientation: rawData.spaceOrientation || new THREE.Quaternion(),
      accessibility: rawData.accessibility || 1.0
    };
  }

  private analyzeNavigation(rawData: any): NavigationData {
    const availablePaths = this.findAvailablePaths(rawData);
    const obstacles = this.detectObstacles(rawData);

    return {
      currentPosition: rawData.observerPosition || new THREE.Vector3(0, 0, 0),
      currentDirection: rawData.observerDirection || new THREE.Vector3(0, 0, -1),
      availablePaths,
      obstacles,
      clearance: this.calculateClearance(rawData.observerPosition, obstacles)
    };
  }

  private findAvailablePaths(rawData: any): Path[] {
    const paths: Path[] = [];

    if (rawData.navigationPaths) {
      for (const pathData of rawData.navigationPaths) {
        paths.push({
          id: pathData.id,
          waypoints: pathData.waypoints || [],
          difficulty: pathData.difficulty || 0.5,
          length: this.calculatePathLength(pathData.waypoints || []),
          estimatedTime: pathData.estimatedTime || 60000
        });
      }
    }

    return paths;
  }

  private detectObstacles(rawData: any): Obstacle[] {
    const obstacles: Obstacle[] = [];

    if (rawData.obstacles) {
      for (const obstacle of rawData.obstacles) {
        const distance = obstacle.position.distanceTo(rawData.observerPosition);

        if (distance <= this.spatialRange) {
          obstacles.push({
            id: obstacle.id,
            position: obstacle.position,
            size: obstacle.size || new THREE.Vector3(1, 1, 1),
            type: obstacle.type || 'static',
            movable: obstacle.movable || false
          });
        }
      }
    }

    return obstacles;
  }

  private detectBoundaries(rawData: any): SpaceBoundary[] {
    const boundaries: SpaceBoundary[] = [];

    if (rawData.boundaries) {
      for (const boundary of rawData.boundaries) {
        boundaries.push({
          id: boundary.id,
          type: boundary.type || 'wall',
          points: boundary.points || [],
          height: boundary.height || 3,
          permeability: boundary.permeability || 0
        });
      }
    }

    return boundaries;
  }

  private detectLandmarks(rawData: any): Landmark[] {
    const landmarks: Landmark[] = [];

    if (rawData.landmarks) {
      for (const landmark of rawData.landmarks) {
        const distance = landmark.position.distanceTo(rawData.observerPosition);

        if (distance <= this.spatialRange) {
          landmarks.push({
            id: landmark.id,
            name: landmark.name,
            position: landmark.position,
            type: landmark.type || 'generic',
            visibility: Math.max(0, 1 - distance / this.spatialRange),
            significance: landmark.significance || 0.5
          });
        }
      }
    }

    return landmarks;
  }

  private analyzeSpatialRelations(rawData: any): SpatialRelation[] {
    const relations: SpatialRelation[] = [];

    if (rawData.entities && rawData.entities.length > 1) {
      for (let i = 0; i < rawData.entities.length; i++) {
        for (let j = i + 1; j < rawData.entities.length; j++) {
          const entity1 = rawData.entities[i];
          const entity2 = rawData.entities[j];
          const distance = entity1.position.distanceTo(entity2.position);

          let relation = 'near';
          if (distance > 50) relation = 'far';
          else if (distance < 5) relation = 'adjacent';

          relations.push({
            object1: entity1.id,
            object2: entity2.id,
            relation,
            distance,
            confidence: 0.9
          });
        }
      }
    }

    return relations;
  }

  private calculatePathLength(waypoints: THREE.Vector3[]): number {
    let length = 0;
    for (let i = 1; i < waypoints.length; i++) {
      length += waypoints[i].distanceTo(waypoints[i - 1]);
    }
    return length;
  }

  private calculateClearance(position: THREE.Vector3, obstacles: Obstacle[]): number {
    let minDistance = Infinity;

    for (const obstacle of obstacles) {
      const distance = position.distanceTo(obstacle.position) - obstacle.size.length() / 2;
      minDistance = Math.min(minDistance, distance);
    }

    return minDistance === Infinity ? 100 : Math.max(0, minDistance);
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

/**
 * 多模态感知系统
 */
export class MultiModalPerceptionSystem {
  private processors: Map<PerceptionModality, PerceptionProcessor> = new Map();
  private perceptionHistory: PerceptionData[] = [];
  private worldModel: WorldModel;
  private eventEmitter = new EventEmitter();
  private fusionEnabled = true;
  private maxHistorySize = 1000;
  private performanceMetrics: PerformanceMetrics;
  private config: PerceptionConfig;

  constructor(config?: Partial<PerceptionConfig>) {
    this.config = {
      maxHistorySize: 1000,
      updateFrequency: 100,
      enablePersistence: false,
      enablePerformanceMonitoring: true,
      confidenceThreshold: 0.3,
      fusionAlgorithm: 'weighted_average',
      ...config
    };

    this.maxHistorySize = this.config.maxHistorySize;
    this.performanceMetrics = this.initializePerformanceMetrics();
    this.initializeWorldModel();
    this.initializeDefaultProcessors();
  }

  /**
   * 初始化世界模型
   */
  private initializeWorldModel(): void {
    this.worldModel = {
      entities: new Map(),
      environment: {
        layout: {},
        lighting: {
          intensity: 1.0,
          direction: new THREE.Vector3(0, -1, 0),
          color: new THREE.Color(1, 1, 1),
          shadows: false
        },
        weather: {},
        obstacles: [],
        resources: [],
        hazards: []
      },
      social: {
        relationships: new Map(),
        groups: [],
        hierarchies: [],
        norms: []
      },
      temporal: {
        currentTime: Date.now(),
        timeOfDay: 'day',
        schedule: [],
        patterns: []
      }
    };
  }

  /**
   * 初始化性能指标
   */
  private initializePerformanceMetrics(): PerformanceMetrics {
    return {
      totalProcessingTime: 0,
      averageProcessingTime: 0,
      processedCount: 0,
      errorCount: 0,
      modalityPerformance: new Map(),
      lastUpdateTime: Date.now()
    };
  }

  /**
   * 初始化默认处理器
   */
  private initializeDefaultProcessors(): void {
    this.addProcessor(new VisualPerceptionProcessor());
    this.addProcessor(new AuditoryPerceptionProcessor());
    this.addProcessor(new TactilePerceptionProcessor());
    this.addProcessor(new SocialPerceptionProcessor());
    this.addProcessor(new EnvironmentalPerceptionProcessor());
    this.addProcessor(new TemporalPerceptionProcessor());
    this.addProcessor(new SpatialPerceptionProcessor());
  }

  /**
   * 添加感知处理器
   */
  public addProcessor(processor: PerceptionProcessor): void {
    this.processors.set(processor.modality, processor);
  }

  /**
   * 移除感知处理器
   */
  public removeProcessor(modality: PerceptionModality): boolean {
    return this.processors.delete(modality);
  }

  /**
   * 处理感知数据
   */
  public processPerception(rawData: any): FusedPerceptionData {
    const startTime = performance.now();
    const perceptionData: PerceptionData[] = [];

    // 验证输入数据
    if (!this.validateRawData(rawData)) {
      throw new Error('无效的原始感知数据');
    }

    // 使用各个处理器处理原始数据
    for (const [modality, processor] of this.processors) {
      if (processor.isEnabled()) {
        try {
          const processingStartTime = performance.now();
          const data = processor.process(rawData);

          // 验证处理结果
          if (this.validatePerceptionData(data)) {
            perceptionData.push(data);
            this.recordPerception(data);

            // 更新性能指标
            if (this.config.enablePerformanceMonitoring) {
              this.updateModalityMetrics(modality, performance.now() - processingStartTime, true, data.confidence);
            }
          }
        } catch (error) {
          console.error(`感知处理器 ${modality} 处理失败:`, error);

          // 更新错误统计
          if (this.config.enablePerformanceMonitoring) {
            this.updateModalityMetrics(modality, 0, false, 0);
            this.performanceMetrics.errorCount++;
          }
        }
      }
    }

    // 融合感知数据
    const fusedData = this.fusePerceptionData(perceptionData);

    // 更新世界模型
    this.updateWorldModel(fusedData);

    // 更新总体性能指标
    if (this.config.enablePerformanceMonitoring) {
      const totalTime = performance.now() - startTime;
      this.updatePerformanceMetrics(totalTime);
    }

    // 触发事件
    this.eventEmitter.emit('perceptionProcessed', fusedData);

    return fusedData;
  }

  /**
   * 验证原始数据
   */
  private validateRawData(rawData: any): boolean {
    if (!rawData || typeof rawData !== 'object') {
      return false;
    }

    // 检查必要的字段
    if (!rawData.observerPosition) {
      console.warn('缺少观察者位置信息');
      rawData.observerPosition = new THREE.Vector3(0, 0, 0);
    }

    return true;
  }

  /**
   * 验证感知数据
   */
  private validatePerceptionData(data: PerceptionData): boolean {
    if (!data || !data.modality || !data.timestamp || data.confidence < 0 || data.confidence > 1) {
      return false;
    }

    // 检查置信度阈值
    if (data.confidence < this.config.confidenceThreshold) {
      console.warn(`感知数据置信度过低: ${data.confidence}`);
      return false;
    }

    return true;
  }

  /**
   * 更新模态性能指标
   */
  private updateModalityMetrics(modality: PerceptionModality, processingTime: number, success: boolean, confidence: number): void {
    let metrics = this.performanceMetrics.modalityPerformance.get(modality);

    if (!metrics) {
      metrics = {
        processingTime: 0,
        successCount: 0,
        errorCount: 0,
        averageConfidence: 0,
        lastProcessTime: 0
      };
      this.performanceMetrics.modalityPerformance.set(modality, metrics);
    }

    metrics.processingTime += processingTime;
    metrics.lastProcessTime = processingTime;

    if (success) {
      metrics.successCount++;
      metrics.averageConfidence = (metrics.averageConfidence * (metrics.successCount - 1) + confidence) / metrics.successCount;
    } else {
      metrics.errorCount++;
    }
  }

  /**
   * 更新总体性能指标
   */
  private updatePerformanceMetrics(processingTime: number): void {
    this.performanceMetrics.totalProcessingTime += processingTime;
    this.performanceMetrics.processedCount++;
    this.performanceMetrics.averageProcessingTime = this.performanceMetrics.totalProcessingTime / this.performanceMetrics.processedCount;
    this.performanceMetrics.lastUpdateTime = Date.now();
  }

  /**
   * 记录感知数据
   */
  private recordPerception(data: PerceptionData): void {
    this.perceptionHistory.push(data);
    
    // 限制历史记录大小
    if (this.perceptionHistory.length > this.maxHistorySize) {
      this.perceptionHistory.shift();
    }
  }

  /**
   * 融合感知数据
   */
  private fusePerceptionData(perceptionData: PerceptionData[]): FusedPerceptionData {
    const timestamp = Date.now();
    let totalConfidence = 0;
    
    // 计算总体置信度
    for (const data of perceptionData) {
      totalConfidence += data.confidence;
    }
    const averageConfidence = perceptionData.length > 0 ? totalConfidence / perceptionData.length : 0;

    // 生成注意力焦点
    const attentionFocus = this.generateAttentionFocus(perceptionData);
    
    // 生成预测
    const predictions = this.generatePredictions(perceptionData);
    
    // 检测异常
    const anomalies = this.detectAnomalies(perceptionData);

    return {
      timestamp,
      confidence: averageConfidence,
      worldModel: this.worldModel,
      attentionFocus,
      predictions,
      anomalies
    };
  }

  /**
   * 生成注意力焦点
   */
  private generateAttentionFocus(perceptionData: PerceptionData[]): AttentionFocus[] {
    const focuses: AttentionFocus[] = [];
    
    // 基于感知数据生成注意力焦点
    for (const data of perceptionData) {
      if (data.modality === PerceptionModality.VISUAL) {
        const visualData = data as VisualPerceptionData;
        for (const obj of visualData.data.objects) {
          if (obj.confidence > 0.7) {
            focuses.push({
              target: obj.id,
              type: 'visual_object',
              priority: obj.confidence,
              reason: `高置信度视觉对象 (${obj.confidence.toFixed(2)})`,
              duration: 5000
            });
          }
        }
      }
    }
    
    // 按优先级排序
    focuses.sort((a, b) => b.priority - a.priority);
    
    return focuses.slice(0, 5); // 最多5个焦点
  }

  /**
   * 生成预测
   */
  private generatePredictions(perceptionData: PerceptionData[]): PerceptionPrediction[] {
    const predictions: PerceptionPrediction[] = [];

    // 基于视觉数据预测对象运动
    for (const data of perceptionData) {
      if (data.modality === PerceptionModality.VISUAL) {
        const visualData = data as VisualPerceptionData;
        for (const obj of visualData.data.objects) {
          if (obj.velocity.length() > 0) {
            const futurePosition = obj.position.clone().add(
              obj.velocity.clone().multiplyScalar(5) // 预测5秒后的位置
            );

            predictions.push({
              type: 'object_movement',
              target: obj.id,
              prediction: {
                position: futurePosition,
                confidence: obj.confidence * 0.8 // 预测置信度降低
              },
              confidence: obj.confidence * 0.8,
              timeHorizon: 5000
            });
          }
        }
      }

      // 基于社交数据预测交互
      if (data.modality === PerceptionModality.SOCIAL) {
        const socialData = data as SocialPerceptionData;
        for (const interaction of socialData.data.interactions) {
          if (interaction.type === 'conversation' && interaction.intensity > 0.7) {
            predictions.push({
              type: 'social_interaction',
              target: interaction.participants.join(','),
              prediction: {
                duration: interaction.duration * 1.5,
                outcome: 'positive'
              },
              confidence: 0.6,
              timeHorizon: 10000
            });
          }
        }
      }

      // 基于环境数据预测天气变化
      if (data.modality === PerceptionModality.ENVIRONMENTAL) {
        const envData = data as EnvironmentalPerceptionData;
        if (envData.data.weather.humidity > 0.8 && envData.data.weather.temperature > 25) {
          predictions.push({
            type: 'weather_change',
            target: 'environment',
            prediction: {
              weatherType: 'rain',
              probability: 0.7
            },
            confidence: 0.7,
            timeHorizon: 3600000 // 1小时
          });
        }
      }
    }

    return predictions;
  }

  /**
   * 检测异常
   */
  private detectAnomalies(perceptionData: PerceptionData[]): PerceptionAnomaly[] {
    const anomalies: PerceptionAnomaly[] = [];
    
    // 检测感知异常
    for (const data of perceptionData) {
      if (data.confidence < 0.3) {
        anomalies.push({
          type: 'low_confidence',
          description: `${data.modality} 感知置信度过低`,
          severity: 0.5,
          timestamp: data.timestamp
        });
      }
    }
    
    return anomalies;
  }

  /**
   * 更新世界模型
   */
  private updateWorldModel(fusedData: FusedPerceptionData): void {
    // 更新时间信息
    this.worldModel.temporal.currentTime = fusedData.timestamp;
    
    // 这里可以添加更复杂的世界模型更新逻辑
  }

  /**
   * 获取世界模型
   */
  public getWorldModel(): WorldModel {
    return this.worldModel;
  }

  /**
   * 获取感知历史
   */
  public getPerceptionHistory(modality?: PerceptionModality, limit?: number): PerceptionData[] {
    let history = this.perceptionHistory;
    
    if (modality) {
      history = history.filter(data => data.modality === modality);
    }
    
    if (limit) {
      history = history.slice(-limit);
    }
    
    return history;
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 重置性能指标
   */
  public resetPerformanceMetrics(): void {
    this.performanceMetrics = this.initializePerformanceMetrics();
  }

  /**
   * 获取配置
   */
  public getConfig(): PerceptionConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PerceptionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.maxHistorySize = this.config.maxHistorySize;
  }

  /**
   * 清理感知历史
   */
  public clearHistory(): void {
    this.perceptionHistory = [];
  }

  /**
   * 获取处理器状态
   */
  public getProcessorStatus(): Map<PerceptionModality, boolean> {
    const status = new Map<PerceptionModality, boolean>();
    for (const [modality, processor] of this.processors) {
      status.set(modality, processor.isEnabled());
    }
    return status;
  }

  /**
   * 启用/禁用处理器
   */
  public setProcessorEnabled(modality: PerceptionModality, enabled: boolean): boolean {
    const processor = this.processors.get(modality);
    if (processor) {
      processor.setEnabled(enabled);
      return true;
    }
    return false;
  }

  /**
   * 获取融合算法列表
   */
  public getAvailableFusionAlgorithms(): string[] {
    return ['weighted_average', 'bayesian_fusion', 'dempster_shafer', 'neural_fusion'];
  }

  /**
   * 设置融合算法
   */
  public setFusionAlgorithm(algorithm: string): boolean {
    const available = this.getAvailableFusionAlgorithms();
    if (available.includes(algorithm)) {
      this.config.fusionAlgorithm = algorithm;
      return true;
    }
    return false;
  }

  /**
   * 导出感知数据
   */
  public exportPerceptionData(format: 'json' | 'csv' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify({
        history: this.perceptionHistory,
        worldModel: this.serializeWorldModel(),
        metrics: this.performanceMetrics,
        config: this.config
      }, null, 2);
    } else {
      // CSV格式导出
      const headers = ['timestamp', 'modality', 'confidence', 'source'];
      const rows = this.perceptionHistory.map(data => [
        data.timestamp,
        data.modality,
        data.confidence,
        data.source
      ]);

      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }
  }

  /**
   * 序列化世界模型
   */
  private serializeWorldModel(): any {
    return {
      entities: Array.from(this.worldModel.entities.entries()),
      environment: this.worldModel.environment,
      social: {
        ...this.worldModel.social,
        relationships: Array.from(this.worldModel.social.relationships.entries())
      },
      temporal: this.worldModel.temporal
    };
  }

  /**
   * 导入感知数据
   */
  public importPerceptionData(data: string): boolean {
    try {
      const parsed = JSON.parse(data);

      if (parsed.history && Array.isArray(parsed.history)) {
        this.perceptionHistory = parsed.history;
      }

      if (parsed.config) {
        this.updateConfig(parsed.config);
      }

      if (parsed.worldModel) {
        this.deserializeWorldModel(parsed.worldModel);
      }

      return true;
    } catch (error) {
      console.error('导入感知数据失败:', error);
      return false;
    }
  }

  /**
   * 反序列化世界模型
   */
  private deserializeWorldModel(data: any): void {
    if (data.entities && Array.isArray(data.entities)) {
      this.worldModel.entities = new Map(data.entities);
    }

    if (data.environment) {
      this.worldModel.environment = data.environment;
    }

    if (data.social) {
      this.worldModel.social = {
        ...data.social,
        relationships: new Map(data.social.relationships || [])
      };
    }

    if (data.temporal) {
      this.worldModel.temporal = data.temporal;
    }
  }

  /**
   * 获取系统状态摘要
   */
  public getSystemSummary(): any {
    return {
      processorsCount: this.processors.size,
      enabledProcessors: Array.from(this.processors.entries())
        .filter(([, processor]) => processor.isEnabled())
        .map(([modality]) => modality),
      historySize: this.perceptionHistory.length,
      entitiesCount: this.worldModel.entities.size,
      lastUpdateTime: this.performanceMetrics.lastUpdateTime,
      averageProcessingTime: this.performanceMetrics.averageProcessingTime,
      errorRate: this.performanceMetrics.errorCount / Math.max(1, this.performanceMetrics.processedCount),
      fusionEnabled: this.fusionEnabled,
      currentConfig: this.config
    };
  }
}
