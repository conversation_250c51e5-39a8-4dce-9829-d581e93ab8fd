/**
 * AI模型加载选项
 */

/**
 * 加载策略
 */
export enum LoadStrategy {
  /** 立即加载 */
  EAGER = 'eager',
  /** 懒加载 */
  LAZY = 'lazy',
  /** 按需加载 */
  ON_DEMAND = 'on_demand',
  /** 预加载 */
  PRELOAD = 'preload',
  /** 流式加载 */
  STREAMING = 'streaming'
}

/**
 * 设备类型
 */
export enum DeviceType {
  /** CPU */
  CPU = 'cpu',
  /** GPU */
  GPU = 'gpu',
  /** TPU */
  TPU = 'tpu',
  /** 自动选择 */
  AUTO = 'auto',
  /** 混合设备 */
  HYBRID = 'hybrid'
}

/**
 * 模型格式
 */
export enum ModelFormat {
  /** ONNX格式 */
  ONNX = 'onnx',
  /** TensorFlow格式 */
  TENSORFLOW = 'tensorflow',
  /** PyTorch格式 */
  PYTORCH = 'pytorch',
  /** TensorFlow Lite格式 */
  TFLITE = 'tflite',
  /** Core ML格式 */
  COREML = 'coreml',
  /** 自定义格式 */
  CUSTOM = 'custom'
}

/**
 * 量化类型
 */
export enum QuantizationType {
  /** 无量化 */
  NONE = 'none',
  /** 动态量化 */
  DYNAMIC = 'dynamic',
  /** 静态量化 */
  STATIC = 'static',
  /** 混合量化 */
  MIXED = 'mixed'
}

/**
 * 加载环境
 */
export enum LoadEnvironment {
  /** 开发环境 */
  DEVELOPMENT = 'development',
  /** 测试环境 */
  TESTING = 'testing',
  /** 生产环境 */
  PRODUCTION = 'production',
  /** 边缘计算 */
  EDGE = 'edge'
}

/**
 * 资源约束
 */
export interface ResourceConstraints {
  /** 最大内存使用量（字节） */
  maxMemory?: number;
  /** 最大GPU内存使用量（字节） */
  maxGpuMemory?: number;
  /** 最大CPU使用率（0-1） */
  maxCpuUsage?: number;
  /** 最大加载时间（毫秒） */
  maxLoadTime?: number;
  /** 最大磁盘使用量（字节） */
  maxDiskUsage?: number;
}

/**
 * 性能优化选项
 */
export interface PerformanceOptions {
  /** 批处理大小 */
  batchSize?: number;
  /** 是否启用并行加载 */
  enableParallelLoading?: boolean;
  /** 并行线程数 */
  parallelThreads?: number;
  /** 是否启用内存映射 */
  enableMemoryMapping?: boolean;
  /** 是否启用预取 */
  enablePrefetch?: boolean;
  /** 预取缓冲区大小 */
  prefetchBufferSize?: number;
  /** 量化类型 */
  quantizationType?: QuantizationType;
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
}

/**
 * 安全选项
 */
export interface SecurityOptions {
  /** 是否验证模型签名 */
  verifySignature?: boolean;
  /** 模型签名 */
  modelSignature?: string;
  /** 是否检查模型完整性 */
  checkIntegrity?: boolean;
  /** 模型哈希值 */
  modelHash?: string;
  /** 是否启用沙箱模式 */
  enableSandbox?: boolean;
  /** 允许的操作列表 */
  allowedOperations?: string[];
}

/**
 * 监控选项
 */
export interface MonitoringOptions {
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用内存监控 */
  enableMemoryMonitoring?: boolean;
  /** 是否启用错误监控 */
  enableErrorMonitoring?: boolean;
  /** 监控间隔（毫秒） */
  monitoringInterval?: number;
  /** 是否记录详细日志 */
  enableDetailedLogging?: boolean;
  /** 日志级别 */
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * 依赖管理选项
 */
export interface DependencyOptions {
  /** 依赖模型列表 */
  dependencies?: string[];
  /** 是否自动解析依赖 */
  autoResolveDependencies?: boolean;
  /** 依赖加载超时时间（毫秒） */
  dependencyTimeout?: number;
  /** 是否允许循环依赖 */
  allowCircularDependencies?: boolean;
}

/**
 * 资源使用情况
 */
export interface ResourceUsage {
  /** CPU使用率（0-1） */
  cpuUsage: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
  /** GPU使用率（0-1） */
  gpuUsage?: number;
  /** GPU内存使用量（字节） */
  gpuMemoryUsage?: number;
  /** 磁盘使用量（字节） */
  diskUsage?: number;
  /** 网络使用量（字节/秒） */
  networkUsage?: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * A/B测试配置
 */
export interface ABTestConfig {
  /** 测试ID */
  testId: string;
  /** 测试组 */
  group: 'A' | 'B' | 'control';
  /** 流量分配比例（0-1） */
  trafficRatio: number;
  /** 测试参数 */
  parameters: Record<string, any>;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 回退选项
 */
export interface FallbackOptions {
  /** 是否启用回退 */
  enabled?: boolean;
  /** 回退模型列表 */
  fallbackModels?: string[];
  /** 回退策略 */
  strategy?: 'sequential' | 'random' | 'weighted';
  /** 回退触发条件 */
  triggers?: {
    /** 加载失败 */
    onLoadFailure?: boolean;
    /** 性能不达标 */
    onPerformanceIssue?: boolean;
    /** 资源不足 */
    onResourceConstraint?: boolean;
    /** 超时 */
    onTimeout?: boolean;
  };
  /** 最大回退次数 */
  maxRetries?: number;
  /** 回退延迟（毫秒） */
  retryDelay?: number;
}

/**
 * AI模型加载选项
 */
export interface AIModelLoadOptions {
  /** 是否强制重新加载 */
  forceReload?: boolean;

  /** 加载超时时间（毫秒） */
  timeout?: number;

  /** 加载优先级 (0-10) */
  priority?: number;

  /** 加载策略 */
  loadStrategy?: LoadStrategy;

  /** 设备类型 */
  deviceType?: DeviceType;

  /** 模型格式 */
  modelFormat?: ModelFormat;

  /** 加载环境 */
  environment?: LoadEnvironment;

  /** 资源约束 */
  resourceConstraints?: ResourceConstraints;

  /** 性能优化选项 */
  performanceOptions?: PerformanceOptions;

  /** 安全选项 */
  securityOptions?: SecurityOptions;

  /** 监控选项 */
  monitoringOptions?: MonitoringOptions;

  /** 依赖管理选项 */
  dependencyOptions?: DependencyOptions;

  /** 加载进度回调 */
  onProgress?: (progress: number, stage?: string, details?: any) => void;

  /** 加载完成回调 */
  onComplete?: (success: boolean, model?: any, stats?: any) => void;

  /** 加载错误回调 */
  onError?: (error: Error, context?: any) => void;

  /** 阶段变更回调 */
  onStageChange?: (stage: string, progress: number) => void;

  /** 资源使用回调 */
  onResourceUsage?: (usage: ResourceUsage) => void;

  /** 是否使用缓存 */
  useCache?: boolean;

  /** 缓存键 */
  cacheKey?: string;

  /** 缓存过期时间（毫秒） */
  cacheExpiry?: number;

  /** 是否使用离线模型 */
  useOfflineModel?: boolean;

  /** 离线模型路径 */
  offlineModelPath?: string;

  /** 是否使用低精度模型 */
  useLowPrecision?: boolean;

  /** 是否使用模型工厂 */
  useFactory?: boolean;

  /** 模型版本 */
  modelVersion?: string;

  /** 模型变体 */
  modelVariant?: string;

  /** 是否启用热重载 */
  enableHotReload?: boolean;

  /** 热重载检查间隔（毫秒） */
  hotReloadInterval?: number;

  /** 是否启用A/B测试 */
  enableABTesting?: boolean;

  /** A/B测试配置 */
  abTestConfig?: ABTestConfig;

  /** 回退选项 */
  fallbackOptions?: FallbackOptions;

  /** 自定义选项 */
  [key: string]: any;
}

/**
 * 加载选项验证器
 */
export class LoadOptionsValidator {
  /**
   * 验证加载选项
   */
  public static validate(options: AIModelLoadOptions): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证超时时间
    if (options.timeout !== undefined && options.timeout <= 0) {
      errors.push('超时时间必须大于0');
    }

    // 验证优先级
    if (options.priority !== undefined && (options.priority < 0 || options.priority > 10)) {
      errors.push('优先级必须在0-10之间');
    }

    // 验证资源约束
    if (options.resourceConstraints) {
      const constraints = options.resourceConstraints;
      if (constraints.maxMemory !== undefined && constraints.maxMemory <= 0) {
        errors.push('最大内存使用量必须大于0');
      }
      if (constraints.maxCpuUsage !== undefined && (constraints.maxCpuUsage < 0 || constraints.maxCpuUsage > 1)) {
        errors.push('最大CPU使用率必须在0-1之间');
      }
    }

    // 验证性能选项
    if (options.performanceOptions) {
      const perf = options.performanceOptions;
      if (perf.batchSize !== undefined && perf.batchSize <= 0) {
        errors.push('批处理大小必须大于0');
      }
      if (perf.parallelThreads !== undefined && perf.parallelThreads <= 0) {
        errors.push('并行线程数必须大于0');
      }
    }

    // 验证A/B测试配置
    if (options.abTestConfig) {
      const ab = options.abTestConfig;
      if (ab.trafficRatio < 0 || ab.trafficRatio > 1) {
        errors.push('流量分配比例必须在0-1之间');
      }
    }

    // 检查潜在的性能问题
    if (options.deviceType === DeviceType.CPU && options.performanceOptions?.enableParallelLoading) {
      warnings.push('在CPU设备上启用并行加载可能不会带来性能提升');
    }

    if (options.loadStrategy === LoadStrategy.EAGER && options.resourceConstraints?.maxMemory) {
      warnings.push('立即加载策略可能会超出内存限制');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

/**
 * 加载选项构建器
 */
export class LoadOptionsBuilder {
  private options: AIModelLoadOptions = {};

  /**
   * 设置基本选项
   */
  public basic(config: {
    forceReload?: boolean;
    timeout?: number;
    priority?: number;
    useCache?: boolean;
  }): LoadOptionsBuilder {
    Object.assign(this.options, config);
    return this;
  }

  /**
   * 设置设备和性能选项
   */
  public performance(config: {
    deviceType?: DeviceType;
    loadStrategy?: LoadStrategy;
    performanceOptions?: PerformanceOptions;
  }): LoadOptionsBuilder {
    Object.assign(this.options, config);
    return this;
  }

  /**
   * 设置资源约束
   */
  public resources(constraints: ResourceConstraints): LoadOptionsBuilder {
    this.options.resourceConstraints = constraints;
    return this;
  }

  /**
   * 设置安全选项
   */
  public security(security: SecurityOptions): LoadOptionsBuilder {
    this.options.securityOptions = security;
    return this;
  }

  /**
   * 设置监控选项
   */
  public monitoring(monitoring: MonitoringOptions): LoadOptionsBuilder {
    this.options.monitoringOptions = monitoring;
    return this;
  }

  /**
   * 设置回调函数
   */
  public callbacks(config: {
    onProgress?: (progress: number, stage?: string, details?: any) => void;
    onComplete?: (success: boolean, model?: any, stats?: any) => void;
    onError?: (error: Error, context?: any) => void;
    onStageChange?: (stage: string, progress: number) => void;
    onResourceUsage?: (usage: ResourceUsage) => void;
  }): LoadOptionsBuilder {
    Object.assign(this.options, config);
    return this;
  }

  /**
   * 设置回退选项
   */
  public fallback(fallback: FallbackOptions): LoadOptionsBuilder {
    this.options.fallbackOptions = fallback;
    return this;
  }

  /**
   * 构建选项
   */
  public build(): AIModelLoadOptions {
    // 验证选项
    const validation = LoadOptionsValidator.validate(this.options);
    if (!validation.valid) {
      throw new Error(`加载选项验证失败: ${validation.errors.join(', ')}`);
    }

    // 输出警告
    if (validation.warnings.length > 0) {
      console.warn('加载选项警告:', validation.warnings.join(', '));
    }

    return { ...this.options };
  }
}

/**
 * 预设加载选项
 */
export class LoadOptionsPresets {
  /**
   * 开发环境预设
   */
  public static development(): AIModelLoadOptions {
    return new LoadOptionsBuilder()
      .basic({
        timeout: 60000,
        priority: 5,
        useCache: true
      })
      .performance({
        deviceType: DeviceType.AUTO,
        loadStrategy: LoadStrategy.LAZY
      })
      .monitoring({
        enablePerformanceMonitoring: true,
        enableDetailedLogging: true,
        logLevel: 'debug'
      })
      .build();
  }

  /**
   * 生产环境预设
   */
  public static production(): AIModelLoadOptions {
    return new LoadOptionsBuilder()
      .basic({
        timeout: 30000,
        priority: 8,
        useCache: true
      })
      .performance({
        deviceType: DeviceType.GPU,
        loadStrategy: LoadStrategy.PRELOAD,
        performanceOptions: {
          enableParallelLoading: true,
          enableMemoryMapping: true
        }
      })
      .security({
        verifySignature: true,
        checkIntegrity: true,
        enableSandbox: true
      })
      .monitoring({
        enablePerformanceMonitoring: true,
        enableErrorMonitoring: true,
        logLevel: 'warn'
      })
      .fallback({
        enabled: true,
        strategy: 'sequential',
        maxRetries: 3
      })
      .build();
  }

  /**
   * 边缘计算预设
   */
  public static edge(): AIModelLoadOptions {
    const options = new LoadOptionsBuilder()
      .basic({
        timeout: 15000,
        priority: 9,
        useCache: true
      })
      .performance({
        deviceType: DeviceType.CPU,
        loadStrategy: LoadStrategy.ON_DEMAND,
        performanceOptions: {
          quantizationType: QuantizationType.DYNAMIC,
          quantizationBits: 8
        }
      })
      .resources({
        maxMemory: 512 * 1024 * 1024, // 512MB
        maxCpuUsage: 0.8
      })
      .monitoring({
        enablePerformanceMonitoring: true,
        logLevel: 'error'
      })
      .build();

    // 添加边缘计算特定选项
    options.useLowPrecision = true;
    options.useOfflineModel = true;

    return options;
  }

  /**
   * 高性能预设
   */
  public static highPerformance(): AIModelLoadOptions {
    return new LoadOptionsBuilder()
      .basic({
        timeout: 120000,
        priority: 10,
        useCache: true
      })
      .performance({
        deviceType: DeviceType.GPU,
        loadStrategy: LoadStrategy.EAGER,
        performanceOptions: {
          enableParallelLoading: true,
          parallelThreads: 8,
          enableMemoryMapping: true,
          enablePrefetch: true,
          batchSize: 64
        }
      })
      .resources({
        maxMemory: 8 * 1024 * 1024 * 1024, // 8GB
        maxGpuMemory: 4 * 1024 * 1024 * 1024 // 4GB
      })
      .monitoring({
        enablePerformanceMonitoring: true,
        enableMemoryMonitoring: true,
        monitoringInterval: 1000
      })
      .build();
  }
}
