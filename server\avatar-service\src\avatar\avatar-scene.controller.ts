/**
 * 虚拟化身场景控制器
 * 
 * 提供虚拟化身场景管理相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AvatarSceneService } from './avatar-scene.service';

/**
 * 场景加载请求DTO
 */
export class LoadSceneDto {
  /** 场景ID */
  sceneId: string;

  /** 虚拟化身ID */
  avatarId: string;

  /** 生成位置 */
  spawnPosition?: {
    x: number;
    y: number;
    z: number;
  };

  /** 生成旋转 */
  spawnRotation?: {
    x: number;
    y: number;
    z: number;
  };

  /** 初始动画 */
  initialAnimation?: string;

  /** 加载选项 */
  loadOptions?: {
    preloadAssets?: boolean;
    enablePhysics?: boolean;
    enableAI?: boolean;
    enableVoice?: boolean;
  };
}

/**
 * 虚拟化身保存请求DTO
 */
export class SaveAvatarDto {
  /** 虚拟化身ID */
  avatarId: string;

  /** 保存格式 */
  format?: 'json' | 'binary' | 'gltf' | 'fbx';

  /** 保存位置 */
  location?: 'filesystem' | 'database' | 'cloud';

  /** 压缩选项 */
  compression?: {
    enabled: boolean;
    level: number;
    algorithm: 'gzip' | 'brotli' | 'lz4';
  };

  /** 加密选项 */
  encryption?: {
    enabled: boolean;
    algorithm: 'aes-256' | 'rsa';
    key?: string;
  };
}

/**
 * 虚拟化身控制请求DTO
 */
export class ControlAvatarDto {
  /** 虚拟化身ID */
  avatarId: string;

  /** 控制类型 */
  controlType: 'move' | 'rotate' | 'teleport' | 'animate';

  /** 控制参数 */
  parameters: {
    position?: { x: number; y: number; z: number };
    rotation?: { x: number; y: number; z: number };
    direction?: { x: number; y: number; z: number };
    speed?: number;
    animation?: string;
  };
}

@ApiTags('虚拟化身场景管理')
@Controller('avatar-scene')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AvatarSceneController {
  constructor(private readonly avatarSceneService: AvatarSceneService) {}

  /**
   * 获取可用场景列表
   */
  @Get('scenes')
  @ApiOperation({ summary: '获取可用场景列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAvailableScenes() {
    try {
      return await this.avatarSceneService.getAvailableScenes();
    } catch (error) {
      throw new HttpException(
        `获取场景列表失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取场景信息
   */
  @Get('scenes/:sceneId')
  @ApiOperation({ summary: '获取场景信息' })
  @ApiParam({ name: 'sceneId', description: '场景ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSceneInfo(@Param('sceneId') sceneId: string) {
    try {
      return await this.avatarSceneService.getSceneInfo(sceneId);
    } catch (error) {
      throw new HttpException(
        `获取场景信息失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 加载虚拟化身到场景
   */
  @Post('load')
  @ApiOperation({ summary: '加载虚拟化身到场景' })
  @ApiResponse({ status: 201, description: '加载成功' })
  async loadAvatarToScene(@Body() loadSceneDto: LoadSceneDto) {
    try {
      return await this.avatarSceneService.loadAvatarToScene(loadSceneDto);
    } catch (error) {
      throw new HttpException(
        `加载场景失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 卸载场景
   */
  @Delete('scenes/:sceneId')
  @ApiOperation({ summary: '卸载场景' })
  @ApiParam({ name: 'sceneId', description: '场景ID' })
  @ApiResponse({ status: 200, description: '卸载成功' })
  async unloadScene(@Param('sceneId') sceneId: string) {
    try {
      return await this.avatarSceneService.unloadScene(sceneId);
    } catch (error) {
      throw new HttpException(
        `卸载场景失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 切换场景
   */
  @Post('switch')
  @ApiOperation({ summary: '切换场景' })
  @ApiResponse({ status: 200, description: '切换成功' })
  async switchScene(@Body() body: {
    fromSceneId: string;
    toSceneId: string;
    avatarId: string;
  }) {
    try {
      return await this.avatarSceneService.switchScene(
        body.fromSceneId,
        body.toSceneId,
        body.avatarId
      );
    } catch (error) {
      throw new HttpException(
        `切换场景失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 保存虚拟化身
   */
  @Post('save')
  @ApiOperation({ summary: '保存虚拟化身' })
  @ApiResponse({ status: 201, description: '保存成功' })
  async saveAvatar(@Body() saveAvatarDto: SaveAvatarDto) {
    try {
      return await this.avatarSceneService.saveAvatar(saveAvatarDto);
    } catch (error) {
      throw new HttpException(
        `保存虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 加载保存的虚拟化身
   */
  @Get('saved/:saveId')
  @ApiOperation({ summary: '加载保存的虚拟化身' })
  @ApiParam({ name: 'saveId', description: '保存ID' })
  @ApiResponse({ status: 200, description: '加载成功' })
  async loadSavedAvatar(@Param('saveId') saveId: string) {
    try {
      return await this.avatarSceneService.loadSavedAvatar(saveId);
    } catch (error) {
      throw new HttpException(
        `加载虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除保存的虚拟化身
   */
  @Delete('saved/:saveId')
  @ApiOperation({ summary: '删除保存的虚拟化身' })
  @ApiParam({ name: 'saveId', description: '保存ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async deleteSavedAvatar(@Param('saveId') saveId: string) {
    try {
      return await this.avatarSceneService.deleteSavedAvatar(saveId);
    } catch (error) {
      throw new HttpException(
        `删除虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 控制虚拟化身
   */
  @Post('control')
  @ApiOperation({ summary: '控制虚拟化身' })
  @ApiResponse({ status: 200, description: '控制成功' })
  async controlAvatar(@Body() controlAvatarDto: ControlAvatarDto) {
    try {
      return await this.avatarSceneService.controlAvatar(controlAvatarDto);
    } catch (error) {
      throw new HttpException(
        `控制虚拟化身失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取虚拟化身状态
   */
  @Get('status/:avatarId')
  @ApiOperation({ summary: '获取虚拟化身状态' })
  @ApiParam({ name: 'avatarId', description: '虚拟化身ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAvatarStatus(@Param('avatarId') avatarId: string) {
    try {
      return await this.avatarSceneService.getAvatarStatus(avatarId);
    } catch (error) {
      throw new HttpException(
        `获取虚拟化身状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取保存统计信息
   */
  @Get('statistics/saves')
  @ApiOperation({ summary: '获取保存统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSaveStatistics() {
    try {
      return await this.avatarSceneService.getSaveStatistics();
    } catch (error) {
      throw new HttpException(
        `获取保存统计失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取场景加载统计信息
   */
  @Get('statistics/loads')
  @ApiOperation({ summary: '获取场景加载统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLoadStatistics() {
    try {
      return await this.avatarSceneService.getLoadStatistics();
    } catch (error) {
      throw new HttpException(
        `获取加载统计失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
