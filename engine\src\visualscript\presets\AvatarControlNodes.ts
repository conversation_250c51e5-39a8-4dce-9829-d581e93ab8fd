/**
 * 虚拟化身控制相关的视觉脚本节点
 * 
 * 提供虚拟化身移动、旋转、交互等控制功能的节点实现
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeSlot, SlotType } from '../NodeSlot';
import { AvatarControlSystem, MovementMode, ControlConfig, InputMapping } from '../../avatar/AvatarControlSystem';
import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';

/**
 * 添加受控虚拟化身节点
 */
export class AddControlledAvatarNode extends VisualScriptNode {
  constructor() {
    super('AddControlledAvatar', '添加受控虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));
    this.addInputSlot(new NodeSlot('avatarEntity', '虚拟化身实体', SlotType.Object));
    this.addInputSlot(new NodeSlot('moveSpeed', '移动速度', SlotType.Number, 5.0));
    this.addInputSlot(new NodeSlot('rotationSpeed', '旋转速度', SlotType.Number, 2.0));
    this.addInputSlot(new NodeSlot('jumpForce', '跳跃力度', SlotType.Number, 10.0));
    this.addInputSlot(new NodeSlot('enableGravity', '启用重力', SlotType.Boolean, true));
    this.addInputSlot(new NodeSlot('enableCollision', '启用碰撞', SlotType.Boolean, true));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '添加成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;
    const avatarEntity = this.getInputValue('avatarEntity') as Entity;
    const moveSpeed = this.getInputValue('moveSpeed') as number;
    const rotationSpeed = this.getInputValue('rotationSpeed') as number;
    const jumpForce = this.getInputValue('jumpForce') as number;
    const enableGravity = this.getInputValue('enableGravity') as boolean;
    const enableCollision = this.getInputValue('enableCollision') as boolean;

    if (!avatarId || !avatarEntity) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID和实体不能为空');
    }

    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 构建控制配置
      const config: Partial<ControlConfig> = {
        moveSpeed,
        rotationSpeed,
        jumpForce,
        enableGravity,
        enableCollision,
        movementMode: MovementMode.WALK,
        enableSmoothMovement: true,
        smoothFactor: 0.1
      };

      // 添加受控虚拟化身
      controlSystem.addControlledAvatar(avatarId, avatarEntity, config);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return true;
    } catch (error) {
      console.error('添加受控虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 设置激活虚拟化身节点
 */
export class SetActiveAvatarNode extends VisualScriptNode {
  constructor() {
    super('SetActiveAvatar', '设置激活虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '设置成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 设置激活虚拟化身
      const success = controlSystem.setActiveAvatar(avatarId);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('设置激活虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 移动虚拟化身节点
 */
export class MoveAvatarControlNode extends VisualScriptNode {
  constructor() {
    super('MoveAvatarControl', '移动虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));
    this.addInputSlot(new NodeSlot('directionX', '方向X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('directionY', '方向Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('directionZ', '方向Z', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('speed', '移动速度', SlotType.Number, 5.0));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '移动成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;
    const directionX = this.getInputValue('directionX') as number;
    const directionY = this.getInputValue('directionY') as number;
    const directionZ = this.getInputValue('directionZ') as number;
    const speed = this.getInputValue('speed') as number;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 执行移动
      const direction = new Vector3(directionX, directionY, directionZ);
      const success = controlSystem.moveAvatar(avatarId, direction, speed);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('移动虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 旋转虚拟化身节点
 */
export class RotateAvatarControlNode extends VisualScriptNode {
  constructor() {
    super('RotateAvatarControl', '旋转虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));
    this.addInputSlot(new NodeSlot('rotationX', '旋转X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationY', '旋转Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationZ', '旋转Z', SlotType.Number, 0));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '旋转成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;
    const rotationX = this.getInputValue('rotationX') as number;
    const rotationY = this.getInputValue('rotationY') as number;
    const rotationZ = this.getInputValue('rotationZ') as number;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 执行旋转
      const rotation = new Vector3(rotationX, rotationY, rotationZ);
      const success = controlSystem.rotateAvatar(avatarId, rotation);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('旋转虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 传送虚拟化身节点
 */
export class TeleportAvatarNode extends VisualScriptNode {
  constructor() {
    super('TeleportAvatar', '传送虚拟化身', '虚拟化身');

    // 输入插槽
    this.addInputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));
    this.addInputSlot(new NodeSlot('positionX', '位置X', SlotType.Number));
    this.addInputSlot(new NodeSlot('positionY', '位置Y', SlotType.Number));
    this.addInputSlot(new NodeSlot('positionZ', '位置Z', SlotType.Number));
    this.addInputSlot(new NodeSlot('rotationX', '旋转X', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationY', '旋转Y', SlotType.Number, 0));
    this.addInputSlot(new NodeSlot('rotationZ', '旋转Z', SlotType.Number, 0));

    // 输出插槽
    this.addOutputSlot(new NodeSlot('success', '传送成功', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onError', '错误', SlotType.Execution));
  }

  protected executeImpl(): any {
    const avatarId = this.getInputValue('avatarId') as string;
    const positionX = this.getInputValue('positionX') as number;
    const positionY = this.getInputValue('positionY') as number;
    const positionZ = this.getInputValue('positionZ') as number;
    const rotationX = this.getInputValue('rotationX') as number;
    const rotationY = this.getInputValue('rotationY') as number;
    const rotationZ = this.getInputValue('rotationZ') as number;

    if (!avatarId) {
      this.triggerOutput('onError');
      throw new Error('虚拟化身ID不能为空');
    }

    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 执行传送
      const position = new Vector3(positionX, positionY, positionZ);
      const rotation = new Vector3(rotationX, rotationY, rotationZ);
      const success = controlSystem.teleportAvatar(avatarId, position, rotation);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return success;
    } catch (error) {
      console.error('传送虚拟化身失败:', error);
      this.setOutputValue('success', false);
      this.triggerOutput('onError');
      throw error;
    }
  }
}

/**
 * 获取激活虚拟化身节点
 */
export class GetActiveAvatarNode extends VisualScriptNode {
  constructor() {
    super('GetActiveAvatar', '获取激活虚拟化身', '虚拟化身');

    // 输出插槽
    this.addOutputSlot(new NodeSlot('avatarId', '虚拟化身ID', SlotType.String));
    this.addOutputSlot(new NodeSlot('hasActiveAvatar', '有激活虚拟化身', SlotType.Boolean));

    // 执行插槽
    this.addInputSlot(new NodeSlot('onExecute', '执行', SlotType.Execution));
    this.addOutputSlot(new NodeSlot('onComplete', '完成', SlotType.Execution));
  }

  protected executeImpl(): any {
    try {
      // 获取控制系统
      const controlSystem = this.context?.world?.getSystem('AvatarControlSystem') as AvatarControlSystem;
      if (!controlSystem) {
        throw new Error('虚拟化身控制系统未找到');
      }

      // 获取激活的虚拟化身ID
      const avatarId = controlSystem.getActiveAvatarId();

      // 设置输出值
      this.setOutputValue('avatarId', avatarId || '');
      this.setOutputValue('hasActiveAvatar', avatarId !== null);

      // 触发完成流程
      this.triggerOutput('onComplete');

      return avatarId;
    } catch (error) {
      console.error('获取激活虚拟化身失败:', error);
      throw error;
    }
  }
}

/**
 * 注册虚拟化身控制节点
 */
export function registerAvatarControlNodes(registry: any): void {
  registry.registerNode('AddControlledAvatarNode', AddControlledAvatarNode);
  registry.registerNode('SetActiveAvatarNode', SetActiveAvatarNode);
  registry.registerNode('MoveAvatarControlNode', MoveAvatarControlNode);
  registry.registerNode('RotateAvatarControlNode', RotateAvatarControlNode);
  registry.registerNode('TeleportAvatarNode', TeleportAvatarNode);
  registry.registerNode('GetActiveAvatarNode', GetActiveAvatarNode);
}
