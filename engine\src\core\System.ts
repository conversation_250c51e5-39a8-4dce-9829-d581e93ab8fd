/**
 * 系统基类
 * 负责处理特定类型的组件和功能
 */
import { Engine } from './Engine';
import { World } from './World';
import { Entity } from './Entity';
import { Component } from './Component';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 系统状态枚举
 */
export enum SystemState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 运行中 */
  RUNNING = 'running',
  /** 已暂停 */
  PAUSED = 'paused',
  /** 已销毁 */
  DISPOSED = 'disposed',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 系统性能统计
 */
export interface SystemPerformanceStats {
  /** 更新次数 */
  updateCount: number;
  /** 总更新时间（毫秒） */
  totalUpdateTime: number;
  /** 平均更新时间（毫秒） */
  averageUpdateTime: number;
  /** 最大更新时间（毫秒） */
  maxUpdateTime: number;
  /** 最小更新时间（毫秒） */
  minUpdateTime: number;
  /** 上次更新时间（毫秒） */
  lastUpdateTime: number;
  /** 处理的实体数量 */
  processedEntities: number;
}

/**
 * 系统配置选项
 */
export interface SystemOptions {
  /** 优先级 */
  priority?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用错误处理 */
  enableErrorHandling?: boolean;
  /** 系统依赖 */
  dependencies?: string[];
  /** 更新频率限制（毫秒） */
  updateFrequencyLimit?: number;
}

export abstract class System extends EventEmitter {
  /** 系统类型 */
  private type: string;

  /** 引擎引用 */
  protected engine: Engine | null = null;

  /** 世界引用 */
  protected world: World | null = null;

  /** 优先级（数字越小优先级越高） */
  private priority: number;

  /** 是否启用 */
  private enabled: boolean = true;

  /** 系统状态 */
  private state: SystemState = SystemState.UNINITIALIZED;

  /** 系统配置 */
  protected options: SystemOptions;

  /** 性能统计 */
  private performanceStats: SystemPerformanceStats;

  /** 系统依赖 */
  private dependencies: string[] = [];

  /** 上次更新时间戳 */
  private lastUpdateTimestamp: number = 0;

  /** 错误计数 */
  private errorCount: number = 0;

  /** 最大错误次数 */
  private maxErrors: number = 10;

  /**
   * 创建系统实例
   * @param priority 优先级（数字越小优先级越高）
   * @param options 系统配置选项
   */
  constructor(priority: number = 0, options: SystemOptions = {}) {
    super();
    this.type = this.constructor.name;
    this.priority = options.priority ?? priority;
    this.enabled = options.enabled ?? true;

    // 设置配置选项
    this.options = {
      priority: this.priority,
      enabled: this.enabled,
      enablePerformanceMonitoring: options.enablePerformanceMonitoring ?? false,
      enableErrorHandling: options.enableErrorHandling ?? true,
      dependencies: options.dependencies ?? [],
      updateFrequencyLimit: options.updateFrequencyLimit ?? 0,
      ...options
    };

    // 设置依赖
    this.dependencies = this.options.dependencies || [];

    // 初始化性能统计
    this.performanceStats = {
      updateCount: 0,
      totalUpdateTime: 0,
      averageUpdateTime: 0,
      maxUpdateTime: 0,
      minUpdateTime: Number.MAX_VALUE,
      lastUpdateTime: 0,
      processedEntities: 0
    };

    // 设置状态
    this.state = SystemState.UNINITIALIZED;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return this.type;
  }

  /**
   * 获取系统状态
   * @returns 系统状态
   */
  public getState(): SystemState {
    return this.state;
  }

  /**
   * 设置系统状态
   * @param state 新状态
   */
  protected setState(state: SystemState): void {
    const oldState = this.state;
    this.state = state;
    this.emit('stateChanged', { oldState, newState: state });
  }

  /**
   * 检查系统是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.state !== SystemState.UNINITIALIZED;
  }

  /**
   * 检查系统是否正在运行
   * @returns 是否正在运行
   */
  public isRunning(): boolean {
    return this.state === SystemState.RUNNING;
  }

  /**
   * 检查系统是否已暂停
   * @returns 是否已暂停
   */
  public isPaused(): boolean {
    return this.state === SystemState.PAUSED;
  }

  /**
   * 检查系统是否处于错误状态
   * @returns 是否处于错误状态
   */
  public isError(): boolean {
    return this.state === SystemState.ERROR;
  }

  /**
   * 获取系统依赖
   * @returns 系统依赖列表
   */
  public getDependencies(): string[] {
    return [...this.dependencies];
  }

  /**
   * 添加系统依赖
   * @param dependency 依赖的系统类型
   */
  public addDependency(dependency: string): void {
    if (!this.dependencies.includes(dependency)) {
      this.dependencies.push(dependency);
    }
  }

  /**
   * 移除系统依赖
   * @param dependency 依赖的系统类型
   */
  public removeDependency(dependency: string): void {
    const index = this.dependencies.indexOf(dependency);
    if (index !== -1) {
      this.dependencies.splice(index, 1);
    }
  }

  /**
   * 检查是否依赖某个系统
   * @param dependency 系统类型
   * @returns 是否依赖
   */
  public hasDependency(dependency: string): boolean {
    return this.dependencies.includes(dependency);
  }

  /**
   * 设置引擎引用
   * @param engine 引擎实例
   */
  public setEngine(engine: Engine): void {
    this.engine = engine;
  }

  /**
   * 获取引擎引用
   * @returns 引擎实例
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 设置世界引用
   * @param world 世界实例
   */
  public setWorld(world: World): void {
    this.world = world;
    this.engine = world.getEngine();
  }

  /**
   * 获取世界引用
   * @returns 世界实例
   */
  public getWorld(): World | null {
    return this.world;
  }

  /**
   * 获取优先级
   * @returns 优先级
   */
  public getPriority(): number {
    return this.priority;
  }

  /**
   * 设置优先级
   * @param priority 优先级
   */
  public setPriority(priority: number): void {
    this.priority = priority;
    this.options.priority = priority;
  }

  /**
   * 获取具有指定组件的所有实体
   * @param componentType 组件类型
   * @returns 实体数组
   */
  protected getEntitiesWithComponent(componentType: string): Entity[] {
    if (!this.world) {
      return [];
    }

    const entities: Entity[] = [];
    const allEntities = this.world.getAllEntities();

    for (const entity of allEntities) {
      if (entity.hasComponent(componentType)) {
        entities.push(entity);
      }
    }

    return entities;
  }

  /**
   * 获取具有指定多个组件的所有实体
   * @param componentTypes 组件类型数组
   * @returns 实体数组
   */
  protected getEntitiesWithComponents(componentTypes: string[]): Entity[] {
    if (!this.world || componentTypes.length === 0) {
      return [];
    }

    const entities: Entity[] = [];
    const allEntities = this.world.getAllEntities();

    for (const entity of allEntities) {
      const hasAllComponents = componentTypes.every(type => entity.hasComponent(type));
      if (hasAllComponents) {
        entities.push(entity);
      }
    }

    return entities;
  }

  /**
   * 查询实体
   * @param filter 过滤函数
   * @returns 符合条件的实体数组
   */
  protected queryEntities(filter: (entity: Entity) => boolean): Entity[] {
    if (!this.world) {
      return [];
    }

    const entities: Entity[] = [];
    const allEntities = this.world.getAllEntities();

    for (const entity of allEntities) {
      if (filter(entity)) {
        entities.push(entity);
      }
    }

    return entities;
  }

  /**
   * 批量处理实体
   * @param entities 实体数组
   * @param processor 处理函数
   * @param batchSize 批处理大小
   */
  protected processBatch(
    entities: Entity[],
    processor: (entity: Entity) => void,
    batchSize: number = 10
  ): void {
    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      for (const entity of batch) {
        try {
          processor(entity);
        } catch (error) {
          this.handleError(error as Error, `处理实体 ${entity.id} 时发生错误`);
        }
      }
    }
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): SystemPerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.performanceStats = {
      updateCount: 0,
      totalUpdateTime: 0,
      averageUpdateTime: 0,
      maxUpdateTime: 0,
      minUpdateTime: Number.MAX_VALUE,
      lastUpdateTime: 0,
      processedEntities: 0
    };
  }

  /**
   * 更新性能统计
   * @param updateTime 更新时间（毫秒）
   * @param processedEntities 处理的实体数量
   */
  private updatePerformanceStats(updateTime: number, processedEntities: number = 0): void {
    if (!this.options.enablePerformanceMonitoring) {
      return;
    }

    this.performanceStats.updateCount++;
    this.performanceStats.totalUpdateTime += updateTime;
    this.performanceStats.lastUpdateTime = updateTime;
    this.performanceStats.processedEntities += processedEntities;

    // 更新平均时间
    this.performanceStats.averageUpdateTime =
      this.performanceStats.totalUpdateTime / this.performanceStats.updateCount;

    // 更新最大时间
    if (updateTime > this.performanceStats.maxUpdateTime) {
      this.performanceStats.maxUpdateTime = updateTime;
    }

    // 更新最小时间
    if (updateTime < this.performanceStats.minUpdateTime) {
      this.performanceStats.minUpdateTime = updateTime;
    }
  }

  /**
   * 设置启用状态
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    if (this.enabled === enabled) {
      return;
    }

    this.enabled = enabled;
    this.options.enabled = enabled;

    if (enabled) {
      this.setState(SystemState.RUNNING);
      this.onEnable();
    } else {
      this.setState(SystemState.PAUSED);
      this.onDisable();
    }

    // 发出启用状态变更事件
    this.emit('enabledChanged', enabled);
  }

  /**
   * 暂停系统
   */
  public pause(): void {
    if (this.state === SystemState.RUNNING) {
      this.setState(SystemState.PAUSED);
      this.onPause();
      this.emit('paused');
    }
  }

  /**
   * 恢复系统
   */
  public resume(): void {
    if (this.state === SystemState.PAUSED) {
      this.setState(SystemState.RUNNING);
      this.onResume();
      this.emit('resumed');
    }
  }

  /**
   * 处理错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  protected handleError(error: Error, context: string = ''): void {
    if (!this.options.enableErrorHandling) {
      throw error;
    }

    this.errorCount++;

    // 发出错误事件
    this.emit('error', { error, context, count: this.errorCount });

    // 如果错误次数超过最大值，设置为错误状态
    if (this.errorCount >= this.maxErrors) {
      this.setState(SystemState.ERROR);
      this.emit('systemError', {
        message: `系统 ${this.type} 错误次数超过最大值 ${this.maxErrors}`,
        error,
        context
      });
    }

    // 记录错误
    console.error(`[${this.type}] ${context}:`, error);
  }

  /**
   * 重置错误计数
   */
  public resetErrorCount(): void {
    this.errorCount = 0;
    if (this.state === SystemState.ERROR) {
      this.setState(SystemState.RUNNING);
    }
  }

  /**
   * 获取错误计数
   * @returns 错误计数
   */
  public getErrorCount(): number {
    return this.errorCount;
  }

  /**
   * 设置最大错误次数
   * @param maxErrors 最大错误次数
   */
  public setMaxErrors(maxErrors: number): void {
    this.maxErrors = Math.max(1, maxErrors);
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.state !== SystemState.UNINITIALIZED) {
      return;
    }

    try {
      this.setState(SystemState.INITIALIZING);

      // 检查依赖
      this.checkDependencies();

      // 调用子类初始化方法
      this.onInitialize();

      // 设置为已初始化状态
      this.setState(SystemState.INITIALIZED);

      // 如果启用，则开始运行
      if (this.enabled) {
        this.setState(SystemState.RUNNING);
      }

      // 发出初始化完成事件
      this.emit('initialized');

    } catch (error) {
      this.setState(SystemState.ERROR);
      this.handleError(error as Error, '系统初始化失败');
    }
  }

  /**
   * 检查系统依赖
   */
  private checkDependencies(): void {
    if (!this.engine || this.dependencies.length === 0) {
      return;
    }

    for (const dependency of this.dependencies) {
      const dependentSystem = this.engine.getSystem(dependency);
      if (!dependentSystem) {
        throw new Error(`缺少依赖系统: ${dependency}`);
      }
      if (!dependentSystem.isInitialized()) {
        throw new Error(`依赖系统 ${dependency} 未初始化`);
      }
    }
  }

  /**
   * 子类可重写的初始化方法
   */
  protected onInitialize(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统启用时调用
   */
  protected onEnable(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统禁用时调用
   */
  protected onDisable(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统暂停时调用
   */
  protected onPause(): void {
    // 子类可以重写此方法
  }

  /**
   * 当系统恢复时调用
   */
  protected onResume(): void {
    // 子类可以重写此方法
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 检查系统状态
    if (!this.enabled || this.state !== SystemState.RUNNING) {
      return;
    }

    // 检查更新频率限制
    const currentTime = performance.now();
    if (this.options.updateFrequencyLimit &&
        this.options.updateFrequencyLimit > 0 &&
        currentTime - this.lastUpdateTimestamp < this.options.updateFrequencyLimit) {
      return;
    }

    const startTime = performance.now();
    let processedEntities = 0;

    try {
      // 调用子类更新方法
      processedEntities = this.onUpdate(deltaTime);

      // 更新时间戳
      this.lastUpdateTimestamp = currentTime;

    } catch (error) {
      this.handleError(error as Error, '系统更新失败');
    } finally {
      // 更新性能统计
      const endTime = performance.now();
      this.updatePerformanceStats(endTime - startTime, processedEntities);
    }
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    // 检查系统状态
    if (!this.enabled || this.state !== SystemState.RUNNING) {
      return;
    }

    const startTime = performance.now();
    let processedEntities = 0;

    try {
      // 调用子类固定更新方法
      processedEntities = this.onFixedUpdate(fixedDeltaTime);

    } catch (error) {
      this.handleError(error as Error, '系统固定更新失败');
    } finally {
      // 更新性能统计
      const endTime = performance.now();
      this.updatePerformanceStats(endTime - startTime, processedEntities);
    }
  }

  /**
   * 后更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public lateUpdate(deltaTime: number): void {
    // 检查系统状态
    if (!this.enabled || this.state !== SystemState.RUNNING) {
      return;
    }

    const startTime = performance.now();
    let processedEntities = 0;

    try {
      // 调用子类后更新方法
      processedEntities = this.onLateUpdate(deltaTime);

    } catch (error) {
      this.handleError(error as Error, '系统后更新失败');
    } finally {
      // 更新性能统计
      const endTime = performance.now();
      this.updatePerformanceStats(endTime - startTime, processedEntities);
    }
  }

  /**
   * 子类可重写的更新方法
   * @param deltaTime 帧间隔时间（秒）
   * @returns 处理的实体数量
   */
  protected onUpdate(deltaTime: number): number {
    // 子类可以重写此方法
    return 0;
  }

  /**
   * 子类可重写的固定更新方法
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   * @returns 处理的实体数量
   */
  protected onFixedUpdate(fixedDeltaTime: number): number {
    // 子类可以重写此方法
    return 0;
  }

  /**
   * 子类可重写的后更新方法
   * @param deltaTime 帧间隔时间（秒）
   * @returns 处理的实体数量
   */
  protected onLateUpdate(deltaTime: number): number {
    // 子类可以重写此方法
    return 0;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.state === SystemState.DISPOSED) {
      return;
    }

    try {
      // 设置为销毁状态
      this.setState(SystemState.DISPOSED);

      // 调用子类销毁方法
      this.onDispose();

      // 重置性能统计
      this.resetPerformanceStats();

      // 重置错误计数
      this.resetErrorCount();

      // 清除依赖
      this.dependencies.length = 0;

      // 清除引擎引用
      this.engine = null;

      // 清除世界引用
      this.world = null;

      // 移除所有事件监听器
      this.removeAllListeners();

      // 发出销毁事件
      this.emit('disposed');

    } catch (error) {
      console.error(`[${this.type}] 销毁系统时发生错误:`, error);
    }
  }

  /**
   * 子类可重写的销毁方法
   */
  protected onDispose(): void {
    // 子类可以重写此方法
  }

  /**
   * 获取系统配置
   * @returns 系统配置
   */
  public getOptions(): SystemOptions {
    return { ...this.options };
  }

  /**
   * 更新系统配置
   * @param options 新的配置选项
   */
  public updateOptions(options: Partial<SystemOptions>): void {
    this.options = { ...this.options, ...options };

    // 更新相关属性
    if (options.priority !== undefined) {
      this.priority = options.priority;
    }

    if (options.enabled !== undefined) {
      this.setEnabled(options.enabled);
    }

    // 发出配置更新事件
    this.emit('optionsUpdated', this.options);
  }

  /**
   * 获取系统信息
   * @returns 系统信息
   */
  public getSystemInfo(): {
    type: string;
    state: SystemState;
    enabled: boolean;
    priority: number;
    dependencies: string[];
    errorCount: number;
    performanceStats: SystemPerformanceStats;
    options: SystemOptions;
  } {
    return {
      type: this.type,
      state: this.state,
      enabled: this.enabled,
      priority: this.priority,
      dependencies: [...this.dependencies],
      errorCount: this.errorCount,
      performanceStats: this.getPerformanceStats(),
      options: this.getOptions()
    };
  }

  /**
   * 系统健康检查
   * @returns 健康状态
   */
  public healthCheck(): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查系统状态
    if (this.state === SystemState.ERROR) {
      issues.push('系统处于错误状态');
      recommendations.push('检查错误日志并重置错误计数');
    }

    // 检查错误计数
    if (this.errorCount > this.maxErrors * 0.8) {
      issues.push(`错误计数过高: ${this.errorCount}/${this.maxErrors}`);
      recommendations.push('检查系统逻辑并修复潜在问题');
    }

    // 检查性能
    if (this.performanceStats.averageUpdateTime > 16) { // 超过16ms可能影响60fps
      issues.push(`平均更新时间过长: ${this.performanceStats.averageUpdateTime.toFixed(2)}ms`);
      recommendations.push('优化系统更新逻辑以提高性能');
    }

    // 检查依赖
    if (this.engine) {
      for (const dependency of this.dependencies) {
        const dependentSystem = this.engine.getSystem(dependency);
        if (!dependentSystem || !dependentSystem.isInitialized()) {
          issues.push(`依赖系统 ${dependency} 不可用`);
          recommendations.push(`确保依赖系统 ${dependency} 已正确初始化`);
        }
      }
    }

    return {
      healthy: issues.length === 0,
      issues,
      recommendations
    };
  }
}
