/**
 * AI模型配置功能测试
 * 验证修复后的AIModelConfig功能是否正常工作
 */
import { 
  AIModelConfigManager,
  AIModelConfig,
  ConfigEnvironment,
  ConfigPriority,
  ConfigValidationResult
} from '../AIModelConfig';
import { AIModelType } from '../AIModelType';

/**
 * AI模型配置测试类
 */
export class AIModelConfigTest {
  private configManager: AIModelConfigManager;

  constructor() {
    // 创建配置管理器实例
    this.configManager = new AIModelConfigManager({
      enableValidation: true,
      enableHistory: true,
      enableAutoSave: false,
      maxHistoryCount: 10,
      debug: true
    });
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('=== AI模型配置功能测试开始 ===\n');

    try {
      this.testBasicOperations();
      this.testConfigValidation();
      this.testConfigPresets();
      this.testConfigTemplates();
      this.testConfigMerging();
      this.testConfigHistory();
      this.testConfigImportExport();
      this.testConfigCloning();

      console.log('\n=== 所有测试完成 ===');
    } catch (error) {
      console.error('测试过程中发生错误:', error);
    } finally {
      this.configManager.dispose();
    }
  }

  /**
   * 测试基本操作
   */
  private testBasicOperations(): void {
    console.log('1. 测试基本操作');

    // 创建配置
    const config = this.configManager.createConfig({
      name: '测试配置',
      description: '这是一个测试配置',
      modelType: AIModelType.GPT,
      temperature: 0.7,
      maxTokens: 2048,
      environment: ConfigEnvironment.DEVELOPMENT,
      priority: ConfigPriority.MEDIUM
    });

    console.log(`创建配置: ${config.id} - ${config.name}`);

    // 获取配置
    const retrieved = this.configManager.getConfig(config.id!);
    console.log(`获取配置: ${retrieved?.name}`);

    // 更新配置
    const updated = this.configManager.updateConfig(config.id!, {
      temperature: 0.8,
      description: '更新后的描述'
    });
    console.log(`更新配置: 温度 ${updated.temperature}, 描述 ${updated.description}`);

    // 获取所有配置
    const allConfigs = this.configManager.getAllConfigs();
    console.log(`总配置数: ${allConfigs.length}`);

    console.log('---\n');
  }

  /**
   * 测试配置验证
   */
  private testConfigValidation(): void {
    console.log('2. 测试配置验证');

    // 有效配置
    const validConfig: AIModelConfig = {
      modelType: AIModelType.BERT,
      temperature: 0.5,
      maxTokens: 1024,
      batchSize: 32,
      quantizationBits: 16
    };

    const validResult = this.configManager.validateConfig(validConfig);
    console.log(`有效配置验证: ${validResult.valid ? '通过' : '失败'}`);

    // 无效配置
    const invalidConfig: AIModelConfig = {
      modelType: AIModelType.GPT,
      temperature: 1.5, // 超出范围
      maxTokens: -100, // 负数
      batchSize: 2000, // 超出范围
      quantizationBits: 12 as any // 不允许的值
    };

    const invalidResult = this.configManager.validateConfig(invalidConfig);
    console.log(`无效配置验证: ${invalidResult.valid ? '通过' : '失败'}`);
    if (!invalidResult.valid) {
      console.log('验证错误:');
      invalidResult.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('---\n');
  }

  /**
   * 测试配置预设
   */
  private testConfigPresets(): void {
    console.log('3. 测试配置预设');

    // 获取所有预设
    const presets = this.configManager.getAllPresets();
    console.log(`可用预设数量: ${presets.length}`);

    presets.forEach(preset => {
      console.log(`  - ${preset.name}: ${preset.description}`);
    });

    // 从预设创建配置
    const gptConfig = this.configManager.createFromPreset('gpt_default', {
      name: '基于GPT预设的配置',
      temperature: 0.9
    });

    console.log(`从预设创建配置: ${gptConfig.name}, 温度: ${gptConfig.temperature}`);

    // 获取特定预设
    const preset = this.configManager.getPreset('high_performance');
    if (preset) {
      console.log(`高性能预设: ${preset.name}`);
      console.log(`  标签: ${preset.tags.join(', ')}`);
    }

    console.log('---\n');
  }

  /**
   * 测试配置模板
   */
  private testConfigTemplates(): void {
    console.log('4. 测试配置模板');

    // 获取所有模板
    const templates = this.configManager.getAllTemplates();
    console.log(`可用模板数量: ${templates.length}`);

    templates.forEach(template => {
      console.log(`  - ${template.name}: ${template.description}`);
      console.log(`    必需字段: ${template.requiredFields.join(', ')}`);
    });

    // 从模板创建配置
    try {
      const apiConfig = this.configManager.createFromTemplate('api', {
        name: '基于API模板的配置',
        modelType: AIModelType.GPT,
        apiKey: 'test-api-key',
        baseUrl: 'https://api.example.com',
        temperature: 0.7
      });

      console.log(`从模板创建配置: ${apiConfig.name}`);
      console.log(`  API密钥: ${apiConfig.apiKey}`);
      console.log(`  基础URL: ${apiConfig.baseUrl}`);
    } catch (error) {
      console.log(`模板创建失败: ${error}`);
    }

    console.log('---\n');
  }

  /**
   * 测试配置合并
   */
  private testConfigMerging(): void {
    console.log('5. 测试配置合并');

    const baseConfig: AIModelConfig = {
      modelType: AIModelType.BERT,
      temperature: 0.5,
      performance: {
        timeout: 10000,
        retryCount: 2
      },
      tags: ['base', 'bert']
    };

    const override1: Partial<AIModelConfig> = {
      temperature: 0.7,
      performance: {
        maxConcurrency: 5
      },
      tags: ['override1']
    };

    const override2: Partial<AIModelConfig> = {
      maxTokens: 1024,
      performance: {
        memoryLimit: 2048
      },
      tags: ['override2']
    };

    const merged = this.configManager.mergeConfigs(baseConfig, override1, override2);

    console.log('合并结果:');
    console.log(`  温度: ${merged.temperature}`);
    console.log(`  最大令牌: ${merged.maxTokens}`);
    console.log(`  性能配置:`, merged.performance);
    console.log(`  标签: ${merged.tags?.join(', ')}`);

    console.log('---\n');
  }

  /**
   * 测试配置历史
   */
  private testConfigHistory(): void {
    console.log('6. 测试配置历史');

    // 创建配置
    const config = this.configManager.createConfig({
      name: '历史测试配置',
      modelType: AIModelType.GPT,
      temperature: 0.5
    });

    // 多次更新配置
    this.configManager.updateConfig(config.id!, { temperature: 0.6 });
    this.configManager.updateConfig(config.id!, { temperature: 0.7 });
    this.configManager.updateConfig(config.id!, { maxTokens: 2048 });

    // 获取历史记录
    const history = this.configManager.getConfigHistory(config.id!);
    console.log(`配置历史记录数量: ${history.length}`);

    history.forEach((record, index) => {
      console.log(`  ${index + 1}. ${record.changeDescription} (${new Date(record.timestamp).toLocaleString()})`);
    });

    console.log('---\n');
  }

  /**
   * 测试配置导入导出
   */
  private testConfigImportExport(): void {
    console.log('7. 测试配置导入导出');

    // 创建配置
    const config = this.configManager.createConfig({
      name: '导出测试配置',
      modelType: AIModelType.STABLE_DIFFUSION,
      temperature: 0.8,
      maxTokens: 1024,
      useGPU: true
    });

    // 导出配置
    const exported = this.configManager.exportConfig(config.id!);
    console.log('导出的配置长度:', exported.length);

    // 导入配置
    const imported = this.configManager.importConfig(exported);
    console.log(`导入配置: ${imported.name} (新ID: ${imported.id})`);

    console.log('---\n');
  }

  /**
   * 测试配置克隆
   */
  private testConfigCloning(): void {
    console.log('8. 测试配置克隆');

    // 创建原始配置
    const original = this.configManager.createConfig({
      name: '原始配置',
      modelType: AIModelType.BERT,
      temperature: 0.6,
      batchSize: 16,
      tags: ['original', 'bert']
    });

    // 克隆配置
    const cloned = this.configManager.cloneConfig(original.id!, '克隆配置');

    console.log(`原始配置: ${original.id} - ${original.name}`);
    console.log(`克隆配置: ${cloned.id} - ${cloned.name}`);
    console.log(`配置相同性检查:`);
    console.log(`  模型类型: ${original.modelType === cloned.modelType}`);
    console.log(`  温度: ${original.temperature === cloned.temperature}`);
    console.log(`  批处理大小: ${original.batchSize === cloned.batchSize}`);
    console.log(`  ID不同: ${original.id !== cloned.id}`);

    console.log('---\n');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new AIModelConfigTest();
  test.runAllTests().catch(console.error);
}
