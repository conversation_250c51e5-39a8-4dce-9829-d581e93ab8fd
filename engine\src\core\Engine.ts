/**
 * 引擎核心类
 * 负责管理世界、系统和渲染循环
 */
import { World } from './World';
import { System } from './System';
import { Time } from '../utils/Time';
import { EventEmitter } from '../utils/EventEmitter';
import { RenderSystem } from '../rendering/RenderSystem';
import { Renderer } from '../rendering/Renderer';
import { Camera } from '../rendering/Camera';
import { AssetManager } from '../assets/AssetManager';
import { I18n } from '../i18n/I18n';

import { PerformanceMonitor } from '../utils/PerformanceMonitor';
import { MemoryAnalyzer } from '../utils/MemoryAnalyzer';
import { Logger } from '../utils/Logger';
import { EnhancedWorkerManager } from '../workers/EnhancedWorkerManager';

/**
 * 引擎状态枚举
 */
export enum EngineState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 运行中 */
  RUNNING = 'running',
  /** 已暂停 */
  PAUSED = 'paused',
  /** 已停止 */
  STOPPED = 'stopped',
  /** 已销毁 */
  DISPOSED = 'disposed',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 引擎性能统计
 */
export interface EnginePerformanceStats {
  /** 帧率 */
  fps: number;
  /** 平均帧时间（毫秒） */
  averageFrameTime: number;
  /** 最大帧时间（毫秒） */
  maxFrameTime: number;
  /** 最小帧时间（毫秒） */
  minFrameTime: number;
  /** 总帧数 */
  totalFrames: number;
  /** 运行时间（秒） */
  runTime: number;
  /** 内存使用情况 */
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  /** 系统统计 */
  systemStats: {
    totalSystems: number;
    activeSystems: number;
    averageSystemUpdateTime: number;
  };
}

/**
 * 插件接口
 */
export interface EnginePlugin {
  /** 插件名称 */
  name: string;
  /** 插件版本 */
  version: string;
  /** 初始化插件 */
  initialize(engine: Engine): Promise<void> | void;
  /** 销毁插件 */
  dispose(): Promise<void> | void;
  /** 插件依赖 */
  dependencies?: string[];
}

export interface EngineOptions {
  /** 画布元素或ID */
  canvas?: HTMLCanvasElement | string;
  /** 是否自动开始渲染循环 */
  autoStart?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 默认语言 */
  language?: string;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 性能监控间隔（毫秒） */
  performanceMonitoringInterval?: number;
  /** 是否启用内存监控 */
  enableMemoryMonitoring?: boolean;
  /** 内存监控间隔（毫秒） */
  memoryMonitoringInterval?: number;
  /** 是否启用热重载 */
  enableHotReload?: boolean;
  /** 热重载端口 */
  hotReloadPort?: number;
  /** 是否启用多线程 */
  enableMultiThreading?: boolean;
  /** Worker数量 */
  workerCount?: number;
  /** 目标帧率 */
  targetFPS?: number;
  /** 是否启用垂直同步 */
  enableVSync?: boolean;
  /** 最大帧时间（毫秒） */
  maxFrameTime?: number;
  /** 是否自动注册核心系统 */
  autoRegisterCoreSystems?: boolean;
  /** 插件列表 */
  plugins?: EnginePlugin[];
}

export class Engine extends EventEmitter {
  /** 单例实例 */
  private static instance: Engine | null = null;

  /** 世界实例 */
  private world: World;

  /** 系统列表 */
  private systems: System[] = [];

  /** 渲染器 */
  private renderer: Renderer;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 资产管理器 */
  private assetManager: AssetManager;

  /** 国际化实例 */
  private i18n: I18n;

  /** 引擎状态 */
  private state: EngineState = EngineState.UNINITIALIZED;

  /** 是否处于调试模式 */
  private debug: boolean;

  /** 引擎配置 */
  private options: Required<EngineOptions>;

  /** 动画帧ID */
  private animationFrameId: number = 0;

  /** 上一帧时间戳 */
  private lastFrameTime: number = 0;

  /** 固定更新累积时间 */
  private fixedUpdateAccumulator: number = 0;

  /** 固定更新时间步长（秒） */
  private fixedUpdateTimeStep: number = 1 / 60;

  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor | null = null;

  /** 内存分析器 */
  private memoryAnalyzer: MemoryAnalyzer | null = null;

  /** 日志记录器 */
  private logger: Logger;

  /** Worker管理器 */
  private workerManager: EnhancedWorkerManager | null = null;

  /** 插件映射 */
  private plugins: Map<string, EnginePlugin> = new Map();

  /** 性能统计 */
  private performanceStats: EnginePerformanceStats;

  /** 帧时间历史 */
  private frameTimeHistory: number[] = [];

  /** 最大帧时间历史长度 */
  private readonly MAX_FRAME_HISTORY = 60;

  /** 错误计数 */
  private errorCount: number = 0;

  /** 最大错误次数 */
  private maxErrors: number = 10;

  /** 暂停前的状态 */
  private pausedFromState: EngineState | null = null;

  /** 热重载WebSocket */
  private hotReloadSocket: WebSocket | null = null;

  /** 默认配置 */
  private static readonly DEFAULT_OPTIONS: Required<EngineOptions> = {
    canvas: undefined as any,
    autoStart: false,
    debug: false,
    language: 'zh-CN',
    enablePerformanceMonitoring: false,
    performanceMonitoringInterval: 1000,
    enableMemoryMonitoring: false,
    memoryMonitoringInterval: 5000,
    enableHotReload: false,
    hotReloadPort: 8080,
    enableMultiThreading: false,
    workerCount: navigator.hardwareConcurrency || 4,
    targetFPS: 60,
    enableVSync: true,
    maxFrameTime: 33.33, // ~30fps minimum
    autoRegisterCoreSystems: true,
    plugins: []
  };

  /**
   * 获取引擎单例实例
   * @param options 引擎选项（仅在首次创建时使用）
   * @returns 引擎实例
   */
  public static getInstance(options?: EngineOptions): Engine {
    if (!Engine.instance) {
      Engine.instance = new Engine(options);
    }
    return Engine.instance;
  }

  /**
   * 销毁单例实例
   */
  public static destroyInstance(): void {
    if (Engine.instance) {
      Engine.instance.dispose();
      Engine.instance = null;
    }
  }

  /**
   * 创建引擎实例
   * @param options 引擎选项
   */
  constructor(options: EngineOptions = {}) {
    super();

    // 合并配置
    this.options = {
      ...Engine.DEFAULT_OPTIONS,
      ...options
    };

    // 设置调试模式
    this.debug = this.options.debug;

    // 创建日志记录器
    this.logger = new Logger(this.debug ? 'debug' : 'info');

    // 初始化性能统计
    this.performanceStats = {
      fps: 0,
      averageFrameTime: 0,
      maxFrameTime: 0,
      minFrameTime: Number.MAX_VALUE,
      totalFrames: 0,
      runTime: 0,
      memoryUsage: {
        used: 0,
        total: 0,
        percentage: 0
      },
      systemStats: {
        totalSystems: 0,
        activeSystems: 0,
        averageSystemUpdateTime: 0
      }
    };

    // 设置固定时间步长
    this.fixedUpdateTimeStep = 1 / this.options.targetFPS;

    // 创建世界
    this.world = new World(this);

    // 创建渲染器
    this.renderer = new Renderer({
      canvas: this.options.canvas
    });

    // 创建资产管理器
    this.assetManager = new AssetManager();

    // 创建国际化实例
    this.i18n = new I18n({
      language: this.options.language,
    });

    // 初始化性能监控
    if (this.options.enablePerformanceMonitoring) {
      this.performanceMonitor = PerformanceMonitor.getInstance();
    }

    // 初始化内存监控
    if (this.options.enableMemoryMonitoring) {
      this.memoryAnalyzer = MemoryAnalyzer.getInstance();
    }

    // 初始化Worker管理器
    if (this.options.enableMultiThreading) {
      this.workerManager = new EnhancedWorkerManager('default');
    }

    // 设置全局错误处理
    this.setupGlobalErrorHandling();

    // 如果设置了自动开始，则初始化并开始渲染循环
    if (this.options.autoStart) {
      this.initialize();
      this.start();
    }
  }

  /**
   * 设置全局错误处理
   */
  private setupGlobalErrorHandling(): void {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(new Error(`未处理的Promise拒绝: ${event.reason}`), 'global');
      event.preventDefault();
    });

    // 捕获全局错误
    window.addEventListener('error', (event) => {
      this.handleError(new Error(`全局错误: ${event.message}`), 'global');
    });
  }

  /**
   * 处理错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  private handleError(error: Error, context: string = ''): void {
    this.errorCount++;

    this.logger.error(`[${context}] ${error.message}`, error);

    // 发出错误事件
    this.emit('error', { error, context, count: this.errorCount });

    // 如果错误次数超过最大值，设置为错误状态
    if (this.errorCount >= this.maxErrors) {
      this.setState(EngineState.ERROR);
      this.emit('criticalError', {
        message: `引擎错误次数超过最大值 ${this.maxErrors}`,
        error,
        context
      });
    }
  }

  /**
   * 设置引擎状态
   * @param state 新状态
   */
  private setState(state: EngineState): void {
    const oldState = this.state;
    this.state = state;
    this.emit('stateChanged', { oldState, newState: state });
    this.logger.info(`引擎状态变更: ${oldState} -> ${state}`);
  }

  /**
   * 初始化引擎
   */
  public async initialize(): Promise<void> {
    if (this.state !== EngineState.UNINITIALIZED) {
      return;
    }

    try {
      this.setState(EngineState.INITIALIZING);

      // 初始化时间
      Time.initialize();

      // 初始化资产管理器
      await this.assetManager.initialize();

      // 初始化世界
      this.world.initialize();

      // 初始化性能监控
      if (this.performanceMonitor) {
        this.performanceMonitor.start();
      }

      // 初始化内存监控
      if (this.memoryAnalyzer) {
        this.memoryAnalyzer.start();
      }

      // 初始化Worker管理器
      if (this.workerManager) {
        // Worker管理器已在构造函数中初始化
      }

      // 初始化热重载
      if (this.options.enableHotReload) {
        this.initializeHotReload();
      }

      // 自动注册核心系统
      if (this.options.autoRegisterCoreSystems) {
        await this.registerCoreSystems();
      }

      // 初始化插件
      await this.initializePlugins();

      // 初始化所有系统
      for (const system of this.systems) {
        await system.initialize();
      }

      this.setState(EngineState.INITIALIZED);
      this.emit('initialized');
      this.logger.info('引擎初始化完成');

    } catch (error) {
      this.setState(EngineState.ERROR);
      this.handleError(error as Error, '引擎初始化失败');
      throw error;
    }
  }

  /**
   * 自动注册核心系统
   */
  private async registerCoreSystems(): Promise<void> {
    try {
      // 添加渲染系统
      this.addSystem(new RenderSystem(this.renderer));

      // 根据配置添加其他核心系统
      const { PhysicsSystem } = await import('../physics/PhysicsSystem');
      this.addSystem(new PhysicsSystem());

      const { AnimationSystem } = await import('../animation/AnimationSystem');
      this.addSystem(new AnimationSystem());

      // AudioSystem已在音频管理器中处理

      const { InputSystem } = await import('../input/InputSystem');
      this.addSystem(new InputSystem());

      // NLPSceneGenerator作为独立服务使用

      this.logger.info('核心系统注册完成');
    } catch (error) {
      this.logger.warn('部分核心系统注册失败', error);
    }
  }

  /**
   * 初始化插件
   */
  private async initializePlugins(): Promise<void> {
    for (const plugin of this.options.plugins) {
      try {
        // 检查插件依赖
        if (plugin.dependencies) {
          for (const dep of plugin.dependencies) {
            if (!this.plugins.has(dep)) {
              throw new Error(`插件 ${plugin.name} 依赖 ${dep} 未找到`);
            }
          }
        }

        await plugin.initialize(this);
        this.plugins.set(plugin.name, plugin);
        this.logger.info(`插件 ${plugin.name} v${plugin.version} 初始化完成`);
      } catch (error) {
        this.logger.error(`插件 ${plugin.name} 初始化失败`, error);
      }
    }
  }

  /**
   * 初始化热重载
   */
  private initializeHotReload(): void {
    try {
      const wsUrl = `ws://localhost:${this.options.hotReloadPort}`;
      this.hotReloadSocket = new WebSocket(wsUrl);

      this.hotReloadSocket.onopen = () => {
        this.logger.info(`热重载连接已建立: ${wsUrl}`);
      };

      this.hotReloadSocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleHotReload(data);
      };

      this.hotReloadSocket.onerror = (error) => {
        this.logger.warn('热重载连接失败', error);
      };
    } catch (error) {
      this.logger.warn('热重载初始化失败', error);
    }
  }

  /**
   * 处理热重载
   * @param data 热重载数据
   */
  private handleHotReload(data: any): void {
    try {
      switch (data.type) {
        case 'reload':
          this.emit('hotReload', data);
          break;
        case 'system-update':
          this.reloadSystem(data.systemType);
          break;
        case 'asset-update':
          this.assetManager.loadAsset(data.assetPath);
          break;
      }
    } catch (error) {
      this.logger.error('热重载处理失败', error);
    }
  }

  /**
   * 重新加载系统
   * @param systemType 系统类型
   */
  private reloadSystem(systemType: string): void {
    const system = this.getSystem(systemType);
    if (system) {
      this.removeSystem(system);
      // 这里需要重新创建系统实例
      this.logger.info(`系统 ${systemType} 已重新加载`);
    }
  }

  /**
   * 开始渲染循环
   */
  public start(): void {
    if (this.state === EngineState.RUNNING) {
      return;
    }

    // 确保引擎已初始化
    if (this.state === EngineState.UNINITIALIZED) {
      this.initialize();
    }

    this.setState(EngineState.RUNNING);
    this.lastFrameTime = performance.now();
    this.animationFrameId = requestAnimationFrame(this.update.bind(this));

    // 启动性能监控
    if (this.performanceMonitor) {
      this.performanceMonitor.start();
    }

    this.emit('started');
    this.logger.info('引擎已启动');
  }

  /**
   * 停止渲染循环
   */
  public stop(): void {
    if (this.state !== EngineState.RUNNING) {
      return;
    }

    this.setState(EngineState.STOPPED);
    cancelAnimationFrame(this.animationFrameId);

    // 停止性能监控
    if (this.performanceMonitor) {
      this.performanceMonitor.stop();
    }

    this.emit('stopped');
    this.logger.info('引擎已停止');
  }

  /**
   * 暂停引擎
   */
  public pause(): void {
    if (this.state === EngineState.RUNNING) {
      this.pausedFromState = this.state;
      this.setState(EngineState.PAUSED);
      cancelAnimationFrame(this.animationFrameId);
      this.emit('paused');
      this.logger.info('引擎已暂停');
    }
  }

  /**
   * 恢复引擎
   */
  public resume(): void {
    if (this.state === EngineState.PAUSED) {
      this.setState(this.pausedFromState || EngineState.RUNNING);
      this.pausedFromState = null;
      this.lastFrameTime = performance.now();
      this.animationFrameId = requestAnimationFrame(this.update.bind(this));
      this.emit('resumed');
      this.logger.info('引擎已恢复');
    }
  }

  /**
   * 更新循环
   * @param timestamp 当前时间戳
   */
  private update(timestamp: number): void {
    if (this.state !== EngineState.RUNNING) {
      return;
    }

    try {
      const frameStartTime = performance.now();

      // 计算帧间隔时间
      const deltaTime = Math.min((timestamp - this.lastFrameTime) / 1000, this.options.maxFrameTime / 1000);
      this.lastFrameTime = timestamp;

      // 限制最大帧时间，防止螺旋死亡
      const clampedDeltaTime = Math.min(deltaTime, this.options.maxFrameTime / 1000);

      // 更新时间
      Time.update(clampedDeltaTime);

      // 更新性能统计
      this.updatePerformanceStats(deltaTime);

      // 更新世界
      this.world.update(clampedDeltaTime);

      // 固定时间步长更新
      this.fixedUpdateAccumulator += clampedDeltaTime;
      while (this.fixedUpdateAccumulator >= this.fixedUpdateTimeStep) {
        // 固定更新世界
        this.world.fixedUpdate(this.fixedUpdateTimeStep);

        // 固定更新系统
        for (const system of this.systems) {
          if (system.isEnabled()) {
            system.fixedUpdate(this.fixedUpdateTimeStep);
          }
        }

        this.fixedUpdateAccumulator -= this.fixedUpdateTimeStep;
      }

      // 更新系统
      let totalSystemUpdateTime = 0;
      let activeSystems = 0;

      for (const system of this.systems) {
        if (system.isEnabled()) {
          const systemStartTime = performance.now();
          system.update(clampedDeltaTime);
          const systemEndTime = performance.now();
          totalSystemUpdateTime += systemEndTime - systemStartTime;
          activeSystems++;
        }
      }

      // 后更新系统
      for (const system of this.systems) {
        if (system.isEnabled()) {
          system.lateUpdate(clampedDeltaTime);
        }
      }

      // 更新系统统计
      this.performanceStats.systemStats = {
        totalSystems: this.systems.length,
        activeSystems,
        averageSystemUpdateTime: activeSystems > 0 ? totalSystemUpdateTime / activeSystems : 0
      };

      // 发出更新事件
      this.emit('update', clampedDeltaTime);

      // 记录帧时间
      const frameEndTime = performance.now();
      const frameTime = frameEndTime - frameStartTime;
      this.recordFrameTime(frameTime);

      // 继续渲染循环
      this.animationFrameId = requestAnimationFrame(this.update.bind(this));

    } catch (error) {
      this.handleError(error as Error, '更新循环');
    }
  }

  /**
   * 更新性能统计
   * @param deltaTime 帧间隔时间
   */
  private updatePerformanceStats(deltaTime: number): void {
    this.performanceStats.totalFrames++;
    this.performanceStats.runTime += deltaTime;

    // 计算FPS（每秒更新一次）
    if (this.performanceStats.totalFrames % 60 === 0) {
      this.performanceStats.fps = Math.round(1 / deltaTime);
    }

    // 更新内存使用情况
    if (this.memoryAnalyzer && (performance as any).memory) {
      const memoryInfo = (performance as any).memory;
      this.performanceStats.memoryUsage = {
        used: memoryInfo.usedJSHeapSize,
        total: memoryInfo.totalJSHeapSize,
        percentage: (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100
      };
    }
  }

  /**
   * 记录帧时间
   * @param frameTime 帧时间（毫秒）
   */
  private recordFrameTime(frameTime: number): void {
    this.frameTimeHistory.push(frameTime);

    // 保持历史记录长度
    if (this.frameTimeHistory.length > this.MAX_FRAME_HISTORY) {
      this.frameTimeHistory.shift();
    }

    // 更新统计
    this.performanceStats.averageFrameTime =
      this.frameTimeHistory.reduce((sum, time) => sum + time, 0) / this.frameTimeHistory.length;

    this.performanceStats.maxFrameTime = Math.max(this.performanceStats.maxFrameTime, frameTime);
    this.performanceStats.minFrameTime = Math.min(this.performanceStats.minFrameTime, frameTime);
  }

  /**
   * 添加系统
   * @param system 系统实例
   * @returns 添加的系统
   */
  public addSystem<T extends System>(system: T): T {
    // 检查系统是否已存在
    const existingSystem = this.getSystem(system.getType());
    if (existingSystem) {
      this.logger.warn(`系统 ${system.getType()} 已存在，将被替换`);
      this.removeSystem(existingSystem);
    }

    // 设置系统的引擎引用
    system.setEngine(this);
    system.setWorld(this.world);

    // 添加到系统列表
    this.systems.push(system);

    // 如果引擎已初始化，则初始化系统
    if (this.state !== EngineState.UNINITIALIZED) {
      system.initialize();
    }

    // 按优先级排序系统
    this.systems.sort((a, b) => a.getPriority() - b.getPriority());

    this.emit('systemAdded', system);
    this.logger.debug(`系统 ${system.getType()} 已添加`);

    return system;
  }

  /**
   * 获取系统
   * @param type 系统类型
   * @returns 系统实例，如果不存在则返回null
   */
  public getSystem<T extends System>(type: string): T | null {
    for (const system of this.systems) {
      if (system.getType() === type) {
        return system as T;
      }
    }

    return null;
  }

  /**
   * 移除系统
   * @param system 系统实例或类型
   * @returns 是否成功移除
   */
  public removeSystem(system: System | string): boolean {
    const index = typeof system === 'string'
      ? this.systems.findIndex(s => s.getType() === system)
      : this.systems.indexOf(system);

    if (index !== -1) {
      const removedSystem = this.systems[index];
      (removedSystem as any).dispose();
      this.systems.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * 获取世界
   * @returns 世界实例
   */
  public getWorld(): World {
    return this.world;
  }

  /**
   * 获取渲染器
   * @returns 渲染器实例
   */
  public getRenderer(): Renderer {
    return this.renderer;
  }

  /**
   * 设置活跃相机
   * @param camera 相机实例
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机实例
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 获取资产管理器
   * @returns 资产管理器实例
   */
  public getAssetManager(): AssetManager {
    return this.assetManager;
  }

  /**
   * 获取国际化实例
   * @returns 国际化实例
   */
  public getI18n(): I18n {
    return this.i18n;
  }

  /**
   * 是否处于调试模式
   * @returns 是否处于调试模式
   */
  public isDebug(): boolean {
    return this.debug;
  }

  /**
   * 设置调试模式
   * @param debug 是否启用调试模式
   */
  public setDebug(debug: boolean): void {
    this.debug = debug;
  }

  /**
   * 获取引擎状态
   * @returns 引擎状态
   */
  public getState(): EngineState {
    return this.state;
  }

  /**
   * 是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.state !== EngineState.UNINITIALIZED;
  }

  /**
   * 是否正在运行
   * @returns 是否正在运行
   */
  public isRunning(): boolean {
    return this.state === EngineState.RUNNING;
  }

  /**
   * 是否已暂停
   * @returns 是否已暂停
   */
  public isPaused(): boolean {
    return this.state === EngineState.PAUSED;
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): EnginePerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.performanceStats = {
      fps: 0,
      averageFrameTime: 0,
      maxFrameTime: 0,
      minFrameTime: Number.MAX_VALUE,
      totalFrames: 0,
      runTime: 0,
      memoryUsage: {
        used: 0,
        total: 0,
        percentage: 0
      },
      systemStats: {
        totalSystems: 0,
        activeSystems: 0,
        averageSystemUpdateTime: 0
      }
    };
    this.frameTimeHistory = [];
  }

  /**
   * 获取所有系统
   * @returns 系统列表
   */
  public getSystems(): System[] {
    return [...this.systems];
  }

  /**
   * 获取插件
   * @param name 插件名称
   * @returns 插件实例
   */
  public getPlugin(name: string): EnginePlugin | null {
    return this.plugins.get(name) || null;
  }

  /**
   * 获取所有插件
   * @returns 插件列表
   */
  public getPlugins(): EnginePlugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 添加插件
   * @param plugin 插件实例
   */
  public async addPlugin(plugin: EnginePlugin): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`插件 ${plugin.name} 已存在`);
    }

    await plugin.initialize(this);
    this.plugins.set(plugin.name, plugin);
    this.emit('pluginAdded', plugin);
    this.logger.info(`插件 ${plugin.name} v${plugin.version} 已添加`);
  }

  /**
   * 移除插件
   * @param name 插件名称
   */
  public async removePlugin(name: string): Promise<boolean> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      return false;
    }

    await plugin.dispose();
    this.plugins.delete(name);
    this.emit('pluginRemoved', plugin);
    this.logger.info(`插件 ${name} 已移除`);
    return true;
  }

  /**
   * 获取引擎配置
   * @returns 引擎配置
   */
  public getOptions(): Required<EngineOptions> {
    return { ...this.options };
  }

  /**
   * 更新引擎配置
   * @param options 新的配置选项
   */
  public updateOptions(options: Partial<EngineOptions>): void {
    this.options = { ...this.options, ...options };
    this.emit('optionsUpdated', this.options);
    this.logger.info('引擎配置已更新');
  }

  /**
   * 获取Worker管理器
   * @returns Worker管理器
   */
  public getWorkerManager(): EnhancedWorkerManager | null {
    return this.workerManager;
  }

  /**
   * 销毁引擎
   */
  public async dispose(): Promise<void> {
    if (this.state === EngineState.DISPOSED) {
      return;
    }

    try {
      this.setState(EngineState.DISPOSED);

      // 停止渲染循环
      this.stop();

      // 销毁所有插件
      for (const plugin of this.plugins.values()) {
        try {
          await plugin.dispose();
        } catch (error) {
          this.logger.error(`插件 ${plugin.name} 销毁失败`, error);
        }
      }
      this.plugins.clear();

      // 销毁所有系统
      for (const system of this.systems) {
        try {
          system.dispose();
        } catch (error) {
          this.logger.error(`系统 ${system.getType()} 销毁失败`, error);
        }
      }
      this.systems = [];

      // 停止监控
      if (this.performanceMonitor) {
        this.performanceMonitor.stop();
      }

      if (this.memoryAnalyzer) {
        this.memoryAnalyzer.stop();
      }

      // 销毁Worker管理器
      if (this.workerManager) {
        // Worker管理器清理
        this.workerManager = null;
      }

      // 关闭热重载连接
      if (this.hotReloadSocket) {
        this.hotReloadSocket.close();
      }

      // 销毁世界
      this.world.dispose();

      // 销毁渲染器
      this.renderer.dispose();

      // 销毁资产管理器
      this.assetManager.dispose();

      // 移除所有事件监听器
      this.removeAllListeners();

      // 清除单例引用
      if (Engine.instance === this) {
        Engine.instance = null;
      }

      this.emit('disposed');
      this.logger.info('引擎已销毁');

    } catch (error) {
      this.logger.error('引擎销毁失败', error);
      throw error;
    }
  }
}
